<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('puntos', function (Blueprint $table) {
            $table->integer('idpos')->primary();
            $table->string('nombre', 200);
            $table->date('fecha')->nullable();
            $table->time('hora')->nullable();
            $table->integer('efectiva')->default(0)->comment('0=no_efectiva, 1=efectiva');
            $table->text('direccion')->nullable();
            $table->text('encuestas_respondidas')->nullable();
            $table->integer('cantidad_visitas')->default(0);
            $table->double('precisiongps', 10, 6)->nullable();
            $table->double('latitud', 10, 6)->nullable();
            $table->double('longitud', 10, 6)->nullable();
            $table->integer('id_distri')->nullable()->comment('ID del agente/distribuidor');
            $table->timestamp('fecha_creacion')->nullable();
            $table->timestamp('fecha_actualizacion')->nullable();
            
            // Índices
            $table->index('nombre');
            $table->index('efectiva');
            $table->index('id_distri');
            $table->index('fecha');
            $table->index(['latitud', 'longitud']);
            
            // Foreign key
            $table->foreign('id_distri')->references('id')->on('usuarios')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('puntos');
    }
};
