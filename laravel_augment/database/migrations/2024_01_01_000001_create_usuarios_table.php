<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usuarios', function (Blueprint $table) {
            $table->id();
            $table->string('cedula', 20)->nullable();
            $table->string('nombre', 100);
            $table->string('apellido', 100);
            $table->string('user', 50)->unique();
            $table->string('pass');
            $table->string('perfil', 50)->nullable();
            $table->integer('id_perfil')->nullable();
            $table->integer('estado_pass')->default(1)->comment('1=primer_logueo, 0=activo');
            $table->timestamp('fecha_hora')->nullable();
            $table->integer('tolerancia_visita')->default(100)->comment('Tolerancia en metros');
            $table->string('email', 100)->nullable();
            $table->string('telefono', 20)->nullable();
            $table->string('token_notificacion')->nullable();
            $table->timestamp('ultimo_acceso')->nullable();
            $table->boolean('activo')->default(true);
            
            // Índices
            $table->index('user');
            $table->index('email');
            $table->index('cedula');
            $table->index('id_perfil');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usuarios');
    }
};
