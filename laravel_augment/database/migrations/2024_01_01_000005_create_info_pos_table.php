<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('info_pos', function (Blueprint $table) {
            $table->integer('idpos')->primary();
            $table->string('nombre', 200);
            $table->string('nombre_propietario', 200)->nullable();
            $table->string('circuito', 50)->nullable();
            $table->string('ruta', 50)->nullable();
            $table->text('direccion')->nullable();
            $table->string('telefono', 20)->nullable();
            $table->string('tel_op', 20)->nullable();
            $table->double('latitud', 10, 6)->nullable();
            $table->double('longitud', 10, 6)->nullable();
            $table->string('distribuidor', 100)->nullable();
            $table->timestamp('fecha_visita')->nullable();
            $table->string('categoria', 50)->nullable();
            $table->string('estado_comercial', 50)->nullable();
            $table->unsignedBigInteger('id_departamento')->nullable();
            $table->unsignedBigInteger('id_municipio')->nullable();
            $table->string('codigo_unico_tienda', 50)->nullable();
            $table->string('tipo_documento', 20)->nullable();
            $table->string('numero_documento', 50)->nullable();
            $table->string('email', 100)->nullable();
            $table->string('telefono_fijo', 20)->nullable();
            $table->string('celular', 20)->nullable();
            
            // Índices
            $table->index('nombre');
            $table->index('circuito');
            $table->index('ruta');
            $table->index('categoria');
            $table->index('estado_comercial');
            $table->index('id_departamento');
            $table->index('id_municipio');
            $table->index('codigo_unico_tienda');
            $table->index(['latitud', 'longitud']);
            
            // Foreign keys
            $table->foreign('idpos')->references('idpos')->on('puntos')->onDelete('cascade');
            $table->foreign('id_departamento')->references('id')->on('departamentos')->onDelete('set null');
            $table->foreign('id_municipio')->references('id')->on('municipios')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('info_pos');
    }
};
