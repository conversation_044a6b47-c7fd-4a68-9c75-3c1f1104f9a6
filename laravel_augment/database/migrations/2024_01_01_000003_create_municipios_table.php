<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('municipios', function (Blueprint $table) {
            $table->id();
            $table->string('descripcion', 100);
            $table->unsignedBigInteger('id_depto');
            $table->string('codigo', 10)->nullable();
            $table->boolean('activo')->default(true);
            
            // Índices y relaciones
            $table->index('descripcion');
            $table->index('id_depto');
            $table->index('codigo');
            
            // Foreign key
            $table->foreign('id_depto')->references('id')->on('departamentos')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('municipios');
    }
};
