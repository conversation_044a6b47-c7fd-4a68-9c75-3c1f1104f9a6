<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Departamento;
use App\Models\Municipio;
use App\Models\User;

class MasterDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Solo crear datos si no existen (para no duplicar en BD existente)
        
        // Crear departamentos de ejemplo para Uruguay
        if (Departamento::count() === 0) {
            $departamentos = [
                ['descripcion' => 'Montevideo', 'codigo' => 'MVD'],
                ['descripcion' => 'Canelones', 'codigo' => 'CAN'],
                ['descripcion' => 'Maldonado', 'codigo' => 'MAL'],
                ['descripcion' => 'Colonia', 'codigo' => 'COL'],
                ['descripcion' => 'San José', 'codigo' => 'SJ'],
                ['descripcion' => 'Paysandú', 'codigo' => 'PAY'],
                ['descripcion' => 'Salto', 'codigo' => 'SAL'],
                ['descripcion' => 'Rivera', 'codigo' => 'RIV'],
                ['descripcion' => 'Tacuarembó', 'codigo' => 'TAC'],
                ['descripcion' => 'Artigas', 'codigo' => 'ART'],
                ['descripcion' => 'Cerro Largo', 'codigo' => 'CL'],
                ['descripcion' => 'Treinta y Tres', 'codigo' => 'TT'],
                ['descripcion' => 'Lavalleja', 'codigo' => 'LAV'],
                ['descripcion' => 'Rocha', 'codigo' => 'ROC'],
                ['descripcion' => 'Flores', 'codigo' => 'FLO'],
                ['descripcion' => 'Florida', 'codigo' => 'FLA'],
                ['descripcion' => 'Durazno', 'codigo' => 'DUR'],
                ['descripcion' => 'Río Negro', 'codigo' => 'RN'],
                ['descripcion' => 'Soriano', 'codigo' => 'SOR']
            ];

            foreach ($departamentos as $depto) {
                Departamento::create($depto);
            }
        }

        // Crear algunos municipios de ejemplo
        if (Municipio::count() === 0) {
            $montevideo = Departamento::where('descripcion', 'Montevideo')->first();
            $canelones = Departamento::where('descripcion', 'Canelones')->first();
            
            if ($montevideo) {
                $municipiosMontevideo = [
                    ['descripcion' => 'Montevideo', 'id_depto' => $montevideo->id, 'codigo' => 'MVD01'],
                    ['descripcion' => 'Pocitos', 'id_depto' => $montevideo->id, 'codigo' => 'MVD02'],
                    ['descripcion' => 'Punta Carretas', 'id_depto' => $montevideo->id, 'codigo' => 'MVD03'],
                    ['descripcion' => 'Centro', 'id_depto' => $montevideo->id, 'codigo' => 'MVD04'],
                    ['descripcion' => 'Ciudad Vieja', 'id_depto' => $montevideo->id, 'codigo' => 'MVD05'],
                ];

                foreach ($municipiosMontevideo as $municipio) {
                    Municipio::create($municipio);
                }
            }

            if ($canelones) {
                $municipiosCanelones = [
                    ['descripcion' => 'Canelones', 'id_depto' => $canelones->id, 'codigo' => 'CAN01'],
                    ['descripcion' => 'Las Piedras', 'id_depto' => $canelones->id, 'codigo' => 'CAN02'],
                    ['descripcion' => 'Pando', 'id_depto' => $canelones->id, 'codigo' => 'CAN03'],
                    ['descripcion' => 'Santa Lucía', 'id_depto' => $canelones->id, 'codigo' => 'CAN04'],
                    ['descripcion' => 'Atlántida', 'id_depto' => $canelones->id, 'codigo' => 'CAN05'],
                ];

                foreach ($municipiosCanelones as $municipio) {
                    Municipio::create($municipio);
                }
            }
        }

        // Crear usuario administrador de ejemplo (solo si no existe)
        if (User::count() === 0) {
            User::create([
                'cedula' => '12345678',
                'nombre' => 'Administrador',
                'apellido' => 'Sistema',
                'user' => 'admin',
                'pass' => bcrypt('admin123'),
                'perfil' => 'Administrador',
                'id_perfil' => 1,
                'estado_pass' => 0, // Ya no es primer logueo
                'fecha_hora' => now(),
                'tolerancia_visita' => 100,
                'email' => '<EMAIL>',
                'activo' => true
            ]);

            // Usuario de prueba para operador
            User::create([
                'cedula' => '87654321',
                'nombre' => 'Operador',
                'apellido' => 'Prueba',
                'user' => 'operador',
                'pass' => bcrypt('operador123'),
                'perfil' => 'Operador',
                'id_perfil' => 2,
                'estado_pass' => 0,
                'fecha_hora' => now(),
                'tolerancia_visita' => 100,
                'email' => '<EMAIL>',
                'activo' => true
            ]);
        }

        $this->command->info('Datos maestros creados exitosamente (solo si no existían).');
    }
}
