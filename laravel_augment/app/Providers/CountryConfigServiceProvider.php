<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;

class CountryConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Registrar el servicio de configuración de país
        $this->app->singleton('country.config', function ($app) {
            $country = config('app.current_country', 'uruguay');
            return config("countries.{$country}");
        });

        // Registrar helper para obtener configuración actual
        $this->app->singleton('country.helper', function ($app) {
            return new class {
                public function getCurrentCountry(): string
                {
                    return config('app.current_country', 'uruguay');
                }

                public function getCountryConfig(?string $country = null): ?array
                {
                    $country = $country ?: $this->getCurrentCountry();
                    return config("countries.{$country}");
                }

                public function getDatabaseName(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['database']['main'] ?? 'movistar_uruguay';
                }

                public function getPosDatabaseName(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['database']['pos'] ?? 'pos_distrimovistar_uruguay';
                }

                public function getAwsBucket(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['aws']['bucket'] ?? 'movilbox-uruguay';
                }

                public function getOperatorUrl(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['urls']['operator'] ?? 'https://uruguay.movilbox.net/operador/';
                }

                public function getRecoveryUrl(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['urls']['recovery'] ?? 'https://uruguay.movilbox.net/operador/index.php?rpss=';
                }

                public function getAppName(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['app']['name'] ?? 'Operador Movistar Uruguay';
                }

                public function getTimezone(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['settings']['timezone'] ?? 'America/Montevideo';
                }

                public function getCurrency(?string $country = null): string
                {
                    $config = $this->getCountryConfig($country);
                    return $config['settings']['currency'] ?? 'UYU';
                }

                public function getVisitTolerance(?string $country = null): int
                {
                    $config = $this->getCountryConfig($country);
                    return $config['tolerances']['visit_distance'] ?? 100;
                }

                public function getGpsPrecision(?string $country = null): int
                {
                    $config = $this->getCountryConfig($country);
                    return $config['tolerances']['gps_precision'] ?? 50;
                }

                public function getDefaultPageSize(?string $country = null): int
                {
                    $config = $this->getCountryConfig($country);
                    return $config['pagination']['default_size'] ?? 20;
                }

                public function getMaxPageSize(?string $country = null): int
                {
                    $config = $this->getCountryConfig($country);
                    return $config['pagination']['max_size'] ?? 100;
                }

                public function isFeatureEnabled(string $feature, ?string $country = null): bool
                {
                    $config = $this->getCountryConfig($country);
                    return $config['features'][$feature] ?? false;
                }
            };
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configurar conexiones de base de datos dinámicamente al inicio
        $this->configureDatabaseConnections();

        // Registrar macros para Query Builder si es necesario
        $this->registerQueryBuilderMacros();
    }

    /**
     * Configurar conexiones de base de datos dinámicamente
     */
    private function configureDatabaseConnections(): void
    {
        $country = config('app.current_country', 'uruguay');
        $countryConfig = config("countries.{$country}");
        
        if ($countryConfig && isset($countryConfig['database'])) {
            // Configurar conexión dinámica
            config([
                'database.connections.dynamic' => array_merge(
                    config('database.connections.mysql'),
                    [
                        'database' => $countryConfig['database']['main']
                    ]
                )
            ]);

            // Configurar conexión POS
            config([
                'database.connections.pos' => array_merge(
                    config('database.connections.mysql'),
                    [
                        'database' => $countryConfig['database']['pos']
                    ]
                )
            ]);
        }
    }

    /**
     * Registrar macros personalizados para Query Builder
     */
    private function registerQueryBuilderMacros(): void
    {
        // Macro para usar la base de datos POS
        DB::macro('pos', function () {
            return DB::connection('pos');
        });

        // Macro para usar la base de datos dinámica
        DB::macro('dynamic', function () {
            return DB::connection('dynamic');
        });
    }
}
