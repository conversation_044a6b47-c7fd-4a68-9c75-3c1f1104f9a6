<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\CheckInService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CheckInController extends Controller
{
    protected $checkInService;

    public function __construct(CheckInService $checkInService)
    {
        $this->checkInService = $checkInService;
    }

    /**
     * Obtener información de tabs para check-in
     * POST /api/tabsInfo
     */
    public function getTabsInfo(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'tabs' => 'nullable|array',
                'tabs.*' => 'string|in:info_general,inventario,encuestas,visitas,fotos'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de tabs inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $tabsSolicitadas = $request->input('tabs', ['info_general', 'inventario', 'encuestas', 'visitas']);

            $tabsInfo = $this->checkInService->getTabsInfo($idpos, $tabsSolicitadas, $user->id);

            return ResponseHelper::success($tabsInfo, 'Información de tabs');

        } catch (\Exception $e) {
            Log::error('Error obteniendo tabs info: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener información general del punto
     * POST /api/infoGeneral
     */
    public function getGeneralInfo(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'ID del punto requerido',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $infoGeneral = $this->checkInService->getInfoGeneral($idpos);

            if (!$infoGeneral) {
                return ResponseHelper::notFound('Punto no encontrado');
            }

            return ResponseHelper::success($infoGeneral, 'Información general del punto');

        } catch (\Exception $e) {
            Log::error('Error obteniendo información general: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener resumen de inventario para tab
     * POST /api/resumenInventario
     */
    public function getInventorySummary(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'categoria' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de inventario inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $categoria = $request->input('categoria');

            $resumen = $this->checkInService->getResumenInventario($idpos, $categoria);

            return ResponseHelper::success($resumen, 'Resumen de inventario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo resumen de inventario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener encuestas disponibles para el punto
     * POST /api/encuestasDisponibles
     */
    public function getAvailableSurveys(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'id_visita' => 'nullable|integer'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de encuestas inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $idVisita = $request->input('id_visita');

            $encuestas = $this->checkInService->getEncuestasDisponibles($idpos, $idVisita, $user->id);

            return ResponseHelper::success($encuestas, 'Encuestas disponibles');

        } catch (\Exception $e) {
            Log::error('Error obteniendo encuestas disponibles: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener historial de visitas del punto
     * POST /api/historialVisitas
     */
    public function getVisitHistory(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'limite' => 'nullable|integer|min:1|max:50',
                'fecha_inicio' => 'nullable|date',
                'fecha_fin' => 'nullable|date|after_or_equal:fecha_inicio'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de historial inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $historial = $this->checkInService->getHistorialVisitas($parametros);

            return ResponseHelper::success($historial, 'Historial de visitas');

        } catch (\Exception $e) {
            Log::error('Error obteniendo historial de visitas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Validar check-in en punto
     * POST /api/validarCheckIn
     */
    public function validateCheckIn(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180',
                'precision_gps' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de ubicación inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;
            $parametros['tolerancia'] = $user->tolerancia_visita ?? 100;

            $validacion = $this->checkInService->validarCheckIn($parametros);

            return ResponseHelper::success($validacion, 'Validación de check-in');

        } catch (\Exception $e) {
            Log::error('Error validando check-in: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Realizar check-in completo
     * POST /api/realizarCheckIn
     */
    public function performCheckIn(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180',
                'precision_gps' => 'nullable|numeric|min:0',
                'observaciones' => 'nullable|string|max:1000',
                'fotos' => 'nullable|array',
                'fotos.*' => 'file|image|max:10240' // 10MB max por foto
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de check-in inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->checkInService->realizarCheckIn($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['checkin'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error realizando check-in: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener información de configuración para check-in
     * GET /api/configuracion-checkin
     */
    public function getCheckInConfiguration(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $configuracion = $this->checkInService->getConfiguracionCheckIn($user->id);

            return ResponseHelper::success($configuracion, 'Configuración de check-in');

        } catch (\Exception $e) {
            Log::error('Error obteniendo configuración de check-in: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener métricas de rendimiento del punto
     * POST /api/metricas-punto
     */
    public function getPointMetrics(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'periodo' => 'nullable|string|in:7,30,90,365',
                'metricas' => 'nullable|array',
                'metricas.*' => 'string|in:visitas,ventas,inventario,encuestas'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de métricas inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $periodo = $request->input('periodo', '30');
            $metricasSolicitadas = $request->input('metricas', ['visitas', 'ventas', 'inventario']);

            $metricas = $this->checkInService->getMetricasPunto($idpos, $periodo, $metricasSolicitadas);

            return ResponseHelper::success($metricas, 'Métricas del punto');

        } catch (\Exception $e) {
            Log::error('Error obteniendo métricas del punto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Actualizar información del punto desde check-in
     * POST /api/actualizar-info-punto
     */
    public function updatePointInfo(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'nombre_propietario' => 'nullable|string|max:200',
                'telefono' => 'nullable|string|max:20',
                'email' => 'nullable|email',
                'estado_comercial' => 'nullable|string',
                'observaciones' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de actualización inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->checkInService->actualizarInfoPunto($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['punto'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error actualizando información del punto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
