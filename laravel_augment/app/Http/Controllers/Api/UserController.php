<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\UserService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * Obtener lista de usuarios
     * GET /api/usuarios
     */
    public function index(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            // Verificar permisos (solo admin puede ver todos los usuarios)
            if (!$user->tienePerfil('Administrador') && $user->id_perfil !== 1) {
                return ResponseHelper::accessDenied();
            }

            $usuarios = $this->userService->getAllUsuarios();

            return ResponseHelper::success($usuarios, 'Lista de usuarios');

        } catch (\Exception $e) {
            Log::error('Error obteniendo usuarios: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener permisos DCS de un usuario
     * POST /api/permisosDcs
     */
    public function getDcsPermissions(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'fechaHora' => 'required|string'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Fecha y hora requeridas',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $fechaHora = $request->input('fechaHora');
            $permisos = $this->userService->getPermisos($user->id, $fechaHora);

            return ResponseHelper::success($permisos, 'Permisos DCS obtenidos');

        } catch (\Exception $e) {
            Log::error('Error obteniendo permisos DCS: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener puntos asignados al usuario
     * GET /api/puntos-usuario
     */
    public function getUserPoints(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $puntos = $this->userService->getPuntos($user->id);

            return ResponseHelper::success($puntos, 'Puntos del usuario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo puntos del usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener estados comerciales
     * GET /api/estados-comerciales
     */
    public function getCommercialStates(Request $request)
    {
        try {
            $fecha = $request->input('fecha', date('Y-m-d'));
            $estados = $this->userService->getEstadosCom($fecha);

            return ResponseHelper::success($estados, 'Estados comerciales');

        } catch (\Exception $e) {
            Log::error('Error obteniendo estados comerciales: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener motivos de visitas no efectivas
     * GET /api/motivos-visita
     */
    public function getVisitReasons(Request $request)
    {
        try {
            $motivos = $this->userService->listarMotivos();

            return ResponseHelper::success($motivos, 'Motivos de visita');

        } catch (\Exception $e) {
            Log::error('Error obteniendo motivos de visita: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Crear nuevo usuario
     * POST /api/usuarios
     */
    public function store(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            
            // Verificar permisos de administrador
            if (!$user || (!$user->tienePerfil('Administrador') && $user->id_perfil !== 1)) {
                return ResponseHelper::accessDenied();
            }

            $validator = Validator::make($request->all(), [
                'cedula' => 'nullable|string|max:20',
                'nombre' => 'required|string|max:100',
                'apellido' => 'required|string|max:100',
                'user' => 'required|string|max:50|unique:usuarios,user',
                'password' => 'required|string|min:6',
                'perfil' => 'nullable|string|max:50',
                'id_perfil' => 'nullable|integer',
                'email' => 'nullable|email|unique:usuarios,email',
                'tolerancia_visita' => 'nullable|integer|min:1'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de entrada inválidos',
                    $validator->errors()
                );
            }

            $resultado = $this->userService->crearUsuario($request->all());

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['usuario'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error creando usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Actualizar usuario
     * PUT /api/usuarios/{id}
     */
    public function update(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            
            // Verificar permisos (admin o el mismo usuario)
            if (!$user || (!$user->tienePerfil('Administrador') && $user->id_perfil !== 1 && $user->id != $id)) {
                return ResponseHelper::accessDenied();
            }

            $validator = Validator::make($request->all(), [
                'cedula' => 'nullable|string|max:20',
                'nombre' => 'sometimes|required|string|max:100',
                'apellido' => 'sometimes|required|string|max:100',
                'email' => 'nullable|email|unique:usuarios,email,' . $id,
                'tolerancia_visita' => 'nullable|integer|min:1'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de entrada inválidos',
                    $validator->errors()
                );
            }

            // Aquí se implementaría la actualización del usuario
            // Por ahora simulamos éxito
            return ResponseHelper::success(null, 'Usuario actualizado exitosamente');

        } catch (\Exception $e) {
            Log::error('Error actualizando usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener información detallada de un usuario
     * GET /api/usuarios/{id}
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            
            // Verificar permisos (admin o el mismo usuario)
            if (!$user || (!$user->tienePerfil('Administrador') && $user->id_perfil !== 1 && $user->id != $id)) {
                return ResponseHelper::accessDenied();
            }

            $usuario = \App\Models\User::find($id);

            if (!$usuario) {
                return ResponseHelper::notFound('Usuario no encontrado');
            }

            $datosUsuario = [
                'id' => $usuario->id,
                'cedula' => $usuario->cedula,
                'nombre' => $usuario->nombre,
                'apellido' => $usuario->apellido,
                'nombre_completo' => $usuario->nombre_completo,
                'user' => $usuario->user,
                'perfil' => $usuario->perfil,
                'id_perfil' => $usuario->id_perfil,
                'email' => $usuario->email,
                'tolerancia_visita' => $usuario->tolerancia_visita,
                'primer_logueo' => $usuario->primer_logueo,
                'ultimo_acceso' => $usuario->ultimo_acceso,
                'fecha_hora' => $usuario->fecha_hora
            ];

            return ResponseHelper::success($datosUsuario, 'Información del usuario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo información del usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Activar/Desactivar usuario
     * PATCH /api/usuarios/{id}/toggle-status
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            
            // Solo administradores pueden cambiar estado
            if (!$user || (!$user->tienePerfil('Administrador') && $user->id_perfil !== 1)) {
                return ResponseHelper::accessDenied();
            }

            $usuario = \App\Models\User::find($id);

            if (!$usuario) {
                return ResponseHelper::notFound('Usuario no encontrado');
            }

            // No permitir desactivar al mismo usuario
            if ($usuario->id === $user->id) {
                return ResponseHelper::error('No puedes desactivar tu propia cuenta');
            }

            $nuevoEstado = !$usuario->activo;
            $usuario->update(['activo' => $nuevoEstado]);

            $mensaje = $nuevoEstado ? 'Usuario activado' : 'Usuario desactivado';

            return ResponseHelper::success([
                'id' => $usuario->id,
                'activo' => $nuevoEstado
            ], $mensaje);

        } catch (\Exception $e) {
            Log::error('Error cambiando estado del usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Actualizar token de notificación
     * POST /api/usuarios/token-notificacion
     */
    public function updateNotificationToken(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Token requerido',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $token = $request->input('token');
            $actualizado = $this->userService->actualizarTokenNotificacion($user->id, $token);

            if ($actualizado) {
                return ResponseHelper::success(null, 'Token de notificación actualizado');
            } else {
                return ResponseHelper::error('Error al actualizar token de notificación');
            }

        } catch (\Exception $e) {
            Log::error('Error actualizando token de notificación: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
