<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SurveyService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SurveyController extends Controller
{
    protected $surveyService;

    public function __construct(SurveyService $surveyService)
    {
        $this->surveyService = $surveyService;
    }

    /**
     * Obtener encuestas disponibles
     * POST /api/encuestas
     */
    public function getSurveys(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'nullable|integer',
                'tipo' => 'nullable|string|in:obligatorias,opcionales,todas',
                'activas_solo' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $encuestas = $this->surveyService->getEncuestas($parametros);

            return ResponseHelper::success($encuestas, 'Encuestas disponibles');

        } catch (\Exception $e) {
            Log::error('Error obteniendo encuestas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener encuestas específicas del operador
     * POST /api/encuestasOperador
     */
    public function getOperatorSurveys(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'fechaHora' => 'required|string',
                'idpos' => 'nullable|integer'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Fecha y hora requeridas',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $encuestas = $this->surveyService->getEncuestasOperador($parametros);

            return ResponseHelper::success($encuestas, 'Encuestas del operador');

        } catch (\Exception $e) {
            Log::error('Error obteniendo encuestas del operador: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Guardar respuestas de encuesta
     * POST /api/guardarEncuesta
     */
    public function saveSurvey(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_encuesta' => 'required|integer',
                'id_visita' => 'required|integer',
                'respuestas' => 'required|array',
                'respuestas.*.id_pregunta' => 'required|integer',
                'respuestas.*.respuesta' => 'required',
                'respuestas.*.archivo' => 'nullable|file|max:10240' // 10MB max
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de encuesta inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->surveyService->guardarEncuesta($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['datos'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error guardando encuesta: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener detalle de una encuesta específica
     * GET /api/encuestas/{id}
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $encuesta = $this->surveyService->getDetalleEncuesta($id);

            if (!$encuesta) {
                return ResponseHelper::notFound('Encuesta no encontrada');
            }

            return ResponseHelper::success($encuesta, 'Detalle de la encuesta');

        } catch (\Exception $e) {
            Log::error('Error obteniendo detalle de encuesta: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener preguntas de una encuesta
     * GET /api/encuestas/{id}/preguntas
     */
    public function getQuestions(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $preguntas = $this->surveyService->getPreguntasEncuesta($id);

            return ResponseHelper::success($preguntas, 'Preguntas de la encuesta');

        } catch (\Exception $e) {
            Log::error('Error obteniendo preguntas de encuesta: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Validar respuestas de encuesta antes de guardar
     * POST /api/validar-respuestas-encuesta
     */
    public function validateResponses(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_encuesta' => 'required|integer',
                'respuestas' => 'required|array',
                'respuestas.*.id_pregunta' => 'required|integer',
                'respuestas.*.respuesta' => 'required'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idEncuesta = $request->input('id_encuesta');
            $respuestas = $request->input('respuestas');

            $validacion = $this->surveyService->validarRespuestas($idEncuesta, $respuestas);

            return ResponseHelper::success($validacion, 'Validación de respuestas');

        } catch (\Exception $e) {
            Log::error('Error validando respuestas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener progreso de encuestas para una visita
     * GET /api/progreso-encuestas/{id_visita}
     */
    public function getSurveyProgress(Request $request, $idVisita)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $progreso = $this->surveyService->getProgresoEncuestas($idVisita);

            return ResponseHelper::success($progreso, 'Progreso de encuestas');

        } catch (\Exception $e) {
            Log::error('Error obteniendo progreso de encuestas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener respuestas guardadas de una encuesta para una visita
     * GET /api/respuestas-encuesta/{id_encuesta}/{id_visita}
     */
    public function getSavedResponses(Request $request, $idEncuesta, $idVisita)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $respuestas = $this->surveyService->getRespuestasGuardadas($idEncuesta, $idVisita);

            return ResponseHelper::success($respuestas, 'Respuestas guardadas');

        } catch (\Exception $e) {
            Log::error('Error obteniendo respuestas guardadas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Marcar encuesta como completada
     * POST /api/completar-encuesta
     */
    public function completeSurvey(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_encuesta' => 'required|integer',
                'id_visita' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos requeridos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idEncuesta = $request->input('id_encuesta');
            $idVisita = $request->input('id_visita');

            $resultado = $this->surveyService->completarEncuesta($idEncuesta, $idVisita, $user->id);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['datos'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error completando encuesta: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener estadísticas de encuestas
     * GET /api/estadisticas-encuestas
     */
    public function getStatistics(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $fechaInicio = $request->input('fecha_inicio', date('Y-m-01'));
            $fechaFin = $request->input('fecha_fin', date('Y-m-d'));

            $estadisticas = $this->surveyService->getEstadisticasEncuestas($user->id, $fechaInicio, $fechaFin);

            return ResponseHelper::success($estadisticas, 'Estadísticas de encuestas');

        } catch (\Exception $e) {
            Log::error('Error obteniendo estadísticas de encuestas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
