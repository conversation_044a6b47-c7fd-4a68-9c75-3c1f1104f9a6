<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\FileService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class FileController extends Controller
{
    protected $fileService;

    public function __construct(FileService $fileService)
    {
        $this->fileService = $fileService;
    }

    /**
     * Subir archivo
     * POST /api/subirArchivo
     */
    public function uploadFile(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'archivo' => 'required|file|max:51200', // 50MB max
                'tipo' => 'required|string|in:imagen,documento,video,audio',
                'categoria' => 'nullable|string|in:visita,encuesta,inventario,punto,perfil',
                'id_referencia' => 'nullable|integer',
                'descripcion' => 'nullable|string|max:500',
                'publico' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de archivo inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->fileService->subirArchivo($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['archivo'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error subiendo archivo: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Subir múltiples archivos
     * POST /api/subirMultiplesArchivos
     */
    public function uploadMultipleFiles(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'archivos' => 'required|array|max:10',
                'archivos.*' => 'file|max:51200',
                'tipo' => 'required|string|in:imagen,documento,video,audio',
                'categoria' => 'nullable|string|in:visita,encuesta,inventario,punto,perfil',
                'id_referencia' => 'nullable|integer',
                'descripcion' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de archivos inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->fileService->subirMultiplesArchivos($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['archivos'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error subiendo múltiples archivos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener información de archivo
     * GET /api/archivo/{id}
     */
    public function getFileInfo(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $archivo = $this->fileService->getInfoArchivo($id, $user->id);

            if (!$archivo) {
                return ResponseHelper::notFound('Archivo no encontrado');
            }

            return ResponseHelper::success($archivo, 'Información del archivo');

        } catch (\Exception $e) {
            Log::error('Error obteniendo información del archivo: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Descargar archivo
     * GET /api/descargar/{id}
     */
    public function downloadFile(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $descarga = $this->fileService->descargarArchivo($id, $user->id);

            if (!$descarga) {
                return ResponseHelper::notFound('Archivo no encontrado o sin permisos');
            }

            return response()->download($descarga['ruta'], $descarga['nombre']);

        } catch (\Exception $e) {
            Log::error('Error descargando archivo: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Eliminar archivo
     * DELETE /api/archivo/{id}
     */
    public function deleteFile(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $resultado = $this->fileService->eliminarArchivo($id, $user->id);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success(null, $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error eliminando archivo: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Listar archivos del usuario
     * GET /api/mis-archivos
     */
    public function getMyFiles(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $tipo = $request->input('tipo');
            $categoria = $request->input('categoria');
            $limite = $request->input('limite', 50);
            $pagina = $request->input('pagina', 1);

            $parametros = [
                'id_usuario' => $user->id,
                'tipo' => $tipo,
                'categoria' => $categoria,
                'limite' => $limite,
                'pagina' => $pagina
            ];

            $archivos = $this->fileService->listarArchivosUsuario($parametros);

            return ResponseHelper::success($archivos, 'Archivos del usuario');

        } catch (\Exception $e) {
            Log::error('Error listando archivos del usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener archivos por referencia
     * POST /api/archivos-referencia
     */
    public function getFilesByReference(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'categoria' => 'required|string|in:visita,encuesta,inventario,punto,perfil',
                'id_referencia' => 'required|integer',
                'tipo' => 'nullable|string|in:imagen,documento,video,audio'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de referencia inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $categoria = $request->input('categoria');
            $idReferencia = $request->input('id_referencia');
            $tipo = $request->input('tipo');

            $archivos = $this->fileService->getArchivosPorReferencia($categoria, $idReferencia, $tipo);

            return ResponseHelper::success($archivos, 'Archivos de la referencia');

        } catch (\Exception $e) {
            Log::error('Error obteniendo archivos por referencia: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Generar URL temporal para archivo
     * POST /api/url-temporal-archivo
     */
    public function generateTemporaryUrl(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_archivo' => 'required|integer',
                'duracion_minutos' => 'nullable|integer|min:1|max:1440' // máximo 24 horas
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de URL temporal inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idArchivo = $request->input('id_archivo');
            $duracionMinutos = $request->input('duracion_minutos', 60);

            $urlTemporal = $this->fileService->generarUrlTemporal($idArchivo, $duracionMinutos, $user->id);

            if ($urlTemporal) {
                return ResponseHelper::success([
                    'url' => $urlTemporal['url'],
                    'expira_en' => $urlTemporal['expira_en'],
                    'duracion_minutos' => $duracionMinutos
                ], 'URL temporal generada');
            } else {
                return ResponseHelper::error('No se pudo generar la URL temporal');
            }

        } catch (\Exception $e) {
            Log::error('Error generando URL temporal: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener estadísticas de archivos
     * GET /api/estadisticas-archivos
     */
    public function getFileStatistics(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $estadisticas = $this->fileService->getEstadisticasArchivos($user->id);

            return ResponseHelper::success($estadisticas, 'Estadísticas de archivos');

        } catch (\Exception $e) {
            Log::error('Error obteniendo estadísticas de archivos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Comprimir imagen
     * POST /api/comprimir-imagen
     */
    public function compressImage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'imagen' => 'required|file|image|max:51200',
                'calidad' => 'nullable|integer|min:10|max:100',
                'ancho_maximo' => 'nullable|integer|min:100|max:4000',
                'alto_maximo' => 'nullable|integer|min:100|max:4000'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de compresión inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->fileService->comprimirImagen($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['imagen'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error comprimiendo imagen: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Validar archivo antes de subir
     * POST /api/validar-archivo
     */
    public function validateFile(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'nombre_archivo' => 'required|string',
                'tamaño_bytes' => 'required|integer|min:1',
                'tipo_mime' => 'required|string',
                'tipo' => 'required|string|in:imagen,documento,video,audio'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de validación inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $validacion = $this->fileService->validarArchivo($request->all());

            return ResponseHelper::success($validacion, 'Validación de archivo');

        } catch (\Exception $e) {
            Log::error('Error validando archivo: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
