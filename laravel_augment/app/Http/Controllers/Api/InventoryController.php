<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\InventoryService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class InventoryController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Obtener detalle de inventario de un punto
     * POST /api/detalleInventario
     */
    public function getInventoryDetail(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'categoria' => 'nullable|string',
                'estado' => 'nullable|string|in:DISPONIBLE,VENDIDO,DEFECTUOSO,DEVUELTO',
                'fecha_inicio' => 'nullable|date',
                'fecha_fin' => 'nullable|date|after_or_equal:fecha_inicio'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de inventario inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $inventario = $this->inventoryService->getDetalleInventario($parametros);

            return ResponseHelper::success($inventario, 'Detalle de inventario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo detalle de inventario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener información de un producto específico
     * POST /api/infoProducto
     */
    public function getProductInfo(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'serial' => 'nullable|string',
                'id_referencia' => 'nullable|integer',
                'codigo_barras' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de producto inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $producto = $this->inventoryService->getInfoProducto($parametros);

            if (!$producto) {
                return ResponseHelper::notFound('Producto no encontrado');
            }

            return ResponseHelper::success($producto, 'Información del producto');

        } catch (\Exception $e) {
            Log::error('Error obteniendo información del producto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Registrar movimiento de inventario
     * POST /api/registrar-movimiento-inventario
     */
    public function registerMovement(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_pos' => 'required|integer',
                'tipo_movimiento' => 'required|string|in:INGRESO,VENTA,DEVOLUCION,AJUSTE',
                'productos' => 'required|array',
                'productos.*.id_referencia' => 'required|integer',
                'productos.*.serial' => 'nullable|string',
                'productos.*.cantidad' => 'required|integer|min:1',
                'productos.*.precio' => 'nullable|numeric|min:0',
                'observaciones' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de movimiento inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->inventoryService->registrarMovimiento($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['movimiento'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error registrando movimiento de inventario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Buscar productos en inventario
     * POST /api/buscar-productos-inventario
     */
    public function searchProducts(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'filtro' => 'nullable|string',
                'tipo_filtro' => 'nullable|string|in:serial,descripcion,referencia',
                'categoria' => 'nullable|string',
                'estado' => 'nullable|string',
                'id_pos' => 'nullable|integer',
                'limite' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de búsqueda inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $productos = $this->inventoryService->buscarProductos($parametros);

            return ResponseHelper::success($productos, 'Productos encontrados');

        } catch (\Exception $e) {
            Log::error('Error buscando productos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Actualizar estado de producto
     * POST /api/actualizar-estado-producto
     */
    public function updateProductStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_detalle' => 'required|integer',
                'nuevo_estado' => 'required|string|in:DISPONIBLE,VENDIDO,DEFECTUOSO,DEVUELTO',
                'observaciones' => 'nullable|string|max:1000',
                'precio_venta' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de actualización inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->inventoryService->actualizarEstadoProducto($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['producto'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error actualizando estado del producto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener referencias de productos disponibles
     * GET /api/referencias-productos
     */
    public function getProductReferences(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $categoria = $request->input('categoria');
            $activas = $request->input('activas', true);

            $referencias = $this->inventoryService->getReferenciasProductos($categoria, $activas);

            return ResponseHelper::success($referencias, 'Referencias de productos');

        } catch (\Exception $e) {
            Log::error('Error obteniendo referencias de productos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener estadísticas de inventario
     * GET /api/estadisticas-inventario
     */
    public function getInventoryStatistics(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idPos = $request->input('id_pos');
            $fechaInicio = $request->input('fecha_inicio', date('Y-m-01'));
            $fechaFin = $request->input('fecha_fin', date('Y-m-d'));

            $estadisticas = $this->inventoryService->getEstadisticasInventario($idPos, $fechaInicio, $fechaFin);

            return ResponseHelper::success($estadisticas, 'Estadísticas de inventario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo estadísticas de inventario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Generar reporte de inventario
     * POST /api/reporte-inventario
     */
    public function generateInventoryReport(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_pos' => 'nullable|integer',
                'tipo_reporte' => 'required|string|in:STOCK,MOVIMIENTOS,VENTAS,ROTACION',
                'fecha_inicio' => 'required|date',
                'fecha_fin' => 'required|date|after_or_equal:fecha_inicio',
                'formato' => 'nullable|string|in:JSON,EXCEL,PDF'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de reporte inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $reporte = $this->inventoryService->generarReporte($parametros);

            if ($reporte['estado'] === 1) {
                return ResponseHelper::success($reporte['datos'], $reporte['msg']);
            } else {
                return ResponseHelper::error($reporte['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error generando reporte de inventario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Calcular rotación de inventario
     * POST /api/calcular-rotacion-inventario
     */
    public function calculateInventoryRotation(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_pos' => 'required|integer',
                'periodo_dias' => 'nullable|integer|min:1|max:365'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de rotación inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idPos = $request->input('id_pos');
            $periodoDias = $request->input('periodo_dias', 30);

            $rotacion = $this->inventoryService->calcularRotacion($idPos, $periodoDias);

            return ResponseHelper::success($rotacion, 'Rotación de inventario');

        } catch (\Exception $e) {
            Log::error('Error calculando rotación de inventario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
