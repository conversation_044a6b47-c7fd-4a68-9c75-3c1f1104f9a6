<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\AuthService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Login de usuario
     * POST /api/login
     */
    public function login(Request $request)
    {
        try {
            // Validar datos de entrada
            $validator = Validator::make($request->all(), [
                'user' => 'required|string',
                'pass' => 'required|string',
                'fechaHora' => 'nullable|string',
                'versionName' => 'required|string',
                'versionCode' => 'required|integer',
                'tokenNotificacion' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de entrada inválidos',
                    $validator->errors()
                );
            }

            // Procesar login
            $resultado = $this->authService->login($request->all());

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['datos'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error en login: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Recuperar contraseña por email
     * POST /api/recuperarPass
     */
    public function recoverPassword(Request $request)
    {
        try {
            // Validar email
            $validator = Validator::make($request->all(), [
                'email' => 'required|email'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Email inválido',
                    $validator->errors()
                );
            }

            $email = $request->input('email');

            // Procesar recuperación
            $resultado = $this->authService->recuperarPassword($email);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success(null, $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error en recuperación de contraseña: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Cambiar contraseña
     * POST /api/cambiarPass
     */
    public function changePassword(Request $request)
    {
        try {
            // Validar datos
            $validator = Validator::make($request->all(), [
                'passAnterior' => 'required|string',
                'passNuevo' => 'required|string|min:6'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de entrada inválidos',
                    $validator->errors()
                );
            }

            // Obtener usuario autenticado
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $passwordAnterior = $request->input('passAnterior');
            $passwordNuevo = $request->input('passNuevo');

            // Procesar cambio de contraseña
            $resultado = $this->authService->cambiarPassword($user, $passwordAnterior, $passwordNuevo);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success(null, $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error en cambio de contraseña: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Logout (invalidar token)
     * POST /api/logout
     */
    public function logout(Request $request)
    {
        try {
            // Invalidar token JWT
            auth()->logout();

            return ResponseHelper::success(null, 'Sesión cerrada exitosamente');

        } catch (\Exception $e) {
            Log::error('Error en logout: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Refrescar token JWT
     * POST /api/refresh
     */
    public function refresh(Request $request)
    {
        try {
            $newToken = auth()->refresh();

            return ResponseHelper::success([
                'token' => $newToken,
                'token_type' => 'bearer',
                'expires_in' => auth()->factory()->getTTL() * 60
            ], 'Token renovado exitosamente');

        } catch (\Exception $e) {
            Log::error('Error en refresh token: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener información del usuario autenticado
     * GET /api/me
     */
    public function me(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $userData = [
                'id' => $user->id,
                'cedula' => $user->cedula,
                'nombre' => $user->nombre_completo,
                'user' => $user->user,
                'perfil' => $user->perfil,
                'id_perfil' => $user->id_perfil,
                'email' => $user->email,
                'tolerancia_visita' => $user->tolerancia_visita,
                'primer_logueo' => $user->primer_logueo,
                'ultimo_acceso' => $user->ultimo_acceso
            ];

            return ResponseHelper::success($userData, 'Información del usuario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo información del usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Validar token (para verificación externa)
     * POST /api/validate-token
     */
    public function validateToken(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            
            if (!$user) {
                return ResponseHelper::error('Token inválido');
            }

            return ResponseHelper::success([
                'valid' => true,
                'user_id' => $user->id,
                'username' => $user->user,
                'perfil' => $user->perfil
            ], 'Token válido');

        } catch (\Exception $e) {
            Log::error('Error validando token: ' . $e->getMessage());
            return ResponseHelper::error('Token inválido');
        }
    }
}
