<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\PointService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PointController extends Controller
{
    protected $pointService;

    public function __construct(PointService $pointService)
    {
        $this->pointService = $pointService;
    }

    /**
     * Buscar puntos de venta
     * POST /api/buscarPuntos
     */
    public function search(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'nombre' => 'nullable|string',
                'latitud' => 'nullable|numeric',
                'longitud' => 'nullable|numeric',
                'radio' => 'nullable|numeric|min:1',
                'departamento' => 'nullable|string',
                'municipio' => 'nullable|string',
                'categoria' => 'nullable|string',
                'estado_comercial' => 'nullable|string',
                'circuito' => 'nullable|string',
                'ruta' => 'nullable|string',
                'limite' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de búsqueda inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $resultado = $this->pointService->buscarPuntos($parametros);

            return ResponseHelper::success($resultado, 'Puntos encontrados');

        } catch (\Exception $e) {
            Log::error('Error buscando puntos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener puntos visitados
     * POST /api/puntosVisitados
     */
    public function visitedPoints(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'fecha_inicio' => 'nullable|date',
                'fecha_fin' => 'nullable|date|after_or_equal:fecha_inicio',
                'limite' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $resultado = $this->pointService->getPuntosVisitados($parametros);

            return ResponseHelper::success($resultado, 'Puntos visitados');

        } catch (\Exception $e) {
            Log::error('Error obteniendo puntos visitados: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Buscar cartera de puntos
     * POST /api/buscarCarteraPuntos
     */
    public function searchPortfolio(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'filtro' => 'nullable|string',
                'tipo_filtro' => 'nullable|string|in:nombre,codigo,direccion',
                'limite' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Parámetros de búsqueda inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $parametros = $request->all();
            $parametros['id_usuario'] = $user->id;

            $resultado = $this->pointService->buscarCarteraPuntos($parametros);

            return ResponseHelper::success($resultado, 'Cartera de puntos');

        } catch (\Exception $e) {
            Log::error('Error buscando cartera de puntos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Crear o actualizar punto
     * POST /api/crearActualizarPuntos
     */
    public function createOrUpdate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'nullable|integer',
                'nombre' => 'required|string|max:200',
                'direccion' => 'required|string',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180',
                'precision_gps' => 'nullable|numeric|min:0',
                'nombre_propietario' => 'nullable|string|max:200',
                'telefono' => 'nullable|string|max:20',
                'email' => 'nullable|email',
                'categoria' => 'nullable|string',
                'estado_comercial' => 'nullable|string',
                'id_departamento' => 'nullable|integer',
                'id_municipio' => 'nullable|integer',
                'circuito' => 'nullable|string|max:50',
                'ruta' => 'nullable|string|max:50'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos del punto inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->pointService->crearOActualizarPunto($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['punto'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error creando/actualizando punto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener campos dinámicos para formularios
     * GET /api/camposDinamicos
     */
    public function getDynamicFields(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $campos = $this->pointService->getCamposDinamicos();

            return ResponseHelper::success($campos, 'Campos dinámicos');

        } catch (\Exception $e) {
            Log::error('Error obteniendo campos dinámicos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener campos dinámicos específicos de un punto
     * POST /api/camposDinamicosPunto
     */
    public function getPointDynamicFields(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'ID del punto requerido',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $campos = $this->pointService->getCamposDinamicosPunto($idpos);

            return ResponseHelper::success($campos, 'Campos dinámicos del punto');

        } catch (\Exception $e) {
            Log::error('Error obteniendo campos dinámicos del punto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener detalle completo de un punto
     * GET /api/puntos/{id}
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $punto = $this->pointService->getDetallePunto($id);

            if (!$punto) {
                return ResponseHelper::notFound('Punto no encontrado');
            }

            return ResponseHelper::success($punto, 'Detalle del punto');

        } catch (\Exception $e) {
            Log::error('Error obteniendo detalle del punto: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Validar proximidad a un punto
     * POST /api/validar-proximidad
     */
    public function validateProximity(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'idpos' => 'required|integer',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180',
                'tolerancia' => 'nullable|integer|min:1'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de ubicación inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idpos = $request->input('idpos');
            $latitud = $request->input('latitud');
            $longitud = $request->input('longitud');
            $tolerancia = $request->input('tolerancia', $user->tolerancia_visita ?? 100);

            $resultado = $this->pointService->validarProximidad($idpos, $latitud, $longitud, $tolerancia);

            return ResponseHelper::success($resultado, 'Validación de proximidad');

        } catch (\Exception $e) {
            Log::error('Error validando proximidad: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener estadísticas de puntos
     * GET /api/estadisticas-puntos
     */
    public function getStatistics(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $estadisticas = $this->pointService->getEstadisticasPuntos($user->id);

            return ResponseHelper::success($estadisticas, 'Estadísticas de puntos');

        } catch (\Exception $e) {
            Log::error('Error obteniendo estadísticas de puntos: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
