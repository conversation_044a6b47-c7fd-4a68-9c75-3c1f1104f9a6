<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\VisitService;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class VisitController extends Controller
{
    protected $visitService;

    public function __construct(VisitService $visitService)
    {
        $this->visitService = $visitService;
    }

    /**
     * Guardar nueva visita
     * POST /api/guardarVisita
     */
    public function saveVisit(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_pos' => 'required|integer',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180',
                'precision_gps' => 'nullable|numeric|min:0',
                'efectiva' => 'required|integer|in:0,1',
                'motivo_no_efectiva' => 'nullable|integer',
                'observaciones' => 'nullable|string|max:1000',
                'fecha' => 'nullable|date',
                'hora_inicio' => 'nullable|date_format:H:i:s',
                'hora_fin' => 'nullable|date_format:H:i:s|after:hora_inicio'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de visita inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->visitService->guardarVisita($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['visita'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error guardando visita: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener detalle de una visita
     * POST /api/detalleVisita
     */
    public function getVisitDetail(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_visita' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'ID de visita requerido',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idVisita = $request->input('id_visita');
            $detalle = $this->visitService->getDetalleVisita($idVisita, $user->id);

            if (!$detalle) {
                return ResponseHelper::notFound('Visita no encontrada');
            }

            return ResponseHelper::success($detalle, 'Detalle de la visita');

        } catch (\Exception $e) {
            Log::error('Error obteniendo detalle de visita: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Iniciar visita (check-in)
     * POST /api/iniciar-visita
     */
    public function startVisit(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_pos' => 'required|integer',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180',
                'precision_gps' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de ubicación inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->visitService->iniciarVisita($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['visita'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error iniciando visita: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Finalizar visita (check-out)
     * POST /api/finalizar-visita
     */
    public function endVisit(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_visita' => 'required|integer',
                'efectiva' => 'required|integer|in:0,1',
                'motivo_no_efectiva' => 'nullable|integer',
                'observaciones' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de finalización inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;

            $resultado = $this->visitService->finalizarVisita($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['visita'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error finalizando visita: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener visitas del usuario
     * GET /api/mis-visitas
     */
    public function getMyVisits(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $fechaInicio = $request->input('fecha_inicio', date('Y-m-01'));
            $fechaFin = $request->input('fecha_fin', date('Y-m-d'));
            $estado = $request->input('estado');
            $limite = $request->input('limite', 50);

            $parametros = [
                'id_usuario' => $user->id,
                'fecha_inicio' => $fechaInicio,
                'fecha_fin' => $fechaFin,
                'estado' => $estado,
                'limite' => $limite
            ];

            $visitas = $this->visitService->getVisitasUsuario($parametros);

            return ResponseHelper::success($visitas, 'Visitas del usuario');

        } catch (\Exception $e) {
            Log::error('Error obteniendo visitas del usuario: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Actualizar visita
     * PUT /api/visitas/{id}
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'efectiva' => 'sometimes|integer|in:0,1',
                'motivo_no_efectiva' => 'nullable|integer',
                'observaciones' => 'nullable|string|max:1000',
                'estado' => 'sometimes|integer|in:0,1,2,3'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de actualización inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $datos = $request->all();
            $datos['id_usuario'] = $user->id;
            $datos['id_visita'] = $id;

            $resultado = $this->visitService->actualizarVisita($datos);

            if ($resultado['estado'] === 1) {
                return ResponseHelper::success($resultado['visita'], $resultado['msg']);
            } else {
                return ResponseHelper::error($resultado['msg']);
            }

        } catch (\Exception $e) {
            Log::error('Error actualizando visita: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Validar ubicación para visita
     * POST /api/validar-ubicacion-visita
     */
    public function validateLocation(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_pos' => 'required|integer',
                'latitud' => 'required|numeric|between:-90,90',
                'longitud' => 'required|numeric|between:-180,180'
            ]);

            if ($validator->fails()) {
                return ResponseHelper::validationError(
                    'Datos de ubicación inválidos',
                    $validator->errors()
                );
            }

            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $idPos = $request->input('id_pos');
            $latitud = $request->input('latitud');
            $longitud = $request->input('longitud');
            $tolerancia = $user->tolerancia_visita ?? 100;

            $validacion = $this->visitService->validarUbicacion($idPos, $latitud, $longitud, $tolerancia);

            return ResponseHelper::success($validacion, 'Validación de ubicación');

        } catch (\Exception $e) {
            Log::error('Error validando ubicación: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener estadísticas de visitas
     * GET /api/estadisticas-visitas
     */
    public function getStatistics(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $fechaInicio = $request->input('fecha_inicio', date('Y-m-01'));
            $fechaFin = $request->input('fecha_fin', date('Y-m-d'));

            $estadisticas = $this->visitService->getEstadisticasVisitas($user->id, $fechaInicio, $fechaFin);

            return ResponseHelper::success($estadisticas, 'Estadísticas de visitas');

        } catch (\Exception $e) {
            Log::error('Error obteniendo estadísticas de visitas: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }

    /**
     * Obtener visita activa (en proceso)
     * GET /api/visita-activa
     */
    public function getActiveVisit(Request $request)
    {
        try {
            $user = $request->input('authenticated_user');
            if (!$user) {
                return ResponseHelper::accessDenied();
            }

            $visitaActiva = $this->visitService->getVisitaActiva($user->id);

            if ($visitaActiva) {
                return ResponseHelper::success($visitaActiva, 'Visita activa');
            } else {
                return ResponseHelper::success(null, 'No hay visita activa');
            }

        } catch (\Exception $e) {
            Log::error('Error obteniendo visita activa: ' . $e->getMessage());
            return ResponseHelper::serverError('Error interno del servidor');
        }
    }
}
