<?php

namespace App\Services;

use App\Models\Visita;
use App\Models\Point;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VisitService
{
    /**
     * Guardar nueva visita
     */
    public function guardarVisita(array $datos): array
    {
        try {
            DB::beginTransaction();

            $idPos = $datos['id_pos'];
            $idUsuario = $datos['id_usuario'];
            $latitud = $datos['latitud'];
            $longitud = $datos['longitud'];
            $efectiva = $datos['efectiva'];

            // Verificar que el punto existe
            $punto = Point::find($idPos);
            if (!$punto) {
                return [
                    'estado' => 0,
                    'msg' => 'Punto de venta no encontrado',
                    'visita' => null
                ];
            }

            // Verificar que el usuario existe
            $usuario = User::find($idUsuario);
            if (!$usuario) {
                return [
                    'estado' => 0,
                    'msg' => 'Usuario no encontrado',
                    'visita' => null
                ];
            }

            // Validar proximidad al punto
            $tolerancia = $usuario->tolerancia_visita ?? 100;
            $distancia = $punto->calcularDistancia($latitud, $longitud);
            $dentroTolerancia = $punto->estaDentroDeTolerancia($latitud, $longitud, $tolerancia);

            if (!$dentroTolerancia && $efectiva == 1) {
                return [
                    'estado' => 0,
                    'msg' => "Ubicación fuera del rango permitido. Distancia: {$distancia}m, Tolerancia: {$tolerancia}m",
                    'visita' => null
                ];
            }

            // Crear nueva visita
            $visita = new Visita();
            $visita->id_pos = $idPos;
            $visita->id_usuario = $idUsuario;
            $visita->fecha = $datos['fecha'] ?? now()->toDateString();
            $visita->hora_inicio = $datos['hora_inicio'] ?? now();
            $visita->hora_fin = $datos['hora_fin'] ?? null;
            $visita->latitud = $latitud;
            $visita->longitud = $longitud;
            $visita->precision_gps = $datos['precision_gps'] ?? null;
            $visita->efectiva = $efectiva;
            $visita->motivo_no_efectiva = $datos['motivo_no_efectiva'] ?? null;
            $visita->observaciones = $datos['observaciones'] ?? null;
            $visita->estado = isset($datos['hora_fin']) ? Visita::ESTADO_COMPLETADA : Visita::ESTADO_EN_PROCESO;
            $visita->fecha_creacion = now();

            $visita->save();

            // Actualizar contador de visitas del punto
            $punto->increment('cantidad_visitas');

            // Si es efectiva, marcar punto como efectivo
            if ($efectiva == 1) {
                $punto->marcarComoEfectivo();
            }

            DB::commit();

            return [
                'estado' => 1,
                'msg' => 'Visita guardada exitosamente',
                'visita' => [
                    'id' => $visita->id,
                    'id_pos' => $visita->id_pos,
                    'punto_nombre' => $punto->nombre,
                    'fecha' => $visita->fecha->format('Y-m-d'),
                    'hora_inicio' => $visita->hora_inicio ? $visita->hora_inicio->format('H:i:s') : null,
                    'hora_fin' => $visita->hora_fin ? $visita->hora_fin->format('H:i:s') : null,
                    'efectiva' => $visita->efectiva,
                    'estado' => $visita->estado_nombre,
                    'distancia_punto' => round($distancia, 2),
                    'dentro_tolerancia' => $dentroTolerancia
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error en guardarVisita: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al guardar la visita',
                'visita' => null
            ];
        }
    }

    /**
     * Iniciar visita (check-in)
     */
    public function iniciarVisita(array $datos): array
    {
        try {
            $idPos = $datos['id_pos'];
            $idUsuario = $datos['id_usuario'];
            $latitud = $datos['latitud'];
            $longitud = $datos['longitud'];

            // Verificar que no hay otra visita activa
            $visitaActiva = Visita::where('id_usuario', $idUsuario)
                ->where('estado', Visita::ESTADO_EN_PROCESO)
                ->first();

            if ($visitaActiva) {
                return [
                    'estado' => 0,
                    'msg' => 'Ya tienes una visita en proceso. Finalízala antes de iniciar otra.',
                    'visita' => null
                ];
            }

            // Crear visita en proceso
            $datosVisita = array_merge($datos, [
                'efectiva' => 1, // Asumimos efectiva al iniciar
                'hora_inicio' => now(),
                'estado' => Visita::ESTADO_EN_PROCESO
            ]);

            return $this->guardarVisita($datosVisita);

        } catch (\Exception $e) {
            Log::error('Error en iniciarVisita: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al iniciar la visita',
                'visita' => null
            ];
        }
    }

    /**
     * Finalizar visita (check-out)
     */
    public function finalizarVisita(array $datos): array
    {
        try {
            DB::beginTransaction();

            $idVisita = $datos['id_visita'];
            $idUsuario = $datos['id_usuario'];
            $efectiva = $datos['efectiva'];

            $visita = Visita::where('id', $idVisita)
                ->where('id_usuario', $idUsuario)
                ->first();

            if (!$visita) {
                return [
                    'estado' => 0,
                    'msg' => 'Visita no encontrada o no autorizada',
                    'visita' => null
                ];
            }

            if ($visita->estado === Visita::ESTADO_COMPLETADA) {
                return [
                    'estado' => 0,
                    'msg' => 'La visita ya está finalizada',
                    'visita' => null
                ];
            }

            // Actualizar visita
            $visita->hora_fin = now();
            $visita->efectiva = $efectiva;
            $visita->motivo_no_efectiva = $datos['motivo_no_efectiva'] ?? null;
            $visita->observaciones = $datos['observaciones'] ?? null;
            $visita->estado = Visita::ESTADO_COMPLETADA;

            $visita->save();

            // Si cambió a no efectiva, actualizar punto
            if ($efectiva == 0 && $visita->punto) {
                // Aquí se podría implementar lógica adicional para puntos no efectivos
            }

            DB::commit();

            return [
                'estado' => 1,
                'msg' => 'Visita finalizada exitosamente',
                'visita' => [
                    'id' => $visita->id,
                    'efectiva' => $visita->efectiva,
                    'estado' => $visita->estado_nombre,
                    'duracion_minutos' => $visita->duracion,
                    'hora_fin' => $visita->hora_fin->format('H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error en finalizarVisita: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al finalizar la visita',
                'visita' => null
            ];
        }
    }

    /**
     * Obtener detalle de una visita
     */
    public function getDetalleVisita(int $idVisita, int $idUsuario): ?array
    {
        try {
            $visita = Visita::with(['punto.infoPos', 'usuario', 'encuestas', 'respuestasEncuestas'])
                ->where('id', $idVisita)
                ->where('id_usuario', $idUsuario)
                ->first();

            if (!$visita) {
                return null;
            }

            return [
                'id' => $visita->id,
                'punto' => [
                    'idpos' => $visita->punto->idpos,
                    'nombre' => $visita->punto->nombre,
                    'direccion' => $visita->punto->direccion,
                    'latitud' => $visita->punto->latitud,
                    'longitud' => $visita->punto->longitud,
                    'info_adicional' => $visita->punto->infoPos ? [
                        'propietario' => $visita->punto->infoPos->nombre_propietario,
                        'telefono' => $visita->punto->infoPos->telefono,
                        'categoria' => $visita->punto->infoPos->categoria
                    ] : null
                ],
                'fecha' => $visita->fecha->format('Y-m-d'),
                'hora_inicio' => $visita->hora_inicio ? $visita->hora_inicio->format('H:i:s') : null,
                'hora_fin' => $visita->hora_fin ? $visita->hora_fin->format('H:i:s') : null,
                'duracion_minutos' => $visita->duracion,
                'ubicacion_visita' => [
                    'latitud' => $visita->latitud,
                    'longitud' => $visita->longitud,
                    'precision_gps' => $visita->precision_gps
                ],
                'efectiva' => $visita->efectiva,
                'motivo_no_efectiva' => $visita->motivo_no_efectiva,
                'observaciones' => $visita->observaciones,
                'estado' => $visita->estado_nombre,
                'distancia_punto' => $visita->calcularDistanciaAlPunto(),
                'dentro_tolerancia' => $visita->estaDentroDeTolerancia(),
                'encuestas' => $visita->encuestas->map(function($encuesta) use ($visita) {
                    return [
                        'id' => $encuesta->id,
                        'titulo' => $encuesta->titulo,
                        'obligatorio' => $encuesta->es_obligatoria,
                        'progreso' => $encuesta->getProgresoParaVisita($visita->id),
                        'completa' => $encuesta->estaCompletaParaVisita($visita->id)
                    ];
                }),
                'total_respuestas' => $visita->respuestasEncuestas->count()
            ];

        } catch (\Exception $e) {
            Log::error('Error en getDetalleVisita: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener visitas de un usuario
     */
    public function getVisitasUsuario(array $parametros): array
    {
        try {
            $idUsuario = $parametros['id_usuario'];
            $fechaInicio = $parametros['fecha_inicio'];
            $fechaFin = $parametros['fecha_fin'];
            $estado = $parametros['estado'] ?? null;
            $limite = $parametros['limite'] ?? 50;

            $query = Visita::with(['punto'])
                ->where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin]);

            if ($estado !== null) {
                $query->where('estado', $estado);
            }

            $visitas = $query->orderBy('fecha', 'desc')
                ->orderBy('hora_inicio', 'desc')
                ->limit($limite)
                ->get();

            $resultado = $visitas->map(function($visita) {
                return [
                    'id' => $visita->id,
                    'punto' => [
                        'idpos' => $visita->punto->idpos,
                        'nombre' => $visita->punto->nombre,
                        'direccion' => $visita->punto->direccion
                    ],
                    'fecha' => $visita->fecha->format('Y-m-d'),
                    'hora_inicio' => $visita->hora_inicio ? $visita->hora_inicio->format('H:i:s') : null,
                    'hora_fin' => $visita->hora_fin ? $visita->hora_fin->format('H:i:s') : null,
                    'duracion_minutos' => $visita->duracion,
                    'efectiva' => $visita->efectiva,
                    'estado' => $visita->estado_nombre,
                    'distancia_punto' => $visita->calcularDistanciaAlPunto()
                ];
            });

            return [
                'total' => $resultado->count(),
                'fecha_inicio' => $fechaInicio,
                'fecha_fin' => $fechaFin,
                'visitas' => $resultado->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error en getVisitasUsuario: ' . $e->getMessage());
            return [
                'total' => 0,
                'visitas' => []
            ];
        }
    }

    /**
     * Actualizar visita
     */
    public function actualizarVisita(array $datos): array
    {
        try {
            $idVisita = $datos['id_visita'];
            $idUsuario = $datos['id_usuario'];

            $visita = Visita::where('id', $idVisita)
                ->where('id_usuario', $idUsuario)
                ->first();

            if (!$visita) {
                return [
                    'estado' => 0,
                    'msg' => 'Visita no encontrada o no autorizada',
                    'visita' => null
                ];
            }

            // Actualizar campos permitidos
            if (isset($datos['efectiva'])) {
                $visita->efectiva = $datos['efectiva'];
            }

            if (isset($datos['motivo_no_efectiva'])) {
                $visita->motivo_no_efectiva = $datos['motivo_no_efectiva'];
            }

            if (isset($datos['observaciones'])) {
                $visita->observaciones = $datos['observaciones'];
            }

            if (isset($datos['estado'])) {
                $visita->estado = $datos['estado'];
            }

            $visita->save();

            return [
                'estado' => 1,
                'msg' => 'Visita actualizada exitosamente',
                'visita' => $visita->resumen
            ];

        } catch (\Exception $e) {
            Log::error('Error en actualizarVisita: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al actualizar la visita',
                'visita' => null
            ];
        }
    }

    /**
     * Validar ubicación para visita
     */
    public function validarUbicacion(int $idPos, float $latitud, float $longitud, int $tolerancia): array
    {
        try {
            $punto = Point::find($idPos);

            if (!$punto) {
                return [
                    'valido' => false,
                    'mensaje' => 'Punto no encontrado',
                    'distancia' => null
                ];
            }

            $distancia = $punto->calcularDistancia($latitud, $longitud);
            $dentroTolerancia = $punto->estaDentroDeTolerancia($latitud, $longitud, $tolerancia);

            return [
                'valido' => $dentroTolerancia,
                'mensaje' => $dentroTolerancia ? 'Ubicación válida para visita' : 'Ubicación fuera del rango permitido',
                'distancia' => round($distancia, 2),
                'tolerancia' => $tolerancia,
                'punto' => [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'latitud' => $punto->latitud,
                    'longitud' => $punto->longitud
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en validarUbicacion: ' . $e->getMessage());
            return [
                'valido' => false,
                'mensaje' => 'Error en validación',
                'distancia' => null
            ];
        }
    }

    /**
     * Obtener estadísticas de visitas
     */
    public function getEstadisticasVisitas(int $idUsuario, string $fechaInicio, string $fechaFin): array
    {
        try {
            $totalVisitas = Visita::where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin])
                ->count();

            $visitasEfectivas = Visita::where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin])
                ->where('efectiva', 1)
                ->count();

            $visitasCompletadas = Visita::where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin])
                ->where('estado', Visita::ESTADO_COMPLETADA)
                ->count();

            $visitasHoy = Visita::where('id_usuario', $idUsuario)
                ->whereDate('fecha', today())
                ->count();

            $tiempoPromedioVisita = Visita::where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin])
                ->whereNotNull('hora_inicio')
                ->whereNotNull('hora_fin')
                ->get()
                ->avg('duracion');

            return [
                'periodo' => [
                    'fecha_inicio' => $fechaInicio,
                    'fecha_fin' => $fechaFin
                ],
                'totales' => [
                    'visitas' => $totalVisitas,
                    'efectivas' => $visitasEfectivas,
                    'completadas' => $visitasCompletadas,
                    'hoy' => $visitasHoy
                ],
                'porcentajes' => [
                    'efectividad' => $totalVisitas > 0 ? round(($visitasEfectivas / $totalVisitas) * 100, 2) : 0,
                    'completitud' => $totalVisitas > 0 ? round(($visitasCompletadas / $totalVisitas) * 100, 2) : 0
                ],
                'promedios' => [
                    'tiempo_visita_minutos' => $tiempoPromedioVisita ? round($tiempoPromedioVisita, 2) : 0,
                    'visitas_por_dia' => $totalVisitas > 0 ? round($totalVisitas / max(1, (strtotime($fechaFin) - strtotime($fechaInicio)) / 86400), 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEstadisticasVisitas: ' . $e->getMessage());
            return [
                'totales' => ['visitas' => 0, 'efectivas' => 0, 'completadas' => 0, 'hoy' => 0],
                'porcentajes' => ['efectividad' => 0, 'completitud' => 0],
                'promedios' => ['tiempo_visita_minutos' => 0, 'visitas_por_dia' => 0]
            ];
        }
    }

    /**
     * Obtener visita activa
     */
    public function getVisitaActiva(int $idUsuario): ?array
    {
        try {
            $visita = Visita::with(['punto'])
                ->where('id_usuario', $idUsuario)
                ->where('estado', Visita::ESTADO_EN_PROCESO)
                ->first();

            if (!$visita) {
                return null;
            }

            return [
                'id' => $visita->id,
                'punto' => [
                    'idpos' => $visita->punto->idpos,
                    'nombre' => $visita->punto->nombre,
                    'direccion' => $visita->punto->direccion
                ],
                'fecha' => $visita->fecha->format('Y-m-d'),
                'hora_inicio' => $visita->hora_inicio->format('H:i:s'),
                'tiempo_transcurrido' => now()->diffInMinutes($visita->hora_inicio),
                'estado' => $visita->estado_nombre
            ];

        } catch (\Exception $e) {
            Log::error('Error en getVisitaActiva: ' . $e->getMessage());
            return null;
        }
    }
}
