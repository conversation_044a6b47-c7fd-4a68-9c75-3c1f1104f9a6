<?php

namespace App\Services;

use App\Models\ArchivoSubido;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Intervention\Image\Facades\Image;

class FileService
{
    /**
     * Tipos de archivo permitidos
     */
    private const TIPOS_PERMITIDOS = [
        'imagen' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'documento' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
        'video' => ['mp4', 'avi', 'mov', 'wmv'],
        'audio' => ['mp3', 'wav', 'aac', 'm4a']
    ];

    /**
     * Tamaños máximos por tipo (en bytes)
     */
    private const TAMAÑOS_MAXIMOS = [
        'imagen' => 10 * 1024 * 1024,    // 10MB
        'documento' => 50 * 1024 * 1024, // 50MB
        'video' => 100 * 1024 * 1024,    // 100MB
        'audio' => 20 * 1024 * 1024      // 20MB
    ];

    /**
     * Subir archivo
     */
    public function subirArchivo(array $datos): array
    {
        try {
            $archivo = $datos['archivo'];
            $tipo = $datos['tipo'];
            $categoria = $datos['categoria'] ?? 'general';
            $idReferencia = $datos['id_referencia'] ?? null;
            $descripcion = $datos['descripcion'] ?? null;
            $publico = $datos['publico'] ?? false;
            $idUsuario = $datos['id_usuario'];

            // Validar archivo
            $validacion = $this->validarArchivo([
                'nombre_archivo' => $archivo->getClientOriginalName(),
                'tamaño_bytes' => $archivo->getSize(),
                'tipo_mime' => $archivo->getMimeType(),
                'tipo' => $tipo
            ]);

            if (!$validacion['valido']) {
                return [
                    'estado' => 0,
                    'msg' => 'Archivo inválido: ' . implode(', ', $validacion['errores']),
                    'archivo' => null
                ];
            }

            // Generar nombre único
            $extension = $archivo->getClientOriginalExtension();
            $nombreUnico = $this->generarNombreUnico($archivo->getClientOriginalName(), $extension);

            // Determinar ruta de almacenamiento
            $rutaAlmacenamiento = $this->determinarRutaAlmacenamiento($categoria, $idReferencia, $tipo);

            // Subir archivo
            $rutaArchivo = $archivo->storeAs($rutaAlmacenamiento, $nombreUnico, 'public');

            // Guardar información en base de datos
            $archivoSubido = new ArchivoSubido();
            $archivoSubido->nombre_original = $archivo->getClientOriginalName();
            $archivoSubido->nombre_archivo = $nombreUnico;
            $archivoSubido->ruta_archivo = $rutaArchivo;
            $archivoSubido->tipo = $tipo;
            $archivoSubido->categoria = $categoria;
            $archivoSubido->id_referencia = $idReferencia;
            $archivoSubido->tamaño_bytes = $archivo->getSize();
            $archivoSubido->tipo_mime = $archivo->getMimeType();
            $archivoSubido->descripcion = $descripcion;
            $archivoSubido->publico = $publico;
            $archivoSubido->id_usuario = $idUsuario;
            $archivoSubido->fecha_subida = now();

            // Generar metadatos adicionales para imágenes
            if ($tipo === 'imagen') {
                $metadatos = $this->extraerMetadatosImagen($archivo);
                $archivoSubido->metadatos = $metadatos;
            }

            $archivoSubido->save();

            return [
                'estado' => 1,
                'msg' => 'Archivo subido exitosamente',
                'archivo' => [
                    'id' => $archivoSubido->id,
                    'nombre_original' => $archivoSubido->nombre_original,
                    'nombre_archivo' => $archivoSubido->nombre_archivo,
                    'url' => asset('storage/' . $rutaArchivo),
                    'tipo' => $archivoSubido->tipo,
                    'tamaño_bytes' => $archivoSubido->tamaño_bytes,
                    'tamaño_legible' => $this->formatearTamaño($archivoSubido->tamaño_bytes),
                    'fecha_subida' => $archivoSubido->fecha_subida->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en subirArchivo: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al subir el archivo',
                'archivo' => null
            ];
        }
    }

    /**
     * Subir múltiples archivos
     */
    public function subirMultiplesArchivos(array $datos): array
    {
        try {
            $archivos = $datos['archivos'];
            $tipo = $datos['tipo'];
            $categoria = $datos['categoria'] ?? 'general';
            $idReferencia = $datos['id_referencia'] ?? null;
            $descripcion = $datos['descripcion'] ?? null;
            $idUsuario = $datos['id_usuario'];

            $archivosSubidos = [];
            $errores = [];

            foreach ($archivos as $index => $archivo) {
                $datosArchivo = [
                    'archivo' => $archivo,
                    'tipo' => $tipo,
                    'categoria' => $categoria,
                    'id_referencia' => $idReferencia,
                    'descripcion' => $descripcion . " (archivo " . ($index + 1) . ")",
                    'id_usuario' => $idUsuario
                ];

                $resultado = $this->subirArchivo($datosArchivo);

                if ($resultado['estado'] === 1) {
                    $archivosSubidos[] = $resultado['archivo'];
                } else {
                    $errores[] = "Archivo " . ($index + 1) . ": " . $resultado['msg'];
                }
            }

            if (count($archivosSubidos) > 0) {
                $mensaje = count($archivosSubidos) . " archivos subidos exitosamente";
                if (count($errores) > 0) {
                    $mensaje .= ". Errores: " . implode(', ', $errores);
                }

                return [
                    'estado' => 1,
                    'msg' => $mensaje,
                    'archivos' => [
                        'subidos' => $archivosSubidos,
                        'total_subidos' => count($archivosSubidos),
                        'total_errores' => count($errores),
                        'errores' => $errores
                    ]
                ];
            } else {
                return [
                    'estado' => 0,
                    'msg' => 'No se pudo subir ningún archivo: ' . implode(', ', $errores),
                    'archivos' => null
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error en subirMultiplesArchivos: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al subir los archivos',
                'archivos' => null
            ];
        }
    }

    /**
     * Obtener información de archivo
     */
    public function getInfoArchivo(int $idArchivo, int $idUsuario): ?array
    {
        try {
            $archivo = ArchivoSubido::where('id', $idArchivo)
                ->where(function($query) use ($idUsuario) {
                    $query->where('id_usuario', $idUsuario)
                          ->orWhere('publico', true);
                })
                ->first();

            if (!$archivo) {
                return null;
            }

            return [
                'id' => $archivo->id,
                'nombre_original' => $archivo->nombre_original,
                'nombre_archivo' => $archivo->nombre_archivo,
                'url' => asset('storage/' . $archivo->ruta_archivo),
                'tipo' => $archivo->tipo,
                'categoria' => $archivo->categoria,
                'tamaño_bytes' => $archivo->tamaño_bytes,
                'tamaño_legible' => $this->formatearTamaño($archivo->tamaño_bytes),
                'tipo_mime' => $archivo->tipo_mime,
                'descripcion' => $archivo->descripcion,
                'publico' => $archivo->publico,
                'fecha_subida' => $archivo->fecha_subida->format('Y-m-d H:i:s'),
                'metadatos' => $archivo->metadatos,
                'propietario' => $archivo->usuario ? $archivo->usuario->nombre_completo : null
            ];

        } catch (\Exception $e) {
            Log::error('Error en getInfoArchivo: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Descargar archivo
     */
    public function descargarArchivo(int $idArchivo, int $idUsuario): ?array
    {
        try {
            $archivo = ArchivoSubido::where('id', $idArchivo)
                ->where(function($query) use ($idUsuario) {
                    $query->where('id_usuario', $idUsuario)
                          ->orWhere('publico', true);
                })
                ->first();

            if (!$archivo) {
                return null;
            }

            $rutaCompleta = storage_path('app/public/' . $archivo->ruta_archivo);

            if (!file_exists($rutaCompleta)) {
                Log::warning("Archivo no encontrado en disco: {$rutaCompleta}");
                return null;
            }

            // Registrar descarga
            $archivo->increment('descargas');

            return [
                'ruta' => $rutaCompleta,
                'nombre' => $archivo->nombre_original
            ];

        } catch (\Exception $e) {
            Log::error('Error en descargarArchivo: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Eliminar archivo
     */
    public function eliminarArchivo(int $idArchivo, int $idUsuario): array
    {
        try {
            $archivo = ArchivoSubido::where('id', $idArchivo)
                ->where('id_usuario', $idUsuario)
                ->first();

            if (!$archivo) {
                return [
                    'estado' => 0,
                    'msg' => 'Archivo no encontrado o sin permisos para eliminarlo'
                ];
            }

            // Eliminar archivo físico
            if (Storage::disk('public')->exists($archivo->ruta_archivo)) {
                Storage::disk('public')->delete($archivo->ruta_archivo);
            }

            // Eliminar registro de base de datos
            $archivo->delete();

            return [
                'estado' => 1,
                'msg' => 'Archivo eliminado exitosamente'
            ];

        } catch (\Exception $e) {
            Log::error('Error en eliminarArchivo: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al eliminar el archivo'
            ];
        }
    }

    /**
     * Listar archivos del usuario
     */
    public function listarArchivosUsuario(array $parametros): array
    {
        try {
            $idUsuario = $parametros['id_usuario'];
            $tipo = $parametros['tipo'] ?? null;
            $categoria = $parametros['categoria'] ?? null;
            $limite = $parametros['limite'] ?? 50;
            $pagina = $parametros['pagina'] ?? 1;

            $query = ArchivoSubido::where('id_usuario', $idUsuario);

            if ($tipo) {
                $query->where('tipo', $tipo);
            }

            if ($categoria) {
                $query->where('categoria', $categoria);
            }

            $total = $query->count();
            $archivos = $query->orderBy('fecha_subida', 'desc')
                ->skip(($pagina - 1) * $limite)
                ->take($limite)
                ->get();

            $resultado = $archivos->map(function($archivo) {
                return [
                    'id' => $archivo->id,
                    'nombre_original' => $archivo->nombre_original,
                    'url' => asset('storage/' . $archivo->ruta_archivo),
                    'tipo' => $archivo->tipo,
                    'categoria' => $archivo->categoria,
                    'tamaño_legible' => $this->formatearTamaño($archivo->tamaño_bytes),
                    'fecha_subida' => $archivo->fecha_subida->format('Y-m-d H:i:s'),
                    'descargas' => $archivo->descargas ?? 0
                ];
            });

            return [
                'total' => $total,
                'pagina' => $pagina,
                'limite' => $limite,
                'total_paginas' => ceil($total / $limite),
                'archivos' => $resultado->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error en listarArchivosUsuario: ' . $e->getMessage());
            return [
                'total' => 0,
                'archivos' => []
            ];
        }
    }

    /**
     * Obtener archivos por referencia
     */
    public function getArchivosPorReferencia(string $categoria, int $idReferencia, ?string $tipo = null): array
    {
        try {
            $query = ArchivoSubido::where('categoria', $categoria)
                ->where('id_referencia', $idReferencia);

            if ($tipo) {
                $query->where('tipo', $tipo);
            }

            $archivos = $query->orderBy('fecha_subida', 'desc')->get();

            $resultado = $archivos->map(function($archivo) {
                return [
                    'id' => $archivo->id,
                    'nombre_original' => $archivo->nombre_original,
                    'url' => asset('storage/' . $archivo->ruta_archivo),
                    'tipo' => $archivo->tipo,
                    'tamaño_legible' => $this->formatearTamaño($archivo->tamaño_bytes),
                    'descripcion' => $archivo->descripcion,
                    'fecha_subida' => $archivo->fecha_subida->format('Y-m-d H:i:s'),
                    'propietario' => $archivo->usuario ? $archivo->usuario->nombre_completo : null
                ];
            });

            return [
                'categoria' => $categoria,
                'id_referencia' => $idReferencia,
                'total' => $resultado->count(),
                'archivos' => $resultado->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error en getArchivosPorReferencia: ' . $e->getMessage());
            return [
                'total' => 0,
                'archivos' => []
            ];
        }
    }

    /**
     * Generar URL temporal
     */
    public function generarUrlTemporal(int $idArchivo, int $duracionMinutos, int $idUsuario): ?array
    {
        try {
            $archivo = ArchivoSubido::where('id', $idArchivo)
                ->where(function($query) use ($idUsuario) {
                    $query->where('id_usuario', $idUsuario)
                          ->orWhere('publico', true);
                })
                ->first();

            if (!$archivo) {
                return null;
            }

            // Generar token temporal
            $token = bin2hex(random_bytes(32));
            $expiraEn = now()->addMinutes($duracionMinutos);

            // Guardar token temporal (en producción, usar cache o tabla temporal)
            cache()->put("temp_file_{$token}", [
                'id_archivo' => $idArchivo,
                'id_usuario' => $idUsuario,
                'expira_en' => $expiraEn
            ], $expiraEn);

            $url = route('archivo.temporal', ['token' => $token]);

            return [
                'url' => $url,
                'expira_en' => $expiraEn->format('Y-m-d H:i:s'),
                'token' => $token
            ];

        } catch (\Exception $e) {
            Log::error('Error en generarUrlTemporal: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener estadísticas de archivos
     */
    public function getEstadisticasArchivos(int $idUsuario): array
    {
        try {
            $archivos = ArchivoSubido::where('id_usuario', $idUsuario)->get();

            $estadisticas = [
                'total_archivos' => $archivos->count(),
                'tamaño_total_bytes' => $archivos->sum('tamaño_bytes'),
                'tamaño_total_legible' => $this->formatearTamaño($archivos->sum('tamaño_bytes')),
                'por_tipo' => [],
                'por_categoria' => [],
                'archivos_recientes' => [],
                'mas_descargados' => []
            ];

            // Estadísticas por tipo
            $porTipo = $archivos->groupBy('tipo');
            foreach ($porTipo as $tipo => $archivosDelTipo) {
                $estadisticas['por_tipo'][$tipo] = [
                    'cantidad' => $archivosDelTipo->count(),
                    'tamaño_total' => $this->formatearTamaño($archivosDelTipo->sum('tamaño_bytes'))
                ];
            }

            // Estadísticas por categoría
            $porCategoria = $archivos->groupBy('categoria');
            foreach ($porCategoria as $categoria => $archivosDeLaCategoria) {
                $estadisticas['por_categoria'][$categoria] = [
                    'cantidad' => $archivosDeLaCategoria->count(),
                    'tamaño_total' => $this->formatearTamaño($archivosDeLaCategoria->sum('tamaño_bytes'))
                ];
            }

            // Archivos recientes
            $estadisticas['archivos_recientes'] = $archivos
                ->sortByDesc('fecha_subida')
                ->take(5)
                ->map(function($archivo) {
                    return [
                        'nombre' => $archivo->nombre_original,
                        'tipo' => $archivo->tipo,
                        'fecha_subida' => $archivo->fecha_subida->format('Y-m-d H:i:s')
                    ];
                })
                ->values()
                ->toArray();

            // Más descargados
            $estadisticas['mas_descargados'] = $archivos
                ->sortByDesc('descargas')
                ->take(5)
                ->map(function($archivo) {
                    return [
                        'nombre' => $archivo->nombre_original,
                        'descargas' => $archivo->descargas ?? 0,
                        'tipo' => $archivo->tipo
                    ];
                })
                ->values()
                ->toArray();

            return $estadisticas;

        } catch (\Exception $e) {
            Log::error('Error en getEstadisticasArchivos: ' . $e->getMessage());
            return [
                'total_archivos' => 0,
                'tamaño_total_bytes' => 0
            ];
        }
    }

    /**
     * Comprimir imagen
     */
    public function comprimirImagen(array $datos): array
    {
        try {
            $imagen = $datos['imagen'];
            $calidad = $datos['calidad'] ?? 80;
            $anchoMaximo = $datos['ancho_maximo'] ?? 1920;
            $altoMaximo = $datos['alto_maximo'] ?? 1080;
            $idUsuario = $datos['id_usuario'];

            // Validar que es una imagen
            if (!in_array($imagen->getClientOriginalExtension(), self::TIPOS_PERMITIDOS['imagen'])) {
                return [
                    'estado' => 0,
                    'msg' => 'El archivo no es una imagen válida',
                    'imagen' => null
                ];
            }

            // Procesar imagen con Intervention Image
            $img = Image::make($imagen);

            // Redimensionar si es necesario
            if ($img->width() > $anchoMaximo || $img->height() > $altoMaximo) {
                $img->resize($anchoMaximo, $altoMaximo, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }

            // Generar nombre único
            $extension = $imagen->getClientOriginalExtension();
            $nombreComprimido = 'compressed_' . time() . '_' . uniqid() . '.' . $extension;

            // Guardar imagen comprimida
            $rutaComprimida = 'imagenes/comprimidas/' . $nombreComprimido;
            $rutaCompleta = storage_path('app/public/' . $rutaComprimida);

            // Crear directorio si no existe
            $directorio = dirname($rutaCompleta);
            if (!file_exists($directorio)) {
                mkdir($directorio, 0755, true);
            }

            // Guardar con calidad especificada
            $img->save($rutaCompleta, $calidad);

            // Guardar información en base de datos
            $archivoComprimido = new ArchivoSubido();
            $archivoComprimido->nombre_original = 'compressed_' . $imagen->getClientOriginalName();
            $archivoComprimido->nombre_archivo = $nombreComprimido;
            $archivoComprimido->ruta_archivo = $rutaComprimida;
            $archivoComprimido->tipo = 'imagen';
            $archivoComprimido->categoria = 'comprimida';
            $archivoComprimido->tamaño_bytes = filesize($rutaCompleta);
            $archivoComprimido->tipo_mime = $imagen->getMimeType();
            $archivoComprimido->descripcion = "Imagen comprimida (calidad: {$calidad}%)";
            $archivoComprimido->id_usuario = $idUsuario;
            $archivoComprimido->fecha_subida = now();
            $archivoComprimido->save();

            $reduccionPorcentaje = round((1 - ($archivoComprimido->tamaño_bytes / $imagen->getSize())) * 100, 2);

            return [
                'estado' => 1,
                'msg' => 'Imagen comprimida exitosamente',
                'imagen' => [
                    'id' => $archivoComprimido->id,
                    'url' => asset('storage/' . $rutaComprimida),
                    'tamaño_original' => $this->formatearTamaño($imagen->getSize()),
                    'tamaño_comprimido' => $this->formatearTamaño($archivoComprimido->tamaño_bytes),
                    'reduccion_porcentaje' => $reduccionPorcentaje,
                    'calidad' => $calidad,
                    'dimensiones' => [
                        'ancho' => $img->width(),
                        'alto' => $img->height()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en comprimirImagen: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al comprimir la imagen',
                'imagen' => null
            ];
        }
    }

    /**
     * Validar archivo
     */
    public function validarArchivo(array $datos): array
    {
        $nombreArchivo = $datos['nombre_archivo'];
        $tamañoBytes = $datos['tamaño_bytes'];
        $tipoMime = $datos['tipo_mime'];
        $tipo = $datos['tipo'];

        $errores = [];

        // Validar extensión
        $extension = strtolower(pathinfo($nombreArchivo, PATHINFO_EXTENSION));
        if (!in_array($extension, self::TIPOS_PERMITIDOS[$tipo] ?? [])) {
            $errores[] = "Extensión no permitida para tipo {$tipo}. Permitidas: " . implode(', ', self::TIPOS_PERMITIDOS[$tipo] ?? []);
        }

        // Validar tamaño
        $tamañoMaximo = self::TAMAÑOS_MAXIMOS[$tipo] ?? self::TAMAÑOS_MAXIMOS['documento'];
        if ($tamañoBytes > $tamañoMaximo) {
            $errores[] = "Archivo demasiado grande. Máximo permitido: " . $this->formatearTamaño($tamañoMaximo);
        }

        // Validar tipo MIME básico
        $tiposMimePermitidos = [
            'imagen' => ['image/'],
            'documento' => ['application/', 'text/'],
            'video' => ['video/'],
            'audio' => ['audio/']
        ];

        $mimeValido = false;
        foreach ($tiposMimePermitidos[$tipo] ?? [] as $mimePrefix) {
            if (strpos($tipoMime, $mimePrefix) === 0) {
                $mimeValido = true;
                break;
            }
        }

        if (!$mimeValido) {
            $errores[] = "Tipo MIME no válido para {$tipo}: {$tipoMime}";
        }

        return [
            'valido' => empty($errores),
            'errores' => $errores,
            'tamaño_legible' => $this->formatearTamaño($tamañoBytes),
            'extension' => $extension
        ];
    }

    // Métodos auxiliares privados

    private function generarNombreUnico(string $nombreOriginal, string $extension): string
    {
        $nombreBase = pathinfo($nombreOriginal, PATHINFO_FILENAME);
        $nombreLimpio = preg_replace('/[^a-zA-Z0-9_-]/', '_', $nombreBase);
        return time() . '_' . uniqid() . '_' . $nombreLimpio . '.' . $extension;
    }

    private function determinarRutaAlmacenamiento(string $categoria, ?int $idReferencia, string $tipo): string
    {
        $ruta = $tipo . 's/' . $categoria;
        
        if ($idReferencia) {
            $ruta .= '/' . $idReferencia;
        }
        
        return $ruta;
    }

    private function extraerMetadatosImagen(UploadedFile $imagen): array
    {
        try {
            $img = Image::make($imagen);
            
            return [
                'ancho' => $img->width(),
                'alto' => $img->height(),
                'formato' => $img->mime(),
                'tamaño_original' => $imagen->getSize()
            ];
        } catch (\Exception $e) {
            return [];
        }
    }

    private function formatearTamaño(int $bytes): string
    {
        $unidades = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($unidades) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $unidades[$pow];
    }
}
