<?php

namespace App\Services;

use App\Models\DetalleInventario;
use App\Models\ReferenciaProducto;
use App\Models\Point;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryService
{
    /**
     * Obtener detalle de inventario de un punto
     */
    public function getDetalleInventario(array $parametros): array
    {
        try {
            $idpos = $parametros['idpos'];
            $categoria = $parametros['categoria'] ?? null;
            $estado = $parametros['estado'] ?? null;
            $fechaInicio = $parametros['fecha_inicio'] ?? null;
            $fechaFin = $parametros['fecha_fin'] ?? null;

            $query = DetalleInventario::with(['referencia', 'punto'])
                ->where('id_pos', $idpos);

            // Filtros opcionales
            if ($categoria) {
                $query->whereHas('referencia', function($q) use ($categoria) {
                    $q->where('categoria', $categoria);
                });
            }

            if ($estado) {
                $query->where('estado', $estado);
            }

            if ($fechaInicio && $fechaFin) {
                $query->whereBetween('fecha_ingreso', [$fechaInicio, $fechaFin]);
            }

            $inventario = $query->orderBy('fecha_ingreso', 'desc')->get();

            $resultado = [];
            $resumen = [
                'total_productos' => 0,
                'valor_total' => 0,
                'por_estado' => []
            ];

            foreach ($inventario as $item) {
                $resultado[] = [
                    'id' => $item->id,
                    'serial' => $item->serial,
                    'descripcion' => $item->descripcion,
                    'referencia' => $item->referencia ? [
                        'id' => $item->referencia->id,
                        'descripcion' => $item->referencia->descripcion,
                        'categoria' => $item->referencia->categoria,
                        'codigo' => $item->referencia->codigo
                    ] : null,
                    'cantidad' => $item->cantidad,
                    'precio' => $item->precio,
                    'valor_total' => $item->valor_total,
                    'estado' => $item->estado,
                    'fecha_ingreso' => $item->fecha_ingreso ? $item->fecha_ingreso->format('Y-m-d') : null,
                    'fecha_venta' => $item->fecha_venta ? $item->fecha_venta->format('Y-m-d') : null,
                    'dias_inventario' => $item->dias_en_inventario,
                    'observaciones' => $item->observaciones
                ];

                // Actualizar resumen
                $resumen['total_productos'] += $item->cantidad;
                $resumen['valor_total'] += $item->valor_total;
                
                if (!isset($resumen['por_estado'][$item->estado])) {
                    $resumen['por_estado'][$item->estado] = 0;
                }
                $resumen['por_estado'][$item->estado] += $item->cantidad;
            }

            return [
                'punto_id' => $idpos,
                'total_items' => count($resultado),
                'resumen' => $resumen,
                'inventario' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en getDetalleInventario: ' . $e->getMessage());
            return [
                'punto_id' => $parametros['idpos'] ?? null,
                'total_items' => 0,
                'resumen' => ['total_productos' => 0, 'valor_total' => 0],
                'inventario' => []
            ];
        }
    }

    /**
     * Obtener información de un producto específico
     */
    public function getInfoProducto(array $parametros): ?array
    {
        try {
            $serial = $parametros['serial'] ?? null;
            $idReferencia = $parametros['id_referencia'] ?? null;
            $codigoBarras = $parametros['codigo_barras'] ?? null;

            $query = DetalleInventario::with(['referencia', 'punto']);

            if ($serial) {
                $query->where('serial', $serial);
            } elseif ($idReferencia) {
                $query->where('id_referencia', $idReferencia);
            } elseif ($codigoBarras) {
                $query->whereHas('referencia', function($q) use ($codigoBarras) {
                    $q->where('codigo_barras', $codigoBarras);
                });
            } else {
                return null;
            }

            $producto = $query->first();

            if (!$producto) {
                return null;
            }

            return [
                'id' => $producto->id,
                'serial' => $producto->serial,
                'descripcion' => $producto->descripcion,
                'referencia' => $producto->referencia ? [
                    'id' => $producto->referencia->id,
                    'descripcion' => $producto->referencia->descripcion,
                    'categoria' => $producto->referencia->categoria,
                    'codigo' => $producto->referencia->codigo,
                    'codigo_barras' => $producto->referencia->codigo_barras ?? null,
                    'precio_sugerido' => $producto->referencia->precio_sugerido ?? null
                ] : null,
                'punto' => $producto->punto ? [
                    'idpos' => $producto->punto->idpos,
                    'nombre' => $producto->punto->nombre,
                    'direccion' => $producto->punto->direccion
                ] : null,
                'cantidad' => $producto->cantidad,
                'precio' => $producto->precio,
                'valor_total' => $producto->valor_total,
                'estado' => $producto->estado,
                'esta_disponible' => $producto->esta_disponible,
                'fecha_ingreso' => $producto->fecha_ingreso ? $producto->fecha_ingreso->format('Y-m-d H:i:s') : null,
                'fecha_venta' => $producto->fecha_venta ? $producto->fecha_venta->format('Y-m-d H:i:s') : null,
                'dias_inventario' => $producto->dias_en_inventario,
                'observaciones' => $producto->observaciones
            ];

        } catch (\Exception $e) {
            Log::error('Error en getInfoProducto: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Registrar movimiento de inventario
     */
    public function registrarMovimiento(array $datos): array
    {
        try {
            DB::beginTransaction();

            $idPos = $datos['id_pos'];
            $tipoMovimiento = $datos['tipo_movimiento'];
            $productos = $datos['productos'];
            $idUsuario = $datos['id_usuario'];
            $observaciones = $datos['observaciones'] ?? null;

            // Verificar que el punto existe
            $punto = Point::find($idPos);
            if (!$punto) {
                return [
                    'estado' => 0,
                    'msg' => 'Punto de venta no encontrado',
                    'movimiento' => null
                ];
            }

            $productosMovimiento = [];

            foreach ($productos as $productoData) {
                $idReferencia = $productoData['id_referencia'];
                $serial = $productoData['serial'] ?? null;
                $cantidad = $productoData['cantidad'];
                $precio = $productoData['precio'] ?? 0;

                // Verificar que la referencia existe
                $referencia = ReferenciaProducto::find($idReferencia);
                if (!$referencia) {
                    DB::rollBack();
                    return [
                        'estado' => 0,
                        'msg' => "Referencia de producto no encontrada: ID {$idReferencia}",
                        'movimiento' => null
                    ];
                }

                switch ($tipoMovimiento) {
                    case 'INGRESO':
                        $detalle = new DetalleInventario();
                        $detalle->id_pos = $idPos;
                        $detalle->id_referencia = $idReferencia;
                        $detalle->serial = $serial;
                        $detalle->descripcion = $referencia->descripcion;
                        $detalle->cantidad = $cantidad;
                        $detalle->precio = $precio ?: $referencia->precio_sugerido;
                        $detalle->estado = DetalleInventario::ESTADO_DISPONIBLE;
                        $detalle->fecha_ingreso = now();
                        $detalle->observaciones = $observaciones;
                        $detalle->save();

                        $productosMovimiento[] = $detalle;
                        break;

                    case 'VENTA':
                        // Buscar producto disponible
                        $producto = DetalleInventario::where('id_pos', $idPos)
                            ->where('id_referencia', $idReferencia)
                            ->where('estado', DetalleInventario::ESTADO_DISPONIBLE)
                            ->when($serial, function($q) use ($serial) {
                                return $q->where('serial', $serial);
                            })
                            ->first();

                        if (!$producto) {
                            DB::rollBack();
                            return [
                                'estado' => 0,
                                'msg' => "Producto no disponible para venta: {$referencia->descripcion}",
                                'movimiento' => null
                            ];
                        }

                        $producto->marcarComoVendido();
                        if ($precio > 0) {
                            $producto->precio = $precio;
                            $producto->save();
                        }

                        $productosMovimiento[] = $producto;
                        break;

                    case 'DEVOLUCION':
                        // Buscar producto vendido
                        $producto = DetalleInventario::where('id_pos', $idPos)
                            ->where('id_referencia', $idReferencia)
                            ->where('estado', DetalleInventario::ESTADO_VENDIDO)
                            ->when($serial, function($q) use ($serial) {
                                return $q->where('serial', $serial);
                            })
                            ->first();

                        if (!$producto) {
                            DB::rollBack();
                            return [
                                'estado' => 0,
                                'msg' => "Producto no encontrado para devolución: {$referencia->descripcion}",
                                'movimiento' => null
                            ];
                        }

                        $producto->devolverAlInventario($observaciones);
                        $productosMovimiento[] = $producto;
                        break;

                    case 'AJUSTE':
                        // Buscar producto para ajuste
                        $producto = DetalleInventario::where('id_pos', $idPos)
                            ->where('id_referencia', $idReferencia)
                            ->when($serial, function($q) use ($serial) {
                                return $q->where('serial', $serial);
                            })
                            ->first();

                        if (!$producto) {
                            DB::rollBack();
                            return [
                                'estado' => 0,
                                'msg' => "Producto no encontrado para ajuste: {$referencia->descripcion}",
                                'movimiento' => null
                            ];
                        }

                        $producto->cantidad = $cantidad;
                        $producto->observaciones = $observaciones;
                        $producto->save();

                        $productosMovimiento[] = $producto;
                        break;
                }
            }

            DB::commit();

            return [
                'estado' => 1,
                'msg' => 'Movimiento de inventario registrado exitosamente',
                'movimiento' => [
                    'tipo' => $tipoMovimiento,
                    'punto_id' => $idPos,
                    'total_productos' => count($productosMovimiento),
                    'fecha' => now()->format('Y-m-d H:i:s'),
                    'productos' => array_map(function($producto) {
                        return [
                            'id' => $producto->id,
                            'descripcion' => $producto->descripcion,
                            'serial' => $producto->serial,
                            'cantidad' => $producto->cantidad,
                            'estado' => $producto->estado
                        ];
                    }, $productosMovimiento)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error en registrarMovimiento: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al registrar el movimiento de inventario',
                'movimiento' => null
            ];
        }
    }

    /**
     * Buscar productos en inventario
     */
    public function buscarProductos(array $parametros): array
    {
        try {
            $filtro = $parametros['filtro'] ?? '';
            $tipoFiltro = $parametros['tipo_filtro'] ?? 'descripcion';
            $categoria = $parametros['categoria'] ?? null;
            $estado = $parametros['estado'] ?? null;
            $idPos = $parametros['id_pos'] ?? null;
            $limite = $parametros['limite'] ?? 50;

            $query = DetalleInventario::with(['referencia', 'punto']);

            // Filtro principal
            if (!empty($filtro)) {
                switch ($tipoFiltro) {
                    case 'serial':
                        $query->where('serial', 'like', '%' . $filtro . '%');
                        break;
                    case 'referencia':
                        $query->whereHas('referencia', function($q) use ($filtro) {
                            $q->where('codigo', 'like', '%' . $filtro . '%')
                              ->orWhere('descripcion', 'like', '%' . $filtro . '%');
                        });
                        break;
                    default: // descripcion
                        $query->where('descripcion', 'like', '%' . $filtro . '%');
                        break;
                }
            }

            // Filtros adicionales
            if ($categoria) {
                $query->whereHas('referencia', function($q) use ($categoria) {
                    $q->where('categoria', $categoria);
                });
            }

            if ($estado) {
                $query->where('estado', $estado);
            }

            if ($idPos) {
                $query->where('id_pos', $idPos);
            }

            $productos = $query->limit($limite)->get();

            $resultado = $productos->map(function($producto) {
                return [
                    'id' => $producto->id,
                    'serial' => $producto->serial,
                    'descripcion' => $producto->descripcion,
                    'referencia' => $producto->referencia ? $producto->referencia->descripcion : null,
                    'categoria' => $producto->referencia ? $producto->referencia->categoria : null,
                    'punto' => $producto->punto ? $producto->punto->nombre : null,
                    'cantidad' => $producto->cantidad,
                    'precio' => $producto->precio,
                    'estado' => $producto->estado,
                    'fecha_ingreso' => $producto->fecha_ingreso ? $producto->fecha_ingreso->format('Y-m-d') : null
                ];
            });

            return [
                'total' => $resultado->count(),
                'filtro_aplicado' => $filtro,
                'tipo_filtro' => $tipoFiltro,
                'productos' => $resultado->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error en buscarProductos: ' . $e->getMessage());
            return [
                'total' => 0,
                'productos' => []
            ];
        }
    }

    /**
     * Actualizar estado de producto
     */
    public function actualizarEstadoProducto(array $datos): array
    {
        try {
            $idDetalle = $datos['id_detalle'];
            $nuevoEstado = $datos['nuevo_estado'];
            $observaciones = $datos['observaciones'] ?? null;
            $precioVenta = $datos['precio_venta'] ?? null;

            $producto = DetalleInventario::find($idDetalle);

            if (!$producto) {
                return [
                    'estado' => 0,
                    'msg' => 'Producto no encontrado',
                    'producto' => null
                ];
            }

            $estadoAnterior = $producto->estado;

            switch ($nuevoEstado) {
                case DetalleInventario::ESTADO_VENDIDO:
                    $producto->marcarComoVendido();
                    if ($precioVenta) {
                        $producto->precio = $precioVenta;
                        $producto->save();
                    }
                    break;

                case DetalleInventario::ESTADO_DEFECTUOSO:
                    $producto->marcarComoDefectuoso($observaciones);
                    break;

                case DetalleInventario::ESTADO_DISPONIBLE:
                    $producto->devolverAlInventario($observaciones);
                    break;

                default:
                    $producto->estado = $nuevoEstado;
                    $producto->observaciones = $observaciones;
                    $producto->save();
                    break;
            }

            return [
                'estado' => 1,
                'msg' => "Estado actualizado de {$estadoAnterior} a {$nuevoEstado}",
                'producto' => [
                    'id' => $producto->id,
                    'descripcion' => $producto->descripcion,
                    'serial' => $producto->serial,
                    'estado_anterior' => $estadoAnterior,
                    'estado_actual' => $producto->estado,
                    'fecha_actualizacion' => now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en actualizarEstadoProducto: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al actualizar el estado del producto',
                'producto' => null
            ];
        }
    }

    /**
     * Obtener referencias de productos
     */
    public function getReferenciasProductos(?string $categoria = null, bool $activas = true): array
    {
        try {
            $query = ReferenciaProducto::query();

            if ($categoria) {
                $query->where('categoria', $categoria);
            }

            if ($activas) {
                $query->where('activo', true);
            }

            $referencias = $query->orderBy('descripcion')->get();

            return $referencias->map(function($referencia) {
                return [
                    'id' => $referencia->id,
                    'codigo' => $referencia->codigo,
                    'descripcion' => $referencia->descripcion,
                    'categoria' => $referencia->categoria,
                    'precio_sugerido' => $referencia->precio_sugerido,
                    'codigo_barras' => $referencia->codigo_barras,
                    'activo' => $referencia->activo
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Error en getReferenciasProductos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener estadísticas de inventario
     */
    public function getEstadisticasInventario(?int $idPos = null, string $fechaInicio, string $fechaFin): array
    {
        try {
            $query = DetalleInventario::query();

            if ($idPos) {
                $query->where('id_pos', $idPos);
            }

            $query->whereBetween('fecha_ingreso', [$fechaInicio, $fechaFin]);

            $inventario = $query->get();

            $estadisticas = [
                'periodo' => [
                    'fecha_inicio' => $fechaInicio,
                    'fecha_fin' => $fechaFin
                ],
                'totales' => [
                    'productos' => $inventario->count(),
                    'valor_total' => $inventario->sum('valor_total'),
                    'disponibles' => $inventario->where('estado', DetalleInventario::ESTADO_DISPONIBLE)->count(),
                    'vendidos' => $inventario->where('estado', DetalleInventario::ESTADO_VENDIDO)->count(),
                    'defectuosos' => $inventario->where('estado', DetalleInventario::ESTADO_DEFECTUOSO)->count()
                ],
                'por_categoria' => [],
                'rotacion_promedio' => 0
            ];

            // Estadísticas por categoría
            $porCategoria = $inventario->groupBy(function($item) {
                return $item->referencia ? $item->referencia->categoria : 'Sin categoría';
            });

            foreach ($porCategoria as $categoria => $productos) {
                $estadisticas['por_categoria'][$categoria] = [
                    'total' => $productos->count(),
                    'valor' => $productos->sum('valor_total'),
                    'disponibles' => $productos->where('estado', DetalleInventario::ESTADO_DISPONIBLE)->count(),
                    'vendidos' => $productos->where('estado', DetalleInventario::ESTADO_VENDIDO)->count()
                ];
            }

            // Calcular rotación promedio si hay un punto específico
            if ($idPos) {
                $estadisticas['rotacion_promedio'] = DetalleInventario::calcularRotacion($idPos, 30);
            }

            return $estadisticas;

        } catch (\Exception $e) {
            Log::error('Error en getEstadisticasInventario: ' . $e->getMessage());
            return [
                'totales' => ['productos' => 0, 'valor_total' => 0],
                'por_categoria' => [],
                'rotacion_promedio' => 0
            ];
        }
    }

    /**
     * Calcular rotación de inventario
     */
    public function calcularRotacion(int $idPos, int $periodoDias): array
    {
        try {
            $rotacion = DetalleInventario::calcularRotacion($idPos, $periodoDias);

            $fechaInicio = now()->subDays($periodoDias);
            
            $inventarioPromedio = DetalleInventario::where('id_pos', $idPos)
                ->whereBetween('fecha_ingreso', [$fechaInicio, now()])
                ->avg('cantidad');
                
            $ventasEnPeriodo = DetalleInventario::where('id_pos', $idPos)
                ->where('estado', DetalleInventario::ESTADO_VENDIDO)
                ->whereBetween('fecha_venta', [$fechaInicio, now()])
                ->sum('cantidad');

            return [
                'punto_id' => $idPos,
                'periodo_dias' => $periodoDias,
                'rotacion' => $rotacion,
                'inventario_promedio' => round($inventarioPromedio, 2),
                'ventas_periodo' => $ventasEnPeriodo,
                'fecha_calculo' => now()->format('Y-m-d H:i:s'),
                'interpretacion' => $this->interpretarRotacion($rotacion)
            ];

        } catch (\Exception $e) {
            Log::error('Error en calcularRotacion: ' . $e->getMessage());
            return [
                'punto_id' => $idPos,
                'rotacion' => 0,
                'error' => 'Error en el cálculo'
            ];
        }
    }

    /**
     * Interpretar valor de rotación
     */
    private function interpretarRotacion(float $rotacion): string
    {
        if ($rotacion >= 2.0) {
            return 'Excelente - Rotación muy alta';
        } elseif ($rotacion >= 1.0) {
            return 'Buena - Rotación adecuada';
        } elseif ($rotacion >= 0.5) {
            return 'Regular - Rotación baja';
        } else {
            return 'Mala - Rotación muy baja';
        }
    }

    /**
     * Generar reporte de inventario
     */
    public function generarReporte(array $parametros): array
    {
        try {
            $tipoReporte = $parametros['tipo_reporte'];
            $fechaInicio = $parametros['fecha_inicio'];
            $fechaFin = $parametros['fecha_fin'];
            $idPos = $parametros['id_pos'] ?? null;
            $formato = $parametros['formato'] ?? 'JSON';

            switch ($tipoReporte) {
                case 'STOCK':
                    $datos = $this->generarReporteStock($idPos, $fechaInicio, $fechaFin);
                    break;
                case 'MOVIMIENTOS':
                    $datos = $this->generarReporteMovimientos($idPos, $fechaInicio, $fechaFin);
                    break;
                case 'VENTAS':
                    $datos = $this->generarReporteVentas($idPos, $fechaInicio, $fechaFin);
                    break;
                case 'ROTACION':
                    $datos = $this->generarReporteRotacion($idPos, $fechaInicio, $fechaFin);
                    break;
                default:
                    return [
                        'estado' => 0,
                        'msg' => 'Tipo de reporte no válido',
                        'datos' => null
                    ];
            }

            return [
                'estado' => 1,
                'msg' => 'Reporte generado exitosamente',
                'datos' => [
                    'tipo' => $tipoReporte,
                    'formato' => $formato,
                    'fecha_generacion' => now()->format('Y-m-d H:i:s'),
                    'periodo' => [
                        'inicio' => $fechaInicio,
                        'fin' => $fechaFin
                    ],
                    'contenido' => $datos
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en generarReporte: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al generar el reporte',
                'datos' => null
            ];
        }
    }

    /**
     * Generar reporte de stock
     */
    private function generarReporteStock(?int $idPos, string $fechaInicio, string $fechaFin): array
    {
        $query = DetalleInventario::with(['referencia', 'punto']);

        if ($idPos) {
            $query->where('id_pos', $idPos);
        }

        $query->whereBetween('fecha_ingreso', [$fechaInicio, $fechaFin]);

        $inventario = $query->get();

        return [
            'resumen' => [
                'total_items' => $inventario->count(),
                'valor_total' => $inventario->sum('valor_total'),
                'por_estado' => $inventario->groupBy('estado')->map->count()
            ],
            'detalle' => $inventario->map(function($item) {
                return $item->info_completa;
            })
        ];
    }

    /**
     * Generar reporte de movimientos
     */
    private function generarReporteMovimientos(?int $idPos, string $fechaInicio, string $fechaFin): array
    {
        // Este sería un reporte más complejo que requeriría una tabla de movimientos
        // Por ahora, simulamos con los datos disponibles
        return [
            'mensaje' => 'Reporte de movimientos - Funcionalidad en desarrollo',
            'periodo' => ['inicio' => $fechaInicio, 'fin' => $fechaFin]
        ];
    }

    /**
     * Generar reporte de ventas
     */
    private function generarReporteVentas(?int $idPos, string $fechaInicio, string $fechaFin): array
    {
        $query = DetalleInventario::with(['referencia', 'punto'])
            ->where('estado', DetalleInventario::ESTADO_VENDIDO);

        if ($idPos) {
            $query->where('id_pos', $idPos);
        }

        $query->whereBetween('fecha_venta', [$fechaInicio, $fechaFin]);

        $ventas = $query->get();

        return [
            'resumen' => [
                'total_ventas' => $ventas->count(),
                'valor_total' => $ventas->sum('valor_total'),
                'promedio_venta' => $ventas->avg('precio')
            ],
            'detalle' => $ventas->map(function($venta) {
                return [
                    'producto' => $venta->descripcion,
                    'serial' => $venta->serial,
                    'precio' => $venta->precio,
                    'fecha_venta' => $venta->fecha_venta->format('Y-m-d H:i:s'),
                    'punto' => $venta->punto ? $venta->punto->nombre : null
                ];
            })
        ];
    }

    /**
     * Generar reporte de rotación
     */
    private function generarReporteRotacion(?int $idPos, string $fechaInicio, string $fechaFin): array
    {
        if (!$idPos) {
            return ['mensaje' => 'Reporte de rotación requiere un punto específico'];
        }

        $periodoDias = now()->parse($fechaInicio)->diffInDays(now()->parse($fechaFin));
        $rotacion = $this->calcularRotacion($idPos, $periodoDias);

        return $rotacion;
    }
}
