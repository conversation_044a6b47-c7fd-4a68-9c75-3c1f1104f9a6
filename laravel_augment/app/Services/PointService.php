<?php

namespace App\Services;

use App\Models\Point;
use App\Models\InfoPos;
use App\Models\Visita;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PointService
{
    /**
     * Buscar puntos de venta con filtros
     */
    public function buscarPuntos(array $parametros): array
    {
        try {
            $query = Point::with(['infoPos', 'agente'])
                ->join('info_pos', 'puntos.idpos', '=', 'info_pos.idpos');

            // Filtro por nombre
            if (!empty($parametros['nombre'])) {
                $query->where(function($q) use ($parametros) {
                    $q->where('puntos.nombre', 'like', '%' . $parametros['nombre'] . '%')
                      ->orWhere('info_pos.nombre', 'like', '%' . $parametros['nombre'] . '%')
                      ->orWhere('info_pos.nombre_propietario', 'like', '%' . $parametros['nombre'] . '%');
                });
            }

            // Filtro por ubicación (radio)
            if (!empty($parametros['latitud']) && !empty($parametros['longitud'])) {
                $latitud = $parametros['latitud'];
                $longitud = $parametros['longitud'];
                $radio = $parametros['radio'] ?? 5000; // 5km por defecto

                $query->whereRaw("
                    (6371000 * acos(
                        cos(radians(?)) * cos(radians(puntos.latitud)) * 
                        cos(radians(puntos.longitud) - radians(?)) + 
                        sin(radians(?)) * sin(radians(puntos.latitud))
                    )) <= ?
                ", [$latitud, $longitud, $latitud, $radio]);
            }

            // Filtros adicionales
            if (!empty($parametros['departamento'])) {
                $query->whereHas('infoPos.departamento', function($q) use ($parametros) {
                    $q->where('descripcion', 'like', '%' . $parametros['departamento'] . '%');
                });
            }

            if (!empty($parametros['municipio'])) {
                $query->whereHas('infoPos.municipio', function($q) use ($parametros) {
                    $q->where('descripcion', 'like', '%' . $parametros['municipio'] . '%');
                });
            }

            if (!empty($parametros['categoria'])) {
                $query->where('info_pos.categoria', $parametros['categoria']);
            }

            if (!empty($parametros['estado_comercial'])) {
                $query->where('info_pos.estado_comercial', $parametros['estado_comercial']);
            }

            if (!empty($parametros['circuito'])) {
                $query->where('info_pos.circuito', $parametros['circuito']);
            }

            if (!empty($parametros['ruta'])) {
                $query->where('info_pos.ruta', $parametros['ruta']);
            }

            // Límite de resultados
            $limite = $parametros['limite'] ?? 20;
            $puntos = $query->limit($limite)->get();

            $resultado = [];
            foreach ($puntos as $punto) {
                $distancia = null;
                if (!empty($parametros['latitud']) && !empty($parametros['longitud'])) {
                    $distancia = $punto->calcularDistancia($parametros['latitud'], $parametros['longitud']);
                }

                $resultado[] = [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'direccion' => $punto->direccion,
                    'latitud' => $punto->latitud,
                    'longitud' => $punto->longitud,
                    'efectiva' => $punto->efectiva,
                    'cantidad_visitas' => $punto->cantidad_visitas,
                    'distancia_metros' => $distancia,
                    'info_pos' => $punto->infoPos ? [
                        'nombre_propietario' => $punto->infoPos->nombre_propietario,
                        'telefono' => $punto->infoPos->telefono,
                        'categoria' => $punto->infoPos->categoria,
                        'estado_comercial' => $punto->infoPos->estado_comercial,
                        'circuito' => $punto->infoPos->circuito,
                        'ruta' => $punto->infoPos->ruta
                    ] : null
                ];
            }

            return [
                'total' => count($resultado),
                'limite' => $limite,
                'puntos' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en buscarPuntos: ' . $e->getMessage());
            return [
                'total' => 0,
                'limite' => 0,
                'puntos' => []
            ];
        }
    }

    /**
     * Obtener puntos visitados por un usuario
     */
    public function getPuntosVisitados(array $parametros): array
    {
        try {
            $idUsuario = $parametros['id_usuario'];
            $fechaInicio = $parametros['fecha_inicio'] ?? date('Y-m-d', strtotime('-30 days'));
            $fechaFin = $parametros['fecha_fin'] ?? date('Y-m-d');
            $limite = $parametros['limite'] ?? 50;

            $visitas = Visita::with(['punto.infoPos'])
                ->where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin])
                ->orderBy('fecha', 'desc')
                ->limit($limite)
                ->get();

            $resultado = [];
            foreach ($visitas as $visita) {
                if ($visita->punto) {
                    $resultado[] = [
                        'id_visita' => $visita->id,
                        'idpos' => $visita->punto->idpos,
                        'nombre' => $visita->punto->nombre,
                        'direccion' => $visita->punto->direccion,
                        'fecha_visita' => $visita->fecha->format('Y-m-d'),
                        'hora_inicio' => $visita->hora_inicio ? $visita->hora_inicio->format('H:i:s') : null,
                        'hora_fin' => $visita->hora_fin ? $visita->hora_fin->format('H:i:s') : null,
                        'efectiva' => $visita->efectiva,
                        'estado' => $visita->estado_nombre,
                        'duracion_minutos' => $visita->duracion,
                        'distancia_punto' => $visita->calcularDistanciaAlPunto()
                    ];
                }
            }

            return [
                'total' => count($resultado),
                'fecha_inicio' => $fechaInicio,
                'fecha_fin' => $fechaFin,
                'visitas' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en getPuntosVisitados: ' . $e->getMessage());
            return [
                'total' => 0,
                'visitas' => []
            ];
        }
    }

    /**
     * Buscar cartera de puntos asignados
     */
    public function buscarCarteraPuntos(array $parametros): array
    {
        try {
            $idUsuario = $parametros['id_usuario'];
            $filtro = $parametros['filtro'] ?? '';
            $tipoFiltro = $parametros['tipo_filtro'] ?? 'nombre';
            $limite = $parametros['limite'] ?? 50;

            $query = Point::with(['infoPos'])
                ->where('id_distri', $idUsuario);

            if (!empty($filtro)) {
                switch ($tipoFiltro) {
                    case 'codigo':
                        $query->where('idpos', 'like', '%' . $filtro . '%');
                        break;
                    case 'direccion':
                        $query->where('direccion', 'like', '%' . $filtro . '%');
                        break;
                    default: // nombre
                        $query->where('nombre', 'like', '%' . $filtro . '%');
                        break;
                }
            }

            $puntos = $query->limit($limite)->get();

            $resultado = [];
            foreach ($puntos as $punto) {
                $resultado[] = [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'direccion' => $punto->direccion,
                    'latitud' => $punto->latitud,
                    'longitud' => $punto->longitud,
                    'efectiva' => $punto->efectiva,
                    'cantidad_visitas' => $punto->cantidad_visitas,
                    'fecha_ultima_visita' => $punto->ultima_visita ? $punto->ultima_visita->fecha->format('Y-m-d') : null,
                    'info_adicional' => $punto->infoPos ? [
                        'propietario' => $punto->infoPos->nombre_propietario,
                        'telefono' => $punto->infoPos->telefono,
                        'categoria' => $punto->infoPos->categoria,
                        'estado_comercial' => $punto->infoPos->estado_comercial
                    ] : null
                ];
            }

            return [
                'total' => count($resultado),
                'filtro_aplicado' => $filtro,
                'tipo_filtro' => $tipoFiltro,
                'puntos' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en buscarCarteraPuntos: ' . $e->getMessage());
            return [
                'total' => 0,
                'puntos' => []
            ];
        }
    }

    /**
     * Crear o actualizar punto
     */
    public function crearOActualizarPunto(array $datos): array
    {
        try {
            DB::beginTransaction();

            $idpos = $datos['idpos'] ?? null;
            $esActualizacion = !empty($idpos);

            if ($esActualizacion) {
                $punto = Point::find($idpos);
                if (!$punto) {
                    return [
                        'estado' => 0,
                        'msg' => 'Punto no encontrado',
                        'punto' => null
                    ];
                }
            } else {
                // Generar nuevo ID para punto
                $ultimoId = Point::max('idpos') ?? 0;
                $idpos = $ultimoId + 1;
                $punto = new Point();
                $punto->idpos = $idpos;
            }

            // Actualizar datos del punto
            $punto->nombre = $datos['nombre'];
            $punto->direccion = $datos['direccion'];
            $punto->latitud = $datos['latitud'];
            $punto->longitud = $datos['longitud'];
            $punto->precisiongps = $datos['precision_gps'] ?? null;
            $punto->id_distri = $datos['id_usuario'];
            $punto->fecha = now()->toDateString();
            $punto->hora = now()->toTimeString();

            if (!$esActualizacion) {
                $punto->efectiva = 0;
                $punto->cantidad_visitas = 0;
                $punto->fecha_creacion = now();
            } else {
                $punto->fecha_actualizacion = now();
            }

            $punto->save();

            // Crear o actualizar info_pos
            $infoPos = InfoPos::find($idpos);
            if (!$infoPos) {
                $infoPos = new InfoPos();
                $infoPos->idpos = $idpos;
            }

            $infoPos->nombre = $datos['nombre'];
            $infoPos->direccion = $datos['direccion'];
            $infoPos->latitud = $datos['latitud'];
            $infoPos->longitud = $datos['longitud'];
            $infoPos->nombre_propietario = $datos['nombre_propietario'] ?? null;
            $infoPos->telefono = $datos['telefono'] ?? null;
            $infoPos->email = $datos['email'] ?? null;
            $infoPos->categoria = $datos['categoria'] ?? null;
            $infoPos->estado_comercial = $datos['estado_comercial'] ?? 'ACTIVO';
            $infoPos->id_departamento = $datos['id_departamento'] ?? null;
            $infoPos->id_municipio = $datos['id_municipio'] ?? null;
            $infoPos->circuito = $datos['circuito'] ?? null;
            $infoPos->ruta = $datos['ruta'] ?? null;

            $infoPos->save();

            DB::commit();

            $mensaje = $esActualizacion ? 'Punto actualizado exitosamente' : 'Punto creado exitosamente';

            return [
                'estado' => 1,
                'msg' => $mensaje,
                'punto' => [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'direccion' => $punto->direccion,
                    'latitud' => $punto->latitud,
                    'longitud' => $punto->longitud,
                    'efectiva' => $punto->efectiva,
                    'info_pos' => [
                        'nombre_propietario' => $infoPos->nombre_propietario,
                        'telefono' => $infoPos->telefono,
                        'categoria' => $infoPos->categoria,
                        'estado_comercial' => $infoPos->estado_comercial
                    ]
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error en crearOActualizarPunto: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al procesar el punto',
                'punto' => null
            ];
        }
    }

    /**
     * Obtener campos dinámicos para formularios
     */
    public function getCamposDinamicos(): array
    {
        try {
            // Campos dinámicos estándar para formularios de puntos
            return [
                'categorias' => [
                    ['id' => 'TIENDA', 'descripcion' => 'Tienda'],
                    ['id' => 'KIOSCO', 'descripcion' => 'Kiosco'],
                    ['id' => 'SUPERMERCADO', 'descripcion' => 'Supermercado'],
                    ['id' => 'FARMACIA', 'descripcion' => 'Farmacia'],
                    ['id' => 'OTROS', 'descripcion' => 'Otros']
                ],
                'estados_comerciales' => [
                    ['id' => 'ACTIVO', 'descripcion' => 'Activo'],
                    ['id' => 'INACTIVO', 'descripcion' => 'Inactivo'],
                    ['id' => 'SUSPENDIDO', 'descripcion' => 'Suspendido'],
                    ['id' => 'EN_PROCESO', 'descripcion' => 'En Proceso']
                ],
                'tipos_documento' => [
                    ['id' => 'CI', 'descripcion' => 'Cédula de Identidad'],
                    ['id' => 'RUT', 'descripcion' => 'RUT'],
                    ['id' => 'PASAPORTE', 'descripcion' => 'Pasaporte']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en getCamposDinamicos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener campos dinámicos específicos de un punto
     */
    public function getCamposDinamicosPunto(int $idpos): array
    {
        try {
            $punto = Point::with('infoPos')->find($idpos);

            if (!$punto) {
                return [];
            }

            return [
                'punto_id' => $idpos,
                'campos_actuales' => [
                    'categoria' => $punto->infoPos->categoria ?? null,
                    'estado_comercial' => $punto->infoPos->estado_comercial ?? null,
                    'tipo_documento' => $punto->infoPos->tipo_documento ?? null,
                    'numero_documento' => $punto->infoPos->numero_documento ?? null,
                    'codigo_unico_tienda' => $punto->infoPos->codigo_unico_tienda ?? null
                ],
                'opciones_disponibles' => $this->getCamposDinamicos()
            ];

        } catch (\Exception $e) {
            Log::error('Error en getCamposDinamicosPunto: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener detalle completo de un punto
     */
    public function getDetallePunto(int $idpos): ?array
    {
        try {
            $punto = Point::with(['infoPos.departamento', 'infoPos.municipio', 'visitas' => function($query) {
                $query->latest()->limit(5);
            }])->find($idpos);

            if (!$punto) {
                return null;
            }

            return [
                'idpos' => $punto->idpos,
                'nombre' => $punto->nombre,
                'direccion' => $punto->direccion,
                'latitud' => $punto->latitud,
                'longitud' => $punto->longitud,
                'efectiva' => $punto->efectiva,
                'cantidad_visitas' => $punto->cantidad_visitas,
                'precision_gps' => $punto->precisiongps,
                'fecha_creacion' => $punto->fecha_creacion,
                'info_detallada' => $punto->infoPos ? $punto->infoPos->resumen : null,
                'ultimas_visitas' => $punto->visitas->map(function($visita) {
                    return [
                        'id' => $visita->id,
                        'fecha' => $visita->fecha->format('Y-m-d'),
                        'efectiva' => $visita->efectiva,
                        'estado' => $visita->estado_nombre
                    ];
                })
            ];

        } catch (\Exception $e) {
            Log::error('Error en getDetallePunto: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validar proximidad a un punto
     */
    public function validarProximidad(int $idpos, float $latitud, float $longitud, int $tolerancia): array
    {
        try {
            $punto = Point::find($idpos);

            if (!$punto) {
                return [
                    'valido' => false,
                    'mensaje' => 'Punto no encontrado',
                    'distancia' => null
                ];
            }

            $distancia = $punto->calcularDistancia($latitud, $longitud);
            $dentroTolerancia = $punto->estaDentroDeTolerancia($latitud, $longitud, $tolerancia);

            return [
                'valido' => $dentroTolerancia,
                'mensaje' => $dentroTolerancia ? 'Ubicación válida' : 'Fuera del rango permitido',
                'distancia' => round($distancia, 2),
                'tolerancia' => $tolerancia,
                'punto' => [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'latitud' => $punto->latitud,
                    'longitud' => $punto->longitud
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en validarProximidad: ' . $e->getMessage());
            return [
                'valido' => false,
                'mensaje' => 'Error en validación',
                'distancia' => null
            ];
        }
    }

    /**
     * Obtener estadísticas de puntos
     */
    public function getEstadisticasPuntos(int $idUsuario): array
    {
        try {
            $totalPuntos = Point::where('id_distri', $idUsuario)->count();
            $puntosEfectivos = Point::where('id_distri', $idUsuario)->where('efectiva', 1)->count();
            $puntosVisitadosHoy = Visita::where('id_usuario', $idUsuario)
                ->whereDate('fecha', today())
                ->distinct('id_pos')
                ->count();

            $visitasEsteMes = Visita::where('id_usuario', $idUsuario)
                ->whereMonth('fecha', now()->month)
                ->whereYear('fecha', now()->year)
                ->count();

            return [
                'total_puntos' => $totalPuntos,
                'puntos_efectivos' => $puntosEfectivos,
                'puntos_visitados_hoy' => $puntosVisitadosHoy,
                'visitas_este_mes' => $visitasEsteMes,
                'porcentaje_efectividad' => $totalPuntos > 0 ? round(($puntosEfectivos / $totalPuntos) * 100, 2) : 0
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEstadisticasPuntos: ' . $e->getMessage());
            return [
                'total_puntos' => 0,
                'puntos_efectivos' => 0,
                'puntos_visitados_hoy' => 0,
                'visitas_este_mes' => 0,
                'porcentaje_efectividad' => 0
            ];
        }
    }
}
