<?php

namespace App\Services;

use App\Models\User;
use App\Helpers\StringHelper;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthService
{
    protected $userService;
    protected $versionService;

    public function __construct(UserService $userService, VersionService $versionService)
    {
        $this->userService = $userService;
        $this->versionService = $versionService;
    }

    /**
     * Procesar login de usuario
     */
    public function login(array $datos): array
    {
        try {
            $username = $datos['user'];
            $password = $datos['pass'];
            $versionName = $datos['versionName'];
            $versionCode = $datos['versionCode'];
            $tokenNotificacion = $datos['tokenNotificacion'] ?? null;

            // Validar versión de la aplicación
            if (!$this->versionService->validarVersion($versionName, $versionCode)) {
                return [
                    'estado' => 0,
                    'msg' => 'La aplicación se encuentra en una versión desactualizada',
                    'datos' => null
                ];
            }

            // Obtener usuario
            $resultado = $this->userService->getUsuario($username, $password);

            if ($resultado['estado'] === 0) {
                return $resultado;
            }

            $user = $resultado['usuario'];

            // Generar token JWT
            $token = JWTAuth::fromUser($user);

            // Actualizar token de notificación si se proporciona
            if ($tokenNotificacion) {
                $this->userService->actualizarTokenNotificacion($user->id, $tokenNotificacion);
            }

            // Registrar uso de versión
            $this->versionService->logVersionUsage($versionName, $versionCode, $user->id);

            // Preparar datos de respuesta
            $datosRespuesta = [
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => auth()->factory()->getTTL() * 60,
                'usuario' => [
                    'id' => $user->id,
                    'cedula' => $user->cedula,
                    'nombre' => $user->nombre_completo,
                    'user' => $user->user,
                    'perfil' => $user->perfil,
                    'id_perfil' => $user->id_perfil,
                    'email' => $user->email,
                    'tolerancia_visita' => $user->tolerancia_visita,
                    'primer_logueo' => $user->primer_logueo,
                    'ultimo_acceso' => $user->ultimo_acceso
                ],
                'configuracion' => $this->getConfiguracionPais(),
                'version_info' => $this->versionService->getVersionInfo()
            ];

            return [
                'estado' => 1,
                'msg' => 'Login exitoso',
                'datos' => $datosRespuesta
            ];

        } catch (\Exception $e) {
            Log::error('Error en AuthService::login: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error interno del servidor',
                'datos' => null
            ];
        }
    }

    /**
     * Recuperar contraseña por email
     */
    public function recuperarPassword(string $email): array
    {
        try {
            // Validar email
            $idUsuario = $this->userService->validarEmail($email);

            if ($idUsuario === 0) {
                return [
                    'estado' => 0,
                    'msg' => 'El email no está registrado en el sistema'
                ];
            }

            // Generar token de recuperación
            $recoverToken = StringHelper::generateRandomToken(32);

            // Guardar token de recuperación
            $guardado = $this->userService->guardarRecoPass($idUsuario, $recoverToken);

            if (!$guardado) {
                return [
                    'estado' => 0,
                    'msg' => 'Error al procesar la solicitud de recuperación'
                ];
            }

            // Enviar email de recuperación
            $enviado = $this->enviarEmailRecuperacion($email, $recoverToken);

            if ($enviado) {
                return [
                    'estado' => 1,
                    'msg' => 'Se ha enviado un email con las instrucciones para recuperar tu contraseña'
                ];
            } else {
                return [
                    'estado' => 0,
                    'msg' => 'Error al enviar el email de recuperación'
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error en AuthService::recuperarPassword: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Cambiar contraseña de usuario
     */
    public function cambiarPassword(User $user, string $passwordAnterior, string $passwordNuevo): array
    {
        try {
            // Cambiar contraseña usando UserService
            $resultado = $this->userService->cambiarPass($passwordAnterior, $passwordNuevo, $user->id);

            switch ($resultado) {
                case 1:
                    return [
                        'estado' => 1,
                        'msg' => 'Contraseña cambiada exitosamente'
                    ];
                case 2:
                    return [
                        'estado' => 0,
                        'msg' => 'La contraseña anterior es incorrecta'
                    ];
                default:
                    return [
                        'estado' => 0,
                        'msg' => 'Error al cambiar la contraseña'
                    ];
            }

        } catch (\Exception $e) {
            Log::error('Error en AuthService::cambiarPassword: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Obtener configuración del país actual
     */
    private function getConfiguracionPais(): array
    {
        $countryHelper = app('country.helper');
        
        return [
            'pais' => $countryHelper->getCurrentCountry(),
            'operador' => $countryHelper->getCountryConfig()['operator'] ?? 'Movistar',
            'timezone' => $countryHelper->getTimezone(),
            'moneda' => $countryHelper->getCurrency(),
            'tolerancia_visita' => $countryHelper->getVisitTolerance(),
            'precision_gps' => $countryHelper->getGpsPrecision(),
            'pagina_size' => $countryHelper->getDefaultPageSize(),
            'max_pagina_size' => $countryHelper->getMaxPageSize(),
            'urls' => [
                'operador' => $countryHelper->getOperatorUrl(),
                'recuperacion' => $countryHelper->getRecoveryUrl()
            ],
            'features' => [
                'formularios_dinamicos' => $countryHelper->isFeatureEnabled('dynamic_forms'),
                'gestion_inventario' => $countryHelper->isFeatureEnabled('inventory_management'),
                'seguimiento_visitas' => $countryHelper->isFeatureEnabled('visit_tracking'),
                'sistema_encuestas' => $countryHelper->isFeatureEnabled('survey_system'),
                'subida_archivos' => $countryHelper->isFeatureEnabled('file_upload'),
                'notificaciones' => $countryHelper->isFeatureEnabled('notifications')
            ]
        ];
    }

    /**
     * Enviar email de recuperación de contraseña
     */
    private function enviarEmailRecuperacion(string $email, string $token): bool
    {
        try {
            $countryHelper = app('country.helper');
            $recoveryUrl = $countryHelper->getRecoveryUrl() . $token;

            // Aquí se implementaría el envío real del email
            // Por ahora simulamos que se envía correctamente
            Log::info("Email de recuperación enviado a: {$email} con token: {$token}");
            Log::info("URL de recuperación: {$recoveryUrl}");

            // En producción, usar Mail::send() o similar
            /*
            Mail::send('emails.password-recovery', [
                'token' => $token,
                'recovery_url' => $recoveryUrl
            ], function ($message) use ($email) {
                $message->to($email)
                        ->subject('Recuperación de Contraseña - Operador Movistar');
            });
            */

            return true;

        } catch (\Exception $e) {
            Log::error('Error enviando email de recuperación: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validar token de recuperación
     */
    public function validarTokenRecuperacion(string $token): array
    {
        try {
            // Aquí se validaría el token contra la base de datos
            // Por ahora simulamos validación básica
            
            if (empty($token) || strlen($token) < 32) {
                return [
                    'estado' => 0,
                    'msg' => 'Token de recuperación inválido'
                ];
            }

            return [
                'estado' => 1,
                'msg' => 'Token válido'
            ];

        } catch (\Exception $e) {
            Log::error('Error validando token de recuperación: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error interno del servidor'
            ];
        }
    }

    /**
     * Restablecer contraseña con token
     */
    public function restablecerPassword(string $token, string $nuevaPassword): array
    {
        try {
            // Validar token
            $validacion = $this->validarTokenRecuperacion($token);
            
            if ($validacion['estado'] === 0) {
                return $validacion;
            }

            // Aquí se buscaría el usuario por token y se actualizaría la contraseña
            // Por ahora simulamos éxito
            
            return [
                'estado' => 1,
                'msg' => 'Contraseña restablecida exitosamente'
            ];

        } catch (\Exception $e) {
            Log::error('Error restableciendo contraseña: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error interno del servidor'
            ];
        }
    }
}
