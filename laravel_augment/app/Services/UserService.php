<?php

namespace App\Services;

use App\Models\User;
use App\Helpers\StringHelper;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UserService
{
    /**
     * Obtener usuario por credenciales
     */
    public function getUsuario(string $username, string $password): array
    {
        try {
            $user = User::byUsername($username)->activos()->first();

            if (!$user) {
                return [
                    'estado' => 0,
                    'msg' => 'Usuario no encontrado o inactivo',
                    'usuario' => null
                ];
            }

            // Verificar contraseña
            if (!$this->verificarPassword($password, $user->pass)) {
                return [
                    'estado' => 0,
                    'msg' => 'Credenciales inválidas',
                    'usuario' => null
                ];
            }

            // Actualizar último acceso
            $user->update(['ultimo_acceso' => now()]);

            return [
                'estado' => 1,
                'msg' => 'Usuario autenticado correctamente',
                'usuario' => $user
            ];

        } catch (\Exception $e) {
            Log::error('Error en getUsuario: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error interno del servidor',
                'usuario' => null
            ];
        }
    }

    /**
     * Obtener todos los usuarios
     */
    public function getAllUsuarios(): array
    {
        try {
            $usuarios = User::activos()
                ->select('id', 'cedula', 'nombre', 'apellido', 'user', 'perfil', 'id_perfil', 'email')
                ->get();

            return $usuarios->toArray();

        } catch (\Exception $e) {
            Log::error('Error en getAllUsuarios: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Validar email y obtener ID de usuario
     */
    public function validarEmail(string $email): int
    {
        try {
            $user = User::byEmail($email)->activos()->first();
            return $user ? $user->id : 0;

        } catch (\Exception $e) {
            Log::error('Error en validarEmail: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Guardar token de recuperación de contraseña
     */
    public function guardarRecoPass(int $idUsuario, string $recoverToken): bool
    {
        try {
            // Aquí se guardaría en una tabla de tokens de recuperación
            // Por ahora simulamos que se guarda correctamente
            Log::info("Token de recuperación guardado para usuario {$idUsuario}: {$recoverToken}");
            return true;

        } catch (\Exception $e) {
            Log::error('Error en guardarRecoPass: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Cambiar contraseña de usuario
     */
    public function cambiarPass(string $passwordAnterior, string $passwordNuevo, int $idUsuario): int
    {
        try {
            $user = User::find($idUsuario);

            if (!$user) {
                return 0; // Usuario no encontrado
            }

            // Verificar contraseña anterior
            if (!$this->verificarPassword($passwordAnterior, $user->pass)) {
                return 2; // Contraseña anterior incorrecta
            }

            // Actualizar contraseña
            $user->update([
                'pass' => bcrypt($passwordNuevo),
                'estado_pass' => 0 // Ya no es primer logueo
            ]);

            return 1; // Éxito

        } catch (\Exception $e) {
            Log::error('Error en cambiarPass: ' . $e->getMessage());
            return 0; // Error
        }
    }

    /**
     * Obtener permisos DCS de un usuario
     */
    public function getPermisos(int $idUsuario, string $fechaHora): array
    {
        try {
            // Aquí se implementaría la lógica para obtener permisos DCS
            // Por ahora retornamos permisos básicos
            return [
                'usuario_id' => $idUsuario,
                'permisos' => [
                    'crear_puntos' => true,
                    'editar_puntos' => true,
                    'ver_inventario' => true,
                    'realizar_visitas' => true,
                    'responder_encuestas' => true
                ],
                'fecha_consulta' => $fechaHora
            ];

        } catch (\Exception $e) {
            Log::error('Error en getPermisos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener puntos asignados a un usuario
     */
    public function getPuntos(int $idUsuario): array
    {
        try {
            $user = User::find($idUsuario);

            if (!$user) {
                return [];
            }

            // Obtener puntos asignados al usuario
            $puntos = DB::table('puntos')
                ->where('id_distri', $idUsuario)
                ->select('idpos', 'nombre', 'direccion', 'latitud', 'longitud', 'efectiva')
                ->get();

            return [
                'total_puntos' => $puntos->count(),
                'puntos_efectivos' => $puntos->where('efectiva', 1)->count(),
                'puntos' => $puntos->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error en getPuntos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener estados comerciales
     */
    public function getEstadosCom(string $fecha): array
    {
        try {
            // Estados comerciales estándar
            return [
                ['id' => 1, 'descripcion' => 'ACTIVO', 'color' => '#28a745'],
                ['id' => 2, 'descripcion' => 'INACTIVO', 'color' => '#dc3545'],
                ['id' => 3, 'descripcion' => 'SUSPENDIDO', 'color' => '#ffc107'],
                ['id' => 4, 'descripcion' => 'EN_PROCESO', 'color' => '#17a2b8'],
                ['id' => 5, 'descripcion' => 'CERRADO', 'color' => '#6c757d']
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEstadosCom: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Listar motivos de visitas no efectivas
     */
    public function listarMotivos(): array
    {
        try {
            // Motivos estándar para visitas no efectivas
            return [
                ['id' => 1, 'descripcion' => 'PUNTO CERRADO', 'tipo' => 'NO_EFECTIVA'],
                ['id' => 2, 'descripcion' => 'NO SE ENCUENTRA RESPONSABLE', 'tipo' => 'NO_EFECTIVA'],
                ['id' => 3, 'descripcion' => 'PUNTO NO EXISTE', 'tipo' => 'NO_EFECTIVA'],
                ['id' => 4, 'descripcion' => 'DIRECCION INCORRECTA', 'tipo' => 'NO_EFECTIVA'],
                ['id' => 5, 'descripcion' => 'CLIENTE NO DISPONIBLE', 'tipo' => 'NO_EFECTIVA'],
                ['id' => 6, 'descripcion' => 'PROBLEMAS DE ACCESO', 'tipo' => 'NO_EFECTIVA'],
                ['id' => 7, 'descripcion' => 'OTROS', 'tipo' => 'NO_EFECTIVA']
            ];

        } catch (\Exception $e) {
            Log::error('Error en listarMotivos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Verificar contraseña
     */
    private function verificarPassword(string $password, string $hashedPassword): bool
    {
        // Verificar si es hash de Laravel/bcrypt
        if (Hash::check($password, $hashedPassword)) {
            return true;
        }

        // Verificar si es MD5 (compatibilidad con sistema anterior)
        if (md5($password) === $hashedPassword) {
            return true;
        }

        // Verificar texto plano (solo para desarrollo)
        if (app()->environment('local') && $password === $hashedPassword) {
            return true;
        }

        return false;
    }

    /**
     * Crear nuevo usuario
     */
    public function crearUsuario(array $datos): array
    {
        try {
            // Validar que el username no exista
            if (User::byUsername($datos['user'])->exists()) {
                return [
                    'estado' => 0,
                    'msg' => 'El nombre de usuario ya existe',
                    'usuario' => null
                ];
            }

            // Validar que el email no exista (si se proporciona)
            if (!empty($datos['email']) && User::byEmail($datos['email'])->exists()) {
                return [
                    'estado' => 0,
                    'msg' => 'El email ya está registrado',
                    'usuario' => null
                ];
            }

            $user = User::create([
                'cedula' => $datos['cedula'] ?? null,
                'nombre' => StringHelper::toMayusculas($datos['nombre']),
                'apellido' => StringHelper::toMayusculas($datos['apellido']),
                'user' => $datos['user'],
                'pass' => bcrypt($datos['password']),
                'perfil' => $datos['perfil'] ?? 'Operador',
                'id_perfil' => $datos['id_perfil'] ?? 2,
                'estado_pass' => 1, // Primer logueo
                'fecha_hora' => now(),
                'tolerancia_visita' => $datos['tolerancia_visita'] ?? 100,
                'email' => $datos['email'] ?? null,
                'activo' => true
            ]);

            return [
                'estado' => 1,
                'msg' => 'Usuario creado exitosamente',
                'usuario' => $user
            ];

        } catch (\Exception $e) {
            Log::error('Error en crearUsuario: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al crear el usuario',
                'usuario' => null
            ];
        }
    }

    /**
     * Actualizar token de notificación
     */
    public function actualizarTokenNotificacion(int $idUsuario, string $token): bool
    {
        try {
            $user = User::find($idUsuario);
            
            if ($user) {
                $user->update(['token_notificacion' => $token]);
                return true;
            }
            
            return false;

        } catch (\Exception $e) {
            Log::error('Error en actualizarTokenNotificacion: ' . $e->getMessage());
            return false;
        }
    }
}
