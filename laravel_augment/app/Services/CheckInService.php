<?php

namespace App\Services;

use App\Models\Point;
use App\Models\InfoPos;
use App\Models\Visita;
use App\Models\Survey;
use App\Models\DetalleInventario;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CheckInService
{
    /**
     * Obtener información de tabs para check-in
     */
    public function getTabsInfo(int $idpos, array $tabs, int $idUsuario): array
    {
        try {
            $resultado = [
                'idpos' => $idpos,
                'tabs_solicitadas' => $tabs,
                'fecha_consulta' => now()->format('Y-m-d H:i:s'),
                'tabs' => []
            ];

            foreach ($tabs as $tab) {
                switch ($tab) {
                    case 'info_general':
                        $resultado['tabs']['info_general'] = $this->getInfoGeneral($idpos);
                        break;
                    case 'inventario':
                        $resultado['tabs']['inventario'] = $this->getResumenInventario($idpos);
                        break;
                    case 'encuestas':
                        $resultado['tabs']['encuestas'] = $this->getEncuestasDisponibles($idpos, null, $idUsuario);
                        break;
                    case 'visitas':
                        $resultado['tabs']['visitas'] = $this->getHistorialVisitas(['idpos' => $idpos, 'limite' => 10]);
                        break;
                    case 'fotos':
                        $resultado['tabs']['fotos'] = $this->getFotosRecientes($idpos);
                        break;
                }
            }

            return $resultado;

        } catch (\Exception $e) {
            Log::error('Error en getTabsInfo: ' . $e->getMessage());
            return [
                'idpos' => $idpos,
                'tabs' => [],
                'error' => 'Error obteniendo información de tabs'
            ];
        }
    }

    /**
     * Obtener información general del punto
     */
    public function getInfoGeneral(int $idpos): ?array
    {
        try {
            $punto = Point::with(['infoPos.departamento', 'infoPos.municipio', 'agente'])->find($idpos);

            if (!$punto) {
                return null;
            }

            $infoPos = $punto->infoPos;

            return [
                'punto' => [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'direccion' => $punto->direccion,
                    'latitud' => $punto->latitud,
                    'longitud' => $punto->longitud,
                    'efectiva' => $punto->efectiva,
                    'cantidad_visitas' => $punto->cantidad_visitas,
                    'fecha_ultima_visita' => $punto->fecha ? $punto->fecha : null,
                    'precision_gps' => $punto->precisiongps
                ],
                'info_detallada' => $infoPos ? [
                    'nombre_propietario' => $infoPos->nombre_propietario,
                    'telefono' => $infoPos->telefono,
                    'tel_op' => $infoPos->tel_op,
                    'email' => $infoPos->email,
                    'telefono_fijo' => $infoPos->telefono_fijo,
                    'celular' => $infoPos->celular,
                    'categoria' => $infoPos->categoria,
                    'estado_comercial' => $infoPos->estado_comercial,
                    'circuito' => $infoPos->circuito,
                    'ruta' => $infoPos->ruta,
                    'distribuidor' => $infoPos->distribuidor,
                    'codigo_unico_tienda' => $infoPos->codigo_unico_tienda,
                    'tipo_documento' => $infoPos->tipo_documento,
                    'numero_documento' => $infoPos->numero_documento
                ] : null,
                'ubicacion' => [
                    'departamento' => $infoPos && $infoPos->departamento ? $infoPos->departamento->descripcion : null,
                    'municipio' => $infoPos && $infoPos->municipio ? $infoPos->municipio->descripcion : null,
                    'direccion_completa' => $infoPos ? $infoPos->direccion_completa : $punto->direccion
                ],
                'agente_asignado' => $punto->agente ? [
                    'id' => $punto->agente->id,
                    'nombre' => $punto->agente->nombre_completo,
                    'user' => $punto->agente->user,
                    'email' => $punto->agente->email
                ] : null,
                'estadisticas' => [
                    'total_visitas' => $punto->cantidad_visitas,
                    'visitas_este_mes' => $this->getVisitasEsteMes($idpos),
                    'ultima_visita_efectiva' => $this->getUltimaVisitaEfectiva($idpos),
                    'promedio_visitas_mes' => $this->getPromedioVisitasMes($idpos)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en getInfoGeneral: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener resumen de inventario
     */
    public function getResumenInventario(int $idpos, ?string $categoria = null): array
    {
        try {
            $query = DetalleInventario::with('referencia')->where('id_pos', $idpos);

            if ($categoria) {
                $query->whereHas('referencia', function($q) use ($categoria) {
                    $q->where('categoria', $categoria);
                });
            }

            $inventario = $query->get();

            $resumen = [
                'total_productos' => $inventario->count(),
                'valor_total' => $inventario->sum('valor_total'),
                'por_estado' => [
                    'disponible' => $inventario->where('estado', DetalleInventario::ESTADO_DISPONIBLE)->count(),
                    'vendido' => $inventario->where('estado', DetalleInventario::ESTADO_VENDIDO)->count(),
                    'defectuoso' => $inventario->where('estado', DetalleInventario::ESTADO_DEFECTUOSO)->count(),
                    'devuelto' => $inventario->where('estado', DetalleInventario::ESTADO_DEVUELTO)->count()
                ],
                'por_categoria' => [],
                'productos_destacados' => []
            ];

            // Agrupar por categoría
            $porCategoria = $inventario->groupBy(function($item) {
                return $item->referencia ? $item->referencia->categoria : 'Sin categoría';
            });

            foreach ($porCategoria as $cat => $productos) {
                $resumen['por_categoria'][$cat] = [
                    'total' => $productos->count(),
                    'disponibles' => $productos->where('estado', DetalleInventario::ESTADO_DISPONIBLE)->count(),
                    'valor' => $productos->sum('valor_total')
                ];
            }

            // Productos más vendidos o de mayor valor
            $resumen['productos_destacados'] = $inventario
                ->where('estado', DetalleInventario::ESTADO_DISPONIBLE)
                ->sortByDesc('precio')
                ->take(5)
                ->map(function($producto) {
                    return [
                        'descripcion' => $producto->descripcion,
                        'precio' => $producto->precio,
                        'categoria' => $producto->referencia ? $producto->referencia->categoria : null
                    ];
                })
                ->values()
                ->toArray();

            return $resumen;

        } catch (\Exception $e) {
            Log::error('Error en getResumenInventario: ' . $e->getMessage());
            return [
                'total_productos' => 0,
                'valor_total' => 0,
                'por_estado' => [],
                'por_categoria' => [],
                'productos_destacados' => []
            ];
        }
    }

    /**
     * Obtener encuestas disponibles
     */
    public function getEncuestasDisponibles(int $idpos, ?int $idVisita = null, int $idUsuario): array
    {
        try {
            $encuestas = Survey::activas()->aplicables()->with('preguntas')->get();

            $resultado = [];
            foreach ($encuestas as $encuesta) {
                $progreso = 0;
                $completa = false;

                if ($idVisita) {
                    $progreso = $encuesta->getProgresoParaVisita($idVisita);
                    $completa = $encuesta->estaCompletaParaVisita($idVisita);
                }

                $resultado[] = [
                    'id' => $encuesta->id,
                    'titulo' => $encuesta->titulo,
                    'descripcion' => $encuesta->descripcion,
                    'obligatorio' => $encuesta->es_obligatoria,
                    'total_preguntas' => $encuesta->total_preguntas,
                    'progreso_porcentaje' => $progreso,
                    'esta_completa' => $completa,
                    'permite_navegacion' => $encuesta->permite_navegacion,
                    'aplicable' => $encuesta->es_aplicable
                ];
            }

            return [
                'total_encuestas' => count($resultado),
                'obligatorias' => collect($resultado)->where('obligatorio', true)->count(),
                'opcionales' => collect($resultado)->where('obligatorio', false)->count(),
                'encuestas' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEncuestasDisponibles: ' . $e->getMessage());
            return [
                'total_encuestas' => 0,
                'encuestas' => []
            ];
        }
    }

    /**
     * Obtener historial de visitas
     */
    public function getHistorialVisitas(array $parametros): array
    {
        try {
            $idpos = $parametros['idpos'];
            $limite = $parametros['limite'] ?? 10;
            $fechaInicio = $parametros['fecha_inicio'] ?? null;
            $fechaFin = $parametros['fecha_fin'] ?? null;

            $query = Visita::with(['usuario'])
                ->where('id_pos', $idpos);

            if ($fechaInicio && $fechaFin) {
                $query->whereBetween('fecha', [$fechaInicio, $fechaFin]);
            }

            $visitas = $query->orderBy('fecha', 'desc')
                ->orderBy('hora_inicio', 'desc')
                ->limit($limite)
                ->get();

            $resultado = $visitas->map(function($visita) {
                return [
                    'id' => $visita->id,
                    'fecha' => $visita->fecha->format('Y-m-d'),
                    'hora_inicio' => $visita->hora_inicio ? $visita->hora_inicio->format('H:i:s') : null,
                    'hora_fin' => $visita->hora_fin ? $visita->hora_fin->format('H:i:s') : null,
                    'duracion_minutos' => $visita->duracion,
                    'efectiva' => $visita->efectiva,
                    'estado' => $visita->estado_nombre,
                    'usuario' => $visita->usuario ? [
                        'nombre' => $visita->usuario->nombre_completo,
                        'user' => $visita->usuario->user
                    ] : null,
                    'observaciones' => $visita->observaciones,
                    'encuestas_respondidas' => $visita->encuestas()->count()
                ];
            });

            return [
                'total_visitas' => $resultado->count(),
                'visitas_efectivas' => $resultado->where('efectiva', 1)->count(),
                'promedio_duracion' => $resultado->where('duracion_minutos', '>', 0)->avg('duracion_minutos'),
                'visitas' => $resultado->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error en getHistorialVisitas: ' . $e->getMessage());
            return [
                'total_visitas' => 0,
                'visitas' => []
            ];
        }
    }

    /**
     * Validar check-in
     */
    public function validarCheckIn(array $parametros): array
    {
        try {
            $idpos = $parametros['idpos'];
            $latitud = $parametros['latitud'];
            $longitud = $parametros['longitud'];
            $idUsuario = $parametros['id_usuario'];
            $tolerancia = $parametros['tolerancia'];

            $punto = Point::find($idpos);

            if (!$punto) {
                return [
                    'valido' => false,
                    'mensaje' => 'Punto no encontrado',
                    'codigo_error' => 'PUNTO_NO_ENCONTRADO'
                ];
            }

            // Verificar si hay visita activa
            $visitaActiva = Visita::where('id_usuario', $idUsuario)
                ->where('estado', Visita::ESTADO_EN_PROCESO)
                ->first();

            if ($visitaActiva && $visitaActiva->id_pos !== $idpos) {
                return [
                    'valido' => false,
                    'mensaje' => 'Tienes una visita activa en otro punto. Finalízala primero.',
                    'codigo_error' => 'VISITA_ACTIVA_OTRO_PUNTO',
                    'visita_activa' => [
                        'id' => $visitaActiva->id,
                        'punto' => $visitaActiva->punto ? $visitaActiva->punto->nombre : null
                    ]
                ];
            }

            // Validar proximidad
            $distancia = $punto->calcularDistancia($latitud, $longitud);
            $dentroTolerancia = $punto->estaDentroDeTolerancia($latitud, $longitud, $tolerancia);

            if (!$dentroTolerancia) {
                return [
                    'valido' => false,
                    'mensaje' => "Estás fuera del rango permitido. Distancia: {$distancia}m, Tolerancia: {$tolerancia}m",
                    'codigo_error' => 'FUERA_DE_RANGO',
                    'distancia' => round($distancia, 2),
                    'tolerancia' => $tolerancia
                ];
            }

            return [
                'valido' => true,
                'mensaje' => 'Check-in válido',
                'distancia' => round($distancia, 2),
                'tolerancia' => $tolerancia,
                'punto' => [
                    'idpos' => $punto->idpos,
                    'nombre' => $punto->nombre,
                    'direccion' => $punto->direccion
                ],
                'puede_continuar_visita' => $visitaActiva && $visitaActiva->id_pos === $idpos
            ];

        } catch (\Exception $e) {
            Log::error('Error en validarCheckIn: ' . $e->getMessage());
            return [
                'valido' => false,
                'mensaje' => 'Error en validación',
                'codigo_error' => 'ERROR_INTERNO'
            ];
        }
    }

    /**
     * Realizar check-in completo
     */
    public function realizarCheckIn(array $datos): array
    {
        try {
            DB::beginTransaction();

            $idpos = $datos['idpos'];
            $idUsuario = $datos['id_usuario'];
            $latitud = $datos['latitud'];
            $longitud = $datos['longitud'];
            $observaciones = $datos['observaciones'] ?? null;
            $fotos = $datos['fotos'] ?? [];

            // Validar check-in primero
            $validacion = $this->validarCheckIn([
                'idpos' => $idpos,
                'latitud' => $latitud,
                'longitud' => $longitud,
                'id_usuario' => $idUsuario,
                'tolerancia' => User::find($idUsuario)->tolerancia_visita ?? 100
            ]);

            if (!$validacion['valido']) {
                return [
                    'estado' => 0,
                    'msg' => $validacion['mensaje'],
                    'checkin' => null
                ];
            }

            // Verificar si continúa visita existente o crea nueva
            $visita = Visita::where('id_usuario', $idUsuario)
                ->where('id_pos', $idpos)
                ->where('estado', Visita::ESTADO_EN_PROCESO)
                ->first();

            if (!$visita) {
                // Crear nueva visita
                $visita = new Visita();
                $visita->id_pos = $idpos;
                $visita->id_usuario = $idUsuario;
                $visita->fecha = now()->toDateString();
                $visita->hora_inicio = now();
                $visita->latitud = $latitud;
                $visita->longitud = $longitud;
                $visita->precision_gps = $datos['precision_gps'] ?? null;
                $visita->efectiva = 1; // Asumimos efectiva al hacer check-in
                $visita->observaciones = $observaciones;
                $visita->estado = Visita::ESTADO_EN_PROCESO;
                $visita->fecha_creacion = now();
                $visita->save();

                $mensaje = 'Check-in realizado exitosamente. Visita iniciada.';
            } else {
                $mensaje = 'Check-in realizado exitosamente. Continuando visita existente.';
            }

            // Procesar fotos si las hay
            $fotosGuardadas = [];
            if (!empty($fotos)) {
                foreach ($fotos as $index => $foto) {
                    $nombreArchivo = "checkin_{$visita->id}_{$index}_" . time() . '.' . $foto->getClientOriginalExtension();
                    $rutaArchivo = $foto->storeAs("visitas/{$visita->id}/fotos", $nombreArchivo, 'public');
                    
                    $fotosGuardadas[] = [
                        'nombre' => $nombreArchivo,
                        'ruta' => $rutaArchivo,
                        'url' => asset('storage/' . $rutaArchivo)
                    ];
                }
            }

            // Actualizar contador de visitas del punto
            $punto = Point::find($idpos);
            if ($punto && !$visita->wasRecentlyCreated) {
                $punto->increment('cantidad_visitas');
            }

            DB::commit();

            return [
                'estado' => 1,
                'msg' => $mensaje,
                'checkin' => [
                    'id_visita' => $visita->id,
                    'idpos' => $idpos,
                    'fecha' => $visita->fecha->format('Y-m-d'),
                    'hora_inicio' => $visita->hora_inicio->format('H:i:s'),
                    'estado' => $visita->estado_nombre,
                    'distancia_punto' => round($validacion['distancia'], 2),
                    'fotos_subidas' => count($fotosGuardadas),
                    'fotos' => $fotosGuardadas,
                    'punto' => [
                        'nombre' => $punto->nombre,
                        'direccion' => $punto->direccion
                    ]
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error en realizarCheckIn: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al realizar check-in',
                'checkin' => null
            ];
        }
    }

    /**
     * Obtener configuración de check-in
     */
    public function getConfiguracionCheckIn(int $idUsuario): array
    {
        try {
            $usuario = User::find($idUsuario);
            $countryHelper = app('country.helper');

            return [
                'tolerancia_gps' => $usuario->tolerancia_visita ?? $countryHelper->getVisitTolerance(),
                'precision_gps_minima' => $countryHelper->getGpsPrecision(),
                'fotos_requeridas' => $countryHelper->isFeatureEnabled('required_photos'),
                'max_fotos_checkin' => 5,
                'formatos_foto_permitidos' => ['jpg', 'jpeg', 'png'],
                'tamaño_maximo_foto_mb' => 10,
                'encuestas_obligatorias' => $countryHelper->isFeatureEnabled('mandatory_surveys'),
                'validacion_ubicacion_estricta' => $countryHelper->isFeatureEnabled('strict_location_validation'),
                'tiempo_maximo_visita_horas' => 8,
                'permite_multiples_checkins' => true,
                'requiere_observaciones' => false
            ];

        } catch (\Exception $e) {
            Log::error('Error en getConfiguracionCheckIn: ' . $e->getMessage());
            return [
                'tolerancia_gps' => 100,
                'precision_gps_minima' => 50,
                'fotos_requeridas' => false
            ];
        }
    }

    /**
     * Obtener métricas del punto
     */
    public function getMetricasPunto(int $idpos, string $periodo, array $metricas): array
    {
        try {
            $fechaInicio = now()->subDays((int)$periodo);
            $resultado = [
                'idpos' => $idpos,
                'periodo_dias' => $periodo,
                'fecha_inicio' => $fechaInicio->format('Y-m-d'),
                'fecha_fin' => now()->format('Y-m-d'),
                'metricas' => []
            ];

            foreach ($metricas as $metrica) {
                switch ($metrica) {
                    case 'visitas':
                        $resultado['metricas']['visitas'] = $this->getMetricasVisitas($idpos, $fechaInicio);
                        break;
                    case 'ventas':
                        $resultado['metricas']['ventas'] = $this->getMetricasVentas($idpos, $fechaInicio);
                        break;
                    case 'inventario':
                        $resultado['metricas']['inventario'] = $this->getMetricasInventario($idpos, $fechaInicio);
                        break;
                    case 'encuestas':
                        $resultado['metricas']['encuestas'] = $this->getMetricasEncuestas($idpos, $fechaInicio);
                        break;
                }
            }

            return $resultado;

        } catch (\Exception $e) {
            Log::error('Error en getMetricasPunto: ' . $e->getMessage());
            return [
                'idpos' => $idpos,
                'metricas' => []
            ];
        }
    }

    /**
     * Actualizar información del punto
     */
    public function actualizarInfoPunto(array $datos): array
    {
        try {
            $idpos = $datos['idpos'];
            $idUsuario = $datos['id_usuario'];

            $infoPos = InfoPos::find($idpos);

            if (!$infoPos) {
                return [
                    'estado' => 0,
                    'msg' => 'Información del punto no encontrada',
                    'punto' => null
                ];
            }

            // Actualizar campos permitidos
            if (isset($datos['nombre_propietario'])) {
                $infoPos->nombre_propietario = $datos['nombre_propietario'];
            }

            if (isset($datos['telefono'])) {
                $infoPos->telefono = $datos['telefono'];
            }

            if (isset($datos['email'])) {
                $infoPos->email = $datos['email'];
            }

            if (isset($datos['estado_comercial'])) {
                $infoPos->estado_comercial = $datos['estado_comercial'];
            }

            $infoPos->save();

            return [
                'estado' => 1,
                'msg' => 'Información del punto actualizada exitosamente',
                'punto' => [
                    'idpos' => $infoPos->idpos,
                    'nombre_propietario' => $infoPos->nombre_propietario,
                    'telefono' => $infoPos->telefono,
                    'email' => $infoPos->email,
                    'estado_comercial' => $infoPos->estado_comercial,
                    'fecha_actualizacion' => now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en actualizarInfoPunto: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al actualizar la información del punto',
                'punto' => null
            ];
        }
    }

    // Métodos auxiliares privados

    private function getVisitasEsteMes(int $idpos): int
    {
        return Visita::where('id_pos', $idpos)
            ->whereMonth('fecha', now()->month)
            ->whereYear('fecha', now()->year)
            ->count();
    }

    private function getUltimaVisitaEfectiva(int $idpos): ?string
    {
        $visita = Visita::where('id_pos', $idpos)
            ->where('efectiva', 1)
            ->orderBy('fecha', 'desc')
            ->first();

        return $visita ? $visita->fecha->format('Y-m-d') : null;
    }

    private function getPromedioVisitasMes(int $idpos): float
    {
        $visitasPorMes = Visita::where('id_pos', $idpos)
            ->selectRaw('COUNT(*) as total, YEAR(fecha) as año, MONTH(fecha) as mes')
            ->groupBy('año', 'mes')
            ->get();

        return $visitasPorMes->avg('total') ?? 0;
    }

    private function getFotosRecientes(int $idpos): array
    {
        // Implementación para obtener fotos recientes del punto
        // Por ahora retornamos array vacío
        return [
            'total_fotos' => 0,
            'fotos_recientes' => []
        ];
    }

    private function getMetricasVisitas(int $idpos, $fechaInicio): array
    {
        $visitas = Visita::where('id_pos', $idpos)
            ->where('fecha', '>=', $fechaInicio)
            ->get();

        return [
            'total' => $visitas->count(),
            'efectivas' => $visitas->where('efectiva', 1)->count(),
            'promedio_duracion' => $visitas->where('duracion', '>', 0)->avg('duracion'),
            'por_dia' => $visitas->groupBy(function($visita) {
                return $visita->fecha->format('Y-m-d');
            })->map->count()
        ];
    }

    private function getMetricasVentas(int $idpos, $fechaInicio): array
    {
        $ventas = DetalleInventario::where('id_pos', $idpos)
            ->where('estado', DetalleInventario::ESTADO_VENDIDO)
            ->where('fecha_venta', '>=', $fechaInicio)
            ->get();

        return [
            'total_ventas' => $ventas->count(),
            'valor_total' => $ventas->sum('precio'),
            'promedio_venta' => $ventas->avg('precio'),
            'productos_mas_vendidos' => $ventas->groupBy('descripcion')
                ->map->count()
                ->sortDesc()
                ->take(5)
        ];
    }

    private function getMetricasInventario(int $idpos, $fechaInicio): array
    {
        $inventario = DetalleInventario::where('id_pos', $idpos)->get();

        return [
            'total_productos' => $inventario->count(),
            'disponibles' => $inventario->where('estado', DetalleInventario::ESTADO_DISPONIBLE)->count(),
            'valor_total' => $inventario->sum('valor_total'),
            'rotacion' => DetalleInventario::calcularRotacion($idpos, 30)
        ];
    }

    private function getMetricasEncuestas(int $idpos, $fechaInicio): array
    {
        $visitas = Visita::where('id_pos', $idpos)
            ->where('fecha', '>=', $fechaInicio)
            ->with('encuestas')
            ->get();

        $totalEncuestas = $visitas->sum(function($visita) {
            return $visita->encuestas->count();
        });

        return [
            'total_encuestas_respondidas' => $totalEncuestas,
            'visitas_con_encuestas' => $visitas->filter(function($visita) {
                return $visita->encuestas->count() > 0;
            })->count(),
            'promedio_encuestas_por_visita' => $visitas->count() > 0 ? $totalEncuestas / $visitas->count() : 0
        ];
    }
}
