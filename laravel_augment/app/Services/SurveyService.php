<?php

namespace App\Services;

use App\Models\Survey;
use App\Models\Question;
use App\Models\SurveyResponse;
use App\Models\Visita;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SurveyService
{
    /**
     * Obtener encuestas disponibles
     */
    public function getEncuestas(array $parametros): array
    {
        try {
            $tipo = $parametros['tipo'] ?? 'todas';
            $activasSolo = $parametros['activas_solo'] ?? true;
            $idpos = $parametros['idpos'] ?? null;

            $query = Survey::with(['preguntas.respuestasPosibles']);

            // Filtrar por tipo
            switch ($tipo) {
                case 'obligatorias':
                    $query->obligatorias();
                    break;
                case 'opcionales':
                    $query->opcionales();
                    break;
                // 'todas' no necesita filtro adicional
            }

            // Filtrar solo activas
            if ($activasSolo) {
                $query->activas();
            }

            $encuestas = $query->get();

            $resultado = [];
            foreach ($encuestas as $encuesta) {
                $resultado[] = [
                    'id' => $encuesta->id,
                    'titulo' => $encuesta->titulo,
                    'descripcion' => $encuesta->descripcion,
                    'obligatorio' => $encuesta->es_obligatoria,
                    'navegar_atras' => $encuesta->permite_navegacion,
                    'aplicar' => $encuesta->es_aplicable,
                    'estado_accion' => $encuesta->esta_activa,
                    'total_preguntas' => $encuesta->total_preguntas,
                    'preguntas_obligatorias' => $encuesta->preguntas()->where('obligatorio', 1)->count(),
                    'preguntas' => $encuesta->preguntas->map(function($pregunta) {
                        return [
                            'id' => $pregunta->id,
                            'pregunta' => $pregunta->pregunta,
                            'tipo' => $pregunta->tipo,
                            'tipo_nombre' => $pregunta->tipo_nombre,
                            'obligatorio' => $pregunta->es_obligatoria,
                            'orden' => $pregunta->orden,
                            'respuestas_posibles' => $pregunta->respuestasPosibles->map(function($respuesta) {
                                return [
                                    'id' => $respuesta->id,
                                    'respuesta' => $respuesta->respuesta,
                                    'orden' => $respuesta->orden
                                ];
                            })
                        ];
                    })
                ];
            }

            return [
                'total' => count($resultado),
                'tipo_filtro' => $tipo,
                'encuestas' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEncuestas: ' . $e->getMessage());
            return [
                'total' => 0,
                'encuestas' => []
            ];
        }
    }

    /**
     * Obtener encuestas específicas del operador
     */
    public function getEncuestasOperador(array $parametros): array
    {
        try {
            $fechaHora = $parametros['fechaHora'];
            $idpos = $parametros['idpos'] ?? null;
            $idUsuario = $parametros['id_usuario'];

            // Obtener encuestas activas y aplicables
            $encuestas = Survey::activas()->aplicables()->with(['preguntas.respuestasPosibles'])->get();

            $resultado = [];
            foreach ($encuestas as $encuesta) {
                // Verificar si la encuesta aplica para este punto/usuario
                $aplica = $this->verificarAplicabilidadEncuesta($encuesta->id, $idpos, $idUsuario);

                if ($aplica) {
                    $resultado[] = [
                        'id' => $encuesta->id,
                        'titulo' => $encuesta->titulo,
                        'descripcion' => $encuesta->descripcion,
                        'obligatorio' => $encuesta->es_obligatoria,
                        'navegar_atras' => $encuesta->permite_navegacion,
                        'fecha_consulta' => $fechaHora,
                        'preguntas' => $encuesta->preguntas->map(function($pregunta) {
                            return [
                                'id' => $pregunta->id,
                                'pregunta' => $pregunta->pregunta,
                                'tipo' => $pregunta->tipo,
                                'obligatorio' => $pregunta->es_obligatoria,
                                'orden' => $pregunta->orden,
                                'validaciones' => $pregunta->getValidaciones(),
                                'respuestas_posibles' => $pregunta->respuestasPosibles->map(function($respuesta) {
                                    return [
                                        'id' => $respuesta->id,
                                        'respuesta' => $respuesta->respuesta,
                                        'orden' => $respuesta->orden
                                    ];
                                })
                            ];
                        })
                    ];
                }
            }

            return [
                'total' => count($resultado),
                'fecha_hora' => $fechaHora,
                'idpos' => $idpos,
                'encuestas' => $resultado
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEncuestasOperador: ' . $e->getMessage());
            return [
                'total' => 0,
                'encuestas' => []
            ];
        }
    }

    /**
     * Guardar respuestas de encuesta
     */
    public function guardarEncuesta(array $datos): array
    {
        try {
            DB::beginTransaction();

            $idEncuesta = $datos['id_encuesta'];
            $idVisita = $datos['id_visita'];
            $idUsuario = $datos['id_usuario'];
            $respuestas = $datos['respuestas'];

            // Verificar que la encuesta existe
            $encuesta = Survey::find($idEncuesta);
            if (!$encuesta) {
                return [
                    'estado' => 0,
                    'msg' => 'Encuesta no encontrada',
                    'datos' => null
                ];
            }

            // Verificar que la visita existe y pertenece al usuario
            $visita = Visita::where('id', $idVisita)->where('id_usuario', $idUsuario)->first();
            if (!$visita) {
                return [
                    'estado' => 0,
                    'msg' => 'Visita no encontrada o no autorizada',
                    'datos' => null
                ];
            }

            // Validar respuestas
            $validacion = $this->validarRespuestas($idEncuesta, $respuestas);
            if (!$validacion['valido']) {
                return [
                    'estado' => 0,
                    'msg' => 'Respuestas inválidas: ' . implode(', ', $validacion['errores']),
                    'datos' => null
                ];
            }

            // Eliminar respuestas anteriores para esta encuesta y visita
            SurveyResponse::where('id_encuesta', $idEncuesta)
                          ->where('id_visita', $idVisita)
                          ->delete();

            // Guardar nuevas respuestas
            $respuestasGuardadas = [];
            foreach ($respuestas as $respuesta) {
                $surveyResponse = new SurveyResponse();
                $surveyResponse->id_encuesta = $idEncuesta;
                $surveyResponse->id_visita = $idVisita;
                $surveyResponse->id_pregunta = $respuesta['id_pregunta'];
                $surveyResponse->respuesta = $respuesta['respuesta'];
                $surveyResponse->fecha_respuesta = now();

                // Manejar archivo si existe
                if (isset($respuesta['archivo']) && $respuesta['archivo']) {
                    $archivo = $respuesta['archivo'];
                    $nombreArchivo = time() . '_' . $archivo->getClientOriginalName();
                    $rutaArchivo = $archivo->storeAs('encuestas/' . $idEncuesta, $nombreArchivo, 'public');
                    $surveyResponse->archivo_url = $rutaArchivo;
                }

                $surveyResponse->save();
                $respuestasGuardadas[] = $surveyResponse;
            }

            // Asociar encuesta con visita si no está asociada
            if (!$visita->encuestas()->where('id_encuesta', $idEncuesta)->exists()) {
                $visita->encuestas()->attach($idEncuesta, ['fecha_asociacion' => now()]);
            }

            DB::commit();

            return [
                'estado' => 1,
                'msg' => 'Encuesta guardada exitosamente',
                'datos' => [
                    'id_encuesta' => $idEncuesta,
                    'id_visita' => $idVisita,
                    'total_respuestas' => count($respuestasGuardadas),
                    'completitud' => $encuesta->getProgresoParaVisita($idVisita)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error en guardarEncuesta: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al guardar la encuesta',
                'datos' => null
            ];
        }
    }

    /**
     * Validar respuestas de encuesta
     */
    public function validarRespuestas(int $idEncuesta, array $respuestas): array
    {
        try {
            $encuesta = Survey::with('preguntas')->find($idEncuesta);
            if (!$encuesta) {
                return [
                    'valido' => false,
                    'errores' => ['Encuesta no encontrada']
                ];
            }

            $errores = [];
            $preguntasObligatorias = $encuesta->preguntas()->where('obligatorio', 1)->pluck('id')->toArray();
            $respuestasIds = array_column($respuestas, 'id_pregunta');

            // Verificar que todas las preguntas obligatorias tienen respuesta
            foreach ($preguntasObligatorias as $idPregunta) {
                if (!in_array($idPregunta, $respuestasIds)) {
                    $pregunta = Question::find($idPregunta);
                    $errores[] = "Falta respuesta para pregunta obligatoria: " . ($pregunta->pregunta ?? "ID $idPregunta");
                }
            }

            // Validar cada respuesta
            foreach ($respuestas as $respuesta) {
                $pregunta = Question::find($respuesta['id_pregunta']);
                if (!$pregunta) {
                    $errores[] = "Pregunta no encontrada: ID " . $respuesta['id_pregunta'];
                    continue;
                }

                // Validar según tipo de pregunta
                $validacionesPregunta = $pregunta->getValidaciones();
                foreach ($validacionesPregunta as $validacion) {
                    if ($validacion === 'required' && empty($respuesta['respuesta'])) {
                        $errores[] = "Respuesta requerida para: " . $pregunta->pregunta;
                    }
                    if ($validacion === 'numeric' && !is_numeric($respuesta['respuesta'])) {
                        $errores[] = "Respuesta debe ser numérica para: " . $pregunta->pregunta;
                    }
                    if ($validacion === 'date' && !strtotime($respuesta['respuesta'])) {
                        $errores[] = "Respuesta debe ser una fecha válida para: " . $pregunta->pregunta;
                    }
                }
            }

            return [
                'valido' => empty($errores),
                'errores' => $errores,
                'total_respuestas' => count($respuestas),
                'preguntas_obligatorias' => count($preguntasObligatorias)
            ];

        } catch (\Exception $e) {
            Log::error('Error en validarRespuestas: ' . $e->getMessage());
            return [
                'valido' => false,
                'errores' => ['Error interno en validación']
            ];
        }
    }

    /**
     * Verificar aplicabilidad de encuesta
     */
    private function verificarAplicabilidadEncuesta(int $idEncuesta, ?int $idpos, int $idUsuario): bool
    {
        try {
            // Por ahora, todas las encuestas activas son aplicables
            // Aquí se podría implementar lógica más compleja basada en:
            // - Tipo de punto
            // - Perfil de usuario
            // - Configuración específica del país/operador
            // - Reglas de negocio específicas

            return true;

        } catch (\Exception $e) {
            Log::error('Error en verificarAplicabilidadEncuesta: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtener detalle de una encuesta
     */
    public function getDetalleEncuesta(int $idEncuesta): ?array
    {
        try {
            $encuesta = Survey::with(['preguntas.respuestasPosibles'])->find($idEncuesta);

            if (!$encuesta) {
                return null;
            }

            return [
                'id' => $encuesta->id,
                'titulo' => $encuesta->titulo,
                'descripcion' => $encuesta->descripcion,
                'obligatorio' => $encuesta->es_obligatoria,
                'navegar_atras' => $encuesta->permite_navegacion,
                'aplicar' => $encuesta->es_aplicable,
                'estado_accion' => $encuesta->esta_activa,
                'estadisticas' => $encuesta->getEstadisticas(),
                'preguntas' => $encuesta->preguntas->map(function($pregunta) {
                    return [
                        'id' => $pregunta->id,
                        'pregunta' => $pregunta->pregunta,
                        'tipo' => $pregunta->tipo,
                        'tipo_nombre' => $pregunta->tipo_nombre,
                        'obligatorio' => $pregunta->es_obligatoria,
                        'orden' => $pregunta->orden,
                        'es_condicional' => $pregunta->es_condicional,
                        'validaciones' => $pregunta->getValidaciones(),
                        'respuestas_posibles' => $pregunta->respuestasPosibles->map(function($respuesta) {
                            return [
                                'id' => $respuesta->id,
                                'respuesta' => $respuesta->respuesta,
                                'orden' => $respuesta->orden
                            ];
                        }),
                        'estadisticas' => $pregunta->getEstadisticasRespuestas()
                    ];
                })
            ];

        } catch (\Exception $e) {
            Log::error('Error en getDetalleEncuesta: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Obtener preguntas de una encuesta
     */
    public function getPreguntasEncuesta(int $idEncuesta): array
    {
        try {
            $preguntas = Question::where('id_encuesta', $idEncuesta)
                ->with('respuestasPosibles')
                ->ordenadas()
                ->get();

            return $preguntas->map(function($pregunta) {
                return [
                    'id' => $pregunta->id,
                    'pregunta' => $pregunta->pregunta,
                    'tipo' => $pregunta->tipo,
                    'tipo_nombre' => $pregunta->tipo_nombre,
                    'obligatorio' => $pregunta->es_obligatoria,
                    'orden' => $pregunta->orden,
                    'es_condicional' => $pregunta->es_condicional,
                    'id_respuesta_padre' => $pregunta->id_respuesta,
                    'validaciones' => $pregunta->getValidaciones(),
                    'respuestas_posibles' => $pregunta->respuestasPosibles->map(function($respuesta) {
                        return [
                            'id' => $respuesta->id,
                            'respuesta' => $respuesta->respuesta,
                            'orden' => $respuesta->orden
                        ];
                    })
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Error en getPreguntasEncuesta: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener progreso de encuestas para una visita
     */
    public function getProgresoEncuestas(int $idVisita): array
    {
        try {
            $visita = Visita::with('encuestas')->find($idVisita);
            if (!$visita) {
                return [];
            }

            $progreso = [];
            foreach ($visita->encuestas as $encuesta) {
                $progresoEncuesta = $encuesta->getProgresoParaVisita($idVisita);
                $estaCompleta = $encuesta->estaCompletaParaVisita($idVisita);

                $progreso[] = [
                    'id_encuesta' => $encuesta->id,
                    'titulo' => $encuesta->titulo,
                    'obligatorio' => $encuesta->es_obligatoria,
                    'progreso_porcentaje' => $progresoEncuesta,
                    'esta_completa' => $estaCompleta,
                    'total_preguntas' => $encuesta->total_preguntas,
                    'respuestas_dadas' => $encuesta->respuestas()->where('id_visita', $idVisita)->count()
                ];
            }

            return $progreso;

        } catch (\Exception $e) {
            Log::error('Error en getProgresoEncuestas: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtener respuestas guardadas
     */
    public function getRespuestasGuardadas(int $idEncuesta, int $idVisita): array
    {
        try {
            $respuestas = SurveyResponse::where('id_encuesta', $idEncuesta)
                ->where('id_visita', $idVisita)
                ->with('pregunta')
                ->get();

            return $respuestas->map(function($respuesta) {
                return [
                    'id_pregunta' => $respuesta->id_pregunta,
                    'pregunta' => $respuesta->pregunta->pregunta ?? null,
                    'respuesta' => $respuesta->respuesta,
                    'archivo_url' => $respuesta->archivo_url,
                    'fecha_respuesta' => $respuesta->fecha_respuesta
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Error en getRespuestasGuardadas: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Completar encuesta
     */
    public function completarEncuesta(int $idEncuesta, int $idVisita, int $idUsuario): array
    {
        try {
            $encuesta = Survey::find($idEncuesta);
            if (!$encuesta) {
                return [
                    'estado' => 0,
                    'msg' => 'Encuesta no encontrada',
                    'datos' => null
                ];
            }

            // Verificar que la encuesta está completa
            $estaCompleta = $encuesta->estaCompletaParaVisita($idVisita);
            if (!$estaCompleta) {
                return [
                    'estado' => 0,
                    'msg' => 'La encuesta no está completa. Faltan respuestas obligatorias.',
                    'datos' => null
                ];
            }

            // Marcar como completada (esto podría ser un campo en una tabla pivot)
            // Por ahora, simplemente verificamos que esté completa

            return [
                'estado' => 1,
                'msg' => 'Encuesta completada exitosamente',
                'datos' => [
                    'id_encuesta' => $idEncuesta,
                    'id_visita' => $idVisita,
                    'progreso' => $encuesta->getProgresoParaVisita($idVisita),
                    'fecha_completada' => now()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en completarEncuesta: ' . $e->getMessage());
            return [
                'estado' => 0,
                'msg' => 'Error al completar la encuesta',
                'datos' => null
            ];
        }
    }

    /**
     * Obtener estadísticas de encuestas
     */
    public function getEstadisticasEncuestas(int $idUsuario, string $fechaInicio, string $fechaFin): array
    {
        try {
            $visitas = Visita::where('id_usuario', $idUsuario)
                ->whereBetween('fecha', [$fechaInicio, $fechaFin])
                ->with('encuestas')
                ->get();

            $totalVisitas = $visitas->count();
            $visitasConEncuestas = $visitas->filter(function($visita) {
                return $visita->encuestas->count() > 0;
            })->count();

            $encuestasRespondidas = 0;
            $encuestasCompletas = 0;
            $respuestasTotales = 0;

            foreach ($visitas as $visita) {
                foreach ($visita->encuestas as $encuesta) {
                    $encuestasRespondidas++;
                    if ($encuesta->estaCompletaParaVisita($visita->id)) {
                        $encuestasCompletas++;
                    }
                    $respuestasTotales += $encuesta->respuestas()->where('id_visita', $visita->id)->count();
                }
            }

            return [
                'periodo' => [
                    'fecha_inicio' => $fechaInicio,
                    'fecha_fin' => $fechaFin
                ],
                'visitas' => [
                    'total' => $totalVisitas,
                    'con_encuestas' => $visitasConEncuestas,
                    'porcentaje_con_encuestas' => $totalVisitas > 0 ? round(($visitasConEncuestas / $totalVisitas) * 100, 2) : 0
                ],
                'encuestas' => [
                    'respondidas' => $encuestasRespondidas,
                    'completas' => $encuestasCompletas,
                    'porcentaje_completitud' => $encuestasRespondidas > 0 ? round(($encuestasCompletas / $encuestasRespondidas) * 100, 2) : 0
                ],
                'respuestas' => [
                    'total' => $respuestasTotales,
                    'promedio_por_encuesta' => $encuestasRespondidas > 0 ? round($respuestasTotales / $encuestasRespondidas, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error en getEstadisticasEncuestas: ' . $e->getMessage());
            return [
                'visitas' => ['total' => 0, 'con_encuestas' => 0],
                'encuestas' => ['respondidas' => 0, 'completas' => 0],
                'respuestas' => ['total' => 0]
            ];
        }
    }
}
