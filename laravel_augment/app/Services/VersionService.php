<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class VersionService
{
    /**
     * Validar versión de la aplicación
     */
    public function validarVersion(string $versionName, int $versionCode): bool
    {
        try {
            $countryHelper = app('country.helper');
            $countryConfig = $countryHelper->getCountryConfig();
            
            // Obtener versión mínima requerida desde configuración
            $minVersion = $countryConfig['app']['min_version'] ?? '3.3.0';
            $currentVersion = $countryConfig['app']['version'] ?? '3.3.4';
            
            // Validar versión por nombre
            if ($this->compareVersions($versionName, $minVersion) < 0) {
                Log::warning("Versión rechazada: {$versionName} es menor que la mínima requerida: {$minVersion}");
                return false;
            }
            
            // Validar versión por código (opcional)
            $minVersionCode = $this->getVersionCodeFromName($minVersion);
            if ($versionCode < $minVersionCode) {
                Log::warning("Código de versión rechazado: {$versionCode} es menor que el mínimo requerido: {$minVersionCode}");
                return false;
            }
            
            Log::info("Versión validada exitosamente: {$versionName} (código: {$versionCode})");
            return true;
            
        } catch (\Exception $e) {
            Log::error('Error validando versión: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Comparar dos versiones en formato semántico (x.y.z)
     * Retorna: -1 si version1 < version2, 0 si son iguales, 1 si version1 > version2
     */
    private function compareVersions(string $version1, string $version2): int
    {
        return version_compare($version1, $version2);
    }
    
    /**
     * Obtener código de versión desde el nombre de versión
     * Convierte "3.3.4" a 334, "3.3.10" a 3310, etc.
     */
    private function getVersionCodeFromName(string $versionName): int
    {
        $parts = explode('.', $versionName);
        
        if (count($parts) !== 3) {
            return 0;
        }
        
        $major = (int)$parts[0];
        $minor = (int)$parts[1];
        $patch = (int)$parts[2];
        
        // Crear código de versión: major * 100 + minor * 10 + patch
        return ($major * 100) + ($minor * 10) + $patch;
    }
    
    /**
     * Obtener información de versión actual
     */
    public function getVersionInfo(): array
    {
        $countryHelper = app('country.helper');
        $countryConfig = $countryHelper->getCountryConfig();
        
        return [
            'current_version' => $countryConfig['app']['version'] ?? '3.3.4',
            'min_version' => $countryConfig['app']['min_version'] ?? '3.3.0',
            'app_name' => $countryConfig['app']['name'] ?? 'Operador Movistar',
            'country' => $countryHelper->getCurrentCountry(),
            'operator' => $countryConfig['operator'] ?? 'Movistar'
        ];
    }
    
    /**
     * Verificar si una versión específica está soportada
     */
    public function isVersionSupported(string $versionName): bool
    {
        $countryHelper = app('country.helper');
        $countryConfig = $countryHelper->getCountryConfig();
        
        $minVersion = $countryConfig['app']['min_version'] ?? '3.3.0';
        $maxVersion = $countryConfig['app']['max_version'] ?? '9.9.9'; // Sin límite superior por defecto
        
        return $this->compareVersions($versionName, $minVersion) >= 0 && 
               $this->compareVersions($versionName, $maxVersion) <= 0;
    }
    
    /**
     * Obtener lista de versiones soportadas
     */
    public function getSupportedVersions(): array
    {
        // Esta información podría venir de base de datos o configuración
        return [
            '3.3.0',
            '3.3.1',
            '3.3.2',
            '3.3.3',
            '3.3.4'
        ];
    }
    
    /**
     * Verificar si hay actualizaciones disponibles
     */
    public function hasUpdatesAvailable(string $currentVersion): bool
    {
        $countryHelper = app('country.helper');
        $countryConfig = $countryHelper->getCountryConfig();
        
        $latestVersion = $countryConfig['app']['version'] ?? '3.3.4';
        
        return $this->compareVersions($currentVersion, $latestVersion) < 0;
    }
    
    /**
     * Obtener URL de descarga para actualización
     */
    public function getUpdateUrl(): ?string
    {
        $countryHelper = app('country.helper');
        $countryConfig = $countryHelper->getCountryConfig();
        
        return $countryConfig['urls']['app_download'] ?? null;
    }
    
    /**
     * Registrar uso de versión para estadísticas
     */
    public function logVersionUsage(string $versionName, int $versionCode, int $userId): void
    {
        try {
            Log::info('Version usage', [
                'version_name' => $versionName,
                'version_code' => $versionCode,
                'user_id' => $userId,
                'country' => app('country.helper')->getCurrentCountry(),
                'timestamp' => now()
            ]);
            
            // Aquí se podría guardar en base de datos para estadísticas
            
        } catch (\Exception $e) {
            Log::error('Error logging version usage: ' . $e->getMessage());
        }
    }
}
