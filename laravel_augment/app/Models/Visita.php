<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Visita extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'visitas';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'id_pos',
        'id_usuario',
        'fecha',
        'hora_inicio',
        'hora_fin',
        'latitud',
        'longitud',
        'precision_gps',
        'efectiva',
        'motivo_no_efectiva',
        'observaciones',
        'estado',
        'fecha_creacion'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_pos' => 'integer',
        'id_usuario' => 'integer',
        'latitud' => 'float',
        'longitud' => 'float',
        'precision_gps' => 'float',
        'efectiva' => 'integer',
        'estado' => 'integer',
        'fecha' => 'date',
        'hora_inicio' => 'datetime',
        'hora_fin' => 'datetime',
        'fecha_creacion' => 'datetime'
    ];

    /**
     * Constantes para estados de visita
     */
    const ESTADO_PENDIENTE = 0;
    const ESTADO_EN_PROCESO = 1;
    const ESTADO_COMPLETADA = 2;
    const ESTADO_CANCELADA = 3;

    /**
     * Relación con el punto de venta
     */
    public function punto()
    {
        return $this->belongsTo(Point::class, 'id_pos', 'idpos');
    }

    /**
     * Relación con el usuario
     */
    public function usuario()
    {
        return $this->belongsTo(User::class, 'id_usuario', 'id');
    }

    /**
     * Relación con encuestas respondidas
     */
    public function encuestas()
    {
        return $this->belongsToMany(Survey::class, 'visita_encuestas', 'id_visita', 'id_encuesta');
    }

    /**
     * Relación con respuestas de encuestas
     */
    public function respuestasEncuestas()
    {
        return $this->hasMany(SurveyResponse::class, 'id_visita', 'id');
    }

    /**
     * Relación con detalles de la visita
     */
    public function detalles()
    {
        return $this->hasMany(VisitaDetalle::class, 'id_visita', 'id');
    }

    /**
     * Relación con archivos/imágenes de la visita
     */
    public function archivos()
    {
        return $this->hasMany(VisitaArchivo::class, 'id_visita', 'id');
    }

    /**
     * Relación con motivo de no efectiva
     */
    public function motivoNoEfectiva()
    {
        return $this->belongsTo(Motivo::class, 'motivo_no_efectiva', 'id');
    }

    /**
     * Scope para visitas efectivas
     */
    public function scopeEfectivas($query)
    {
        return $query->where('efectiva', 1);
    }

    /**
     * Scope para visitas no efectivas
     */
    public function scopeNoEfectivas($query)
    {
        return $query->where('efectiva', 0);
    }

    /**
     * Scope para visitas por estado
     */
    public function scopeByEstado($query, $estado)
    {
        return $query->where('estado', $estado);
    }

    /**
     * Scope para visitas completadas
     */
    public function scopeCompletadas($query)
    {
        return $query->where('estado', self::ESTADO_COMPLETADA);
    }

    /**
     * Scope para visitas en proceso
     */
    public function scopeEnProceso($query)
    {
        return $query->where('estado', self::ESTADO_EN_PROCESO);
    }

    /**
     * Scope para visitas por usuario
     */
    public function scopeByUsuario($query, $idUsuario)
    {
        return $query->where('id_usuario', $idUsuario);
    }

    /**
     * Scope para visitas por punto
     */
    public function scopeByPunto($query, $idPunto)
    {
        return $query->where('id_pos', $idPunto);
    }

    /**
     * Scope para visitas por fecha
     */
    public function scopeByFecha($query, $fecha)
    {
        return $query->whereDate('fecha', $fecha);
    }

    /**
     * Scope para visitas en rango de fechas
     */
    public function scopeEntreFechas($query, $fechaInicio, $fechaFin)
    {
        return $query->whereBetween('fecha', [$fechaInicio, $fechaFin]);
    }

    /**
     * Accessor para verificar si es efectiva
     */
    public function getEsEfectivaAttribute()
    {
        return $this->efectiva === 1;
    }

    /**
     * Accessor para verificar si está completada
     */
    public function getEstaCompletadaAttribute()
    {
        return $this->estado === self::ESTADO_COMPLETADA;
    }

    /**
     * Accessor para obtener la duración de la visita
     */
    public function getDuracionAttribute()
    {
        if (!$this->hora_inicio || !$this->hora_fin) {
            return null;
        }

        return $this->hora_inicio->diffInMinutes($this->hora_fin);
    }

    /**
     * Accessor para obtener el nombre del estado
     */
    public function getEstadoNombreAttribute()
    {
        $estados = [
            self::ESTADO_PENDIENTE => 'Pendiente',
            self::ESTADO_EN_PROCESO => 'En Proceso',
            self::ESTADO_COMPLETADA => 'Completada',
            self::ESTADO_CANCELADA => 'Cancelada'
        ];

        return $estados[$this->estado] ?? 'Desconocido';
    }

    /**
     * Verificar si tiene coordenadas válidas
     */
    public function tieneCoordenadas()
    {
        return !empty($this->latitud) && !empty($this->longitud) && 
               $this->latitud != 0 && $this->longitud != 0;
    }

    /**
     * Calcular distancia al punto de venta
     */
    public function calcularDistanciaAlPunto()
    {
        if (!$this->tieneCoordenadas() || !$this->punto || !$this->punto->tiene_coordenadas) {
            return null;
        }

        return $this->punto->calcularDistancia($this->latitud, $this->longitud);
    }

    /**
     * Verificar si está dentro de la tolerancia del punto
     */
    public function estaDentroDeTolerancia($tolerancia = null)
    {
        $tolerancia = $tolerancia ?: ($this->usuario->tolerancia_visita ?? 100);
        $distancia = $this->calcularDistanciaAlPunto();
        
        return $distancia !== null && $distancia <= $tolerancia;
    }

    /**
     * Marcar como completada
     */
    public function marcarComoCompletada()
    {
        $this->update([
            'estado' => self::ESTADO_COMPLETADA,
            'hora_fin' => now()
        ]);

        // Actualizar contador de visitas del punto
        if ($this->es_efectiva && $this->punto) {
            $this->punto->incrementarVisitas();
            $this->punto->marcarComoEfectivo();
        }
    }

    /**
     * Marcar como no efectiva
     */
    public function marcarComoNoEfectiva($motivoId, $observaciones = null)
    {
        $this->update([
            'efectiva' => 0,
            'motivo_no_efectiva' => $motivoId,
            'observaciones' => $observaciones,
            'estado' => self::ESTADO_COMPLETADA,
            'hora_fin' => now()
        ]);
    }

    /**
     * Obtener resumen de la visita
     */
    public function getResumenAttribute()
    {
        return [
            'id' => $this->id,
            'punto' => $this->punto ? $this->punto->nombre : null,
            'usuario' => $this->usuario ? $this->usuario->nombre_completo : null,
            'fecha' => $this->fecha->format('Y-m-d'),
            'hora_inicio' => $this->hora_inicio ? $this->hora_inicio->format('H:i:s') : null,
            'hora_fin' => $this->hora_fin ? $this->hora_fin->format('H:i:s') : null,
            'duracion_minutos' => $this->duracion,
            'efectiva' => $this->es_efectiva,
            'estado' => $this->estado_nombre,
            'distancia_punto' => $this->calcularDistanciaAlPunto(),
            'dentro_tolerancia' => $this->estaDentroDeTolerancia(),
            'total_encuestas' => $this->encuestas()->count(),
            'total_respuestas' => $this->respuestasEncuestas()->count()
        ];
    }

    /**
     * Verificar si todas las encuestas obligatorias están completas
     */
    public function tieneEncuestasObligatoriasCompletas()
    {
        $encuestasObligatorias = $this->encuestas()->where('obligatorio', 1)->get();
        
        foreach ($encuestasObligatorias as $encuesta) {
            if (!$encuesta->estaCompletaParaVisita($this->id)) {
                return false;
            }
        }
        
        return true;
    }
}
