<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Departamento extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'departamentos';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'descripcion'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer'
    ];

    /**
     * Relación con municipios
     */
    public function municipios()
    {
        return $this->hasMany(Municipio::class, 'id_depto', 'id');
    }

    /**
     * Relación con puntos de venta
     */
    public function puntos()
    {
        return $this->hasMany(InfoPos::class, 'id_departamento', 'id');
    }

    /**
     * Scope para buscar por descripción
     */
    public function scopeByDescripcion($query, $descripcion)
    {
        return $query->where('descripcion', 'like', '%' . $descripcion . '%');
    }

    /**
     * Obtener total de municipios
     */
    public function getTotalMunicipiosAttribute()
    {
        return $this->municipios()->count();
    }

    /**
     * Obtener total de puntos de venta
     */
    public function getTotalPuntosAttribute()
    {
        return $this->puntos()->count();
    }
}
