<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Municipio extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'municipios';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'descripcion',
        'id_depto'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_depto' => 'integer'
    ];

    /**
     * Relación con departamento
     */
    public function departamento()
    {
        return $this->belongsTo(Departamento::class, 'id_depto', 'id');
    }

    /**
     * Relación con puntos de venta
     */
    public function puntos()
    {
        return $this->hasMany(InfoPos::class, 'id_municipio', 'id');
    }

    /**
     * Scope para buscar por descripción
     */
    public function scopeByDescripcion($query, $descripcion)
    {
        return $query->where('descripcion', 'like', '%' . $descripcion . '%');
    }

    /**
     * Scope para buscar por departamento
     */
    public function scopeByDepartamento($query, $idDepartamento)
    {
        return $query->where('id_depto', $idDepartamento);
    }

    /**
     * Obtener total de puntos de venta
     */
    public function getTotalPuntosAttribute()
    {
        return $this->puntos()->count();
    }

    /**
     * Obtener nombre completo (Municipio, Departamento)
     */
    public function getNombreCompletoAttribute()
    {
        return $this->descripcion . ', ' . ($this->departamento->descripcion ?? '');
    }
}
