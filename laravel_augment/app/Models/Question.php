<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'preguntas';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'pregunta',
        'id_encuesta',
        'tipo',
        'id_respuesta',
        'obligatorio',
        'orden'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_encuesta' => 'integer',
        'tipo' => 'integer',
        'id_respuesta' => 'integer',
        'obligatorio' => 'integer',
        'orden' => 'integer'
    ];

    /**
     * Constantes para tipos de pregunta
     */
    const TIPO_TEXTO = 1;
    const TIPO_NUMERO = 2;
    const TIPO_SELECCION_SIMPLE = 3;
    const TIPO_SELECCION_MULTIPLE = 4;
    const TIPO_FECHA = 5;
    const TIPO_IMAGEN = 6;
    const TIPO_BOOLEAN = 7;

    /**
     * Relación con la encuesta
     */
    public function encuesta()
    {
        return $this->belongsTo(Survey::class, 'id_encuesta', 'id');
    }

    /**
     * Relación con respuesta padre (para preguntas condicionales)
     */
    public function respuestaPadre()
    {
        return $this->belongsTo(Answer::class, 'id_respuesta', 'id');
    }

    /**
     * Relación con respuestas posibles de esta pregunta
     */
    public function respuestasPosibles()
    {
        return $this->hasMany(Answer::class, 'id_pregunta', 'id')->orderBy('orden');
    }

    /**
     * Relación con respuestas dadas a esta pregunta
     */
    public function respuestasDadas()
    {
        return $this->hasMany(SurveyResponse::class, 'id_pregunta', 'id');
    }

    /**
     * Relación con preguntas hijas (condicionales)
     */
    public function preguntasHijas()
    {
        return $this->hasManyThrough(
            Question::class,
            Answer::class,
            'id_pregunta', // Foreign key en answers
            'id_respuesta', // Foreign key en questions
            'id', // Local key en questions
            'id' // Local key en answers
        );
    }

    /**
     * Scope para preguntas obligatorias
     */
    public function scopeObligatorias($query)
    {
        return $query->where('obligatorio', 1);
    }

    /**
     * Scope para preguntas opcionales
     */
    public function scopeOpcionales($query)
    {
        return $query->where('obligatorio', 0);
    }

    /**
     * Scope para preguntas por tipo
     */
    public function scopeByTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    /**
     * Scope para preguntas principales (sin padre)
     */
    public function scopePrincipales($query)
    {
        return $query->whereNull('id_respuesta');
    }

    /**
     * Scope para preguntas condicionales (con padre)
     */
    public function scopeCondicionales($query)
    {
        return $query->whereNotNull('id_respuesta');
    }

    /**
     * Scope ordenadas por orden
     */
    public function scopeOrdenadas($query)
    {
        return $query->orderBy('orden');
    }

    /**
     * Accessor para verificar si es obligatoria
     */
    public function getEsObligatoriaAttribute()
    {
        return $this->obligatorio === 1;
    }

    /**
     * Accessor para verificar si es condicional
     */
    public function getEsCondicionalAttribute()
    {
        return !empty($this->id_respuesta);
    }

    /**
     * Accessor para obtener el nombre del tipo
     */
    public function getTipoNombreAttribute()
    {
        $tipos = [
            self::TIPO_TEXTO => 'Texto',
            self::TIPO_NUMERO => 'Número',
            self::TIPO_SELECCION_SIMPLE => 'Selección Simple',
            self::TIPO_SELECCION_MULTIPLE => 'Selección Múltiple',
            self::TIPO_FECHA => 'Fecha',
            self::TIPO_IMAGEN => 'Imagen',
            self::TIPO_BOOLEAN => 'Sí/No'
        ];

        return $tipos[$this->tipo] ?? 'Desconocido';
    }

    /**
     * Verificar si requiere respuestas predefinidas
     */
    public function requiereRespuestasPredefinidas()
    {
        return in_array($this->tipo, [
            self::TIPO_SELECCION_SIMPLE,
            self::TIPO_SELECCION_MULTIPLE,
            self::TIPO_BOOLEAN
        ]);
    }

    /**
     * Verificar si permite múltiples respuestas
     */
    public function permiteMultiplesRespuestas()
    {
        return $this->tipo === self::TIPO_SELECCION_MULTIPLE;
    }

    /**
     * Verificar si requiere archivo
     */
    public function requiereArchivo()
    {
        return $this->tipo === self::TIPO_IMAGEN;
    }

    /**
     * Obtener validaciones para esta pregunta
     */
    public function getValidaciones()
    {
        $validaciones = [];

        if ($this->es_obligatoria) {
            $validaciones[] = 'required';
        }

        switch ($this->tipo) {
            case self::TIPO_NUMERO:
                $validaciones[] = 'numeric';
                break;
            case self::TIPO_FECHA:
                $validaciones[] = 'date';
                break;
            case self::TIPO_IMAGEN:
                $validaciones[] = 'image';
                $validaciones[] = 'max:10240'; // 10MB
                break;
            case self::TIPO_BOOLEAN:
                $validaciones[] = 'boolean';
                break;
        }

        return $validaciones;
    }

    /**
     * Verificar si debe mostrarse basado en respuestas anteriores
     */
    public function debeMostrarse($respuestasAnteriores = [])
    {
        if (!$this->es_condicional) {
            return true;
        }

        // Verificar si la respuesta padre está en las respuestas anteriores
        return in_array($this->id_respuesta, $respuestasAnteriores);
    }

    /**
     * Obtener estadísticas de respuestas para esta pregunta
     */
    public function getEstadisticasRespuestas()
    {
        $totalRespuestas = $this->respuestasDadas()->count();
        $estadisticas = [
            'total_respuestas' => $totalRespuestas,
            'respuestas_por_opcion' => []
        ];

        if ($this->requiereRespuestasPredefinidas()) {
            foreach ($this->respuestasPosibles as $respuesta) {
                $count = $this->respuestasDadas()
                    ->where('respuesta', $respuesta->respuesta)
                    ->count();
                
                $estadisticas['respuestas_por_opcion'][$respuesta->respuesta] = [
                    'count' => $count,
                    'porcentaje' => $totalRespuestas > 0 ? round(($count / $totalRespuestas) * 100, 2) : 0
                ];
            }
        }

        return $estadisticas;
    }

    /**
     * Clonar pregunta con sus respuestas
     */
    public function clonar($nuevaEncuestaId)
    {
        $nuevaPregunta = $this->replicate();
        $nuevaPregunta->id_encuesta = $nuevaEncuestaId;
        $nuevaPregunta->save();

        // Clonar respuestas posibles
        foreach ($this->respuestasPosibles as $respuesta) {
            $nuevaRespuesta = $respuesta->replicate();
            $nuevaRespuesta->id_pregunta = $nuevaPregunta->id;
            $nuevaRespuesta->save();
        }

        return $nuevaPregunta;
    }
}
