<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ArchivoSubido extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'archivos_subidos';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'nombre_original',
        'nombre_archivo',
        'ruta_archivo',
        'tipo',
        'categoria',
        'id_referencia',
        'tamaño_bytes',
        'tipo_mime',
        'descripcion',
        'publico',
        'id_usuario',
        'fecha_subida',
        'metadatos',
        'descargas'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_referencia' => 'integer',
        'tamaño_bytes' => 'integer',
        'publico' => 'boolean',
        'id_usuario' => 'integer',
        'fecha_subida' => 'datetime',
        'metadatos' => 'array',
        'descargas' => 'integer'
    ];

    /**
     * Relación con el usuario propietario
     */
    public function usuario()
    {
        return $this->belongsTo(User::class, 'id_usuario', 'id');
    }

    /**
     * Scope para archivos públicos
     */
    public function scopePublicos($query)
    {
        return $query->where('publico', true);
    }

    /**
     * Scope para archivos por tipo
     */
    public function scopeByTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    /**
     * Scope para archivos por categoría
     */
    public function scopeByCategoria($query, $categoria)
    {
        return $query->where('categoria', $categoria);
    }

    /**
     * Scope para archivos por referencia
     */
    public function scopeByReferencia($query, $categoria, $idReferencia)
    {
        return $query->where('categoria', $categoria)
                     ->where('id_referencia', $idReferencia);
    }

    /**
     * Scope para archivos del usuario
     */
    public function scopeByUsuario($query, $idUsuario)
    {
        return $query->where('id_usuario', $idUsuario);
    }

    /**
     * Scope para archivos recientes
     */
    public function scopeRecientes($query, $dias = 7)
    {
        return $query->where('fecha_subida', '>=', now()->subDays($dias));
    }

    /**
     * Accessor para obtener URL completa
     */
    public function getUrlCompletaAttribute()
    {
        return asset('storage/' . $this->ruta_archivo);
    }

    /**
     * Accessor para obtener tamaño legible
     */
    public function getTamañoLegibleAttribute()
    {
        return $this->formatearTamaño($this->tamaño_bytes);
    }

    /**
     * Accessor para verificar si es imagen
     */
    public function getEsImagenAttribute()
    {
        return $this->tipo === 'imagen';
    }

    /**
     * Accessor para verificar si es documento
     */
    public function getEsDocumentoAttribute()
    {
        return $this->tipo === 'documento';
    }

    /**
     * Accessor para verificar si es video
     */
    public function getEsVideoAttribute()
    {
        return $this->tipo === 'video';
    }

    /**
     * Accessor para verificar si es audio
     */
    public function getEsAudioAttribute()
    {
        return $this->tipo === 'audio';
    }

    /**
     * Accessor para obtener extensión del archivo
     */
    public function getExtensionAttribute()
    {
        return strtolower(pathinfo($this->nombre_original, PATHINFO_EXTENSION));
    }

    /**
     * Accessor para obtener información completa
     */
    public function getInfoCompletaAttribute()
    {
        return [
            'id' => $this->id,
            'nombre_original' => $this->nombre_original,
            'nombre_archivo' => $this->nombre_archivo,
            'url' => $this->url_completa,
            'tipo' => $this->tipo,
            'categoria' => $this->categoria,
            'tamaño_bytes' => $this->tamaño_bytes,
            'tamaño_legible' => $this->tamaño_legible,
            'tipo_mime' => $this->tipo_mime,
            'extension' => $this->extension,
            'descripcion' => $this->descripcion,
            'publico' => $this->publico,
            'fecha_subida' => $this->fecha_subida ? $this->fecha_subida->format('Y-m-d H:i:s') : null,
            'metadatos' => $this->metadatos,
            'descargas' => $this->descargas ?? 0,
            'propietario' => $this->usuario ? $this->usuario->nombre_completo : null
        ];
    }

    /**
     * Verificar si el usuario tiene permisos para acceder al archivo
     */
    public function puedeAcceder(int $idUsuario): bool
    {
        return $this->publico || $this->id_usuario === $idUsuario;
    }

    /**
     * Incrementar contador de descargas
     */
    public function registrarDescarga(): void
    {
        $this->increment('descargas');
    }

    /**
     * Obtener archivos relacionados (misma categoría y referencia)
     */
    public function getArchivosRelacionados(int $limite = 5)
    {
        return self::where('categoria', $this->categoria)
            ->where('id_referencia', $this->id_referencia)
            ->where('id', '!=', $this->id)
            ->orderBy('fecha_subida', 'desc')
            ->limit($limite)
            ->get();
    }

    /**
     * Formatear tamaño en bytes a formato legible
     */
    private function formatearTamaño(int $bytes): string
    {
        $unidades = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($unidades) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $unidades[$pow];
    }

    /**
     * Obtener icono según el tipo de archivo
     */
    public function getIconoAttribute(): string
    {
        switch ($this->tipo) {
            case 'imagen':
                return 'fas fa-image';
            case 'documento':
                return 'fas fa-file-alt';
            case 'video':
                return 'fas fa-video';
            case 'audio':
                return 'fas fa-music';
            default:
                return 'fas fa-file';
        }
    }

    /**
     * Obtener color según el tipo de archivo
     */
    public function getColorAttribute(): string
    {
        switch ($this->tipo) {
            case 'imagen':
                return 'success';
            case 'documento':
                return 'primary';
            case 'video':
                return 'warning';
            case 'audio':
                return 'info';
            default:
                return 'secondary';
        }
    }
}
