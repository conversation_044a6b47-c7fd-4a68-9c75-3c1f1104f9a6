<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'usuarios';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'cedula',
        'nombre',
        'apellido',
        'user',
        'pass',
        'perfil',
        'id_perfil',
        'estado_pass',
        'fecha_hora',
        'tolerancia_visita',
        'email'
    ];

    /**
     * Los atributos que deben ocultarse para la serialización
     */
    protected $hidden = [
        'pass',
        'remember_token',
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_perfil' => 'integer',
        'estado_pass' => 'integer',
        'tolerancia_visita' => 'integer',
        'fecha_hora' => 'datetime',
    ];

    /**
     * Obtener el identificador que será almacenado en el claim 'sub' del JWT
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Retornar un array de claims personalizados para ser añadidos al JWT
     */
    public function getJWTCustomClaims()
    {
        return [
            'perfil' => $this->perfil,
            'id_perfil' => $this->id_perfil,
            'cedula' => $this->cedula,
            'nombre_completo' => $this->nombre . ' ' . $this->apellido
        ];
    }

    /**
     * Obtener el campo de contraseña para autenticación
     */
    public function getAuthPassword()
    {
        return $this->pass;
    }

    /**
     * Obtener el nombre de usuario para autenticación
     */
    public function getAuthIdentifierName()
    {
        return 'user';
    }

    /**
     * Obtener el identificador único para autenticación
     */
    public function getAuthIdentifier()
    {
        return $this->user;
    }

    /**
     * Relación con visitas
     */
    public function visitas()
    {
        return $this->hasMany(Visita::class, 'id_usuario', 'id');
    }

    /**
     * Relación con permisos DCS
     */
    public function permisosDcs()
    {
        return $this->hasMany(PermisoDcs::class, 'id_usuario', 'id');
    }

    /**
     * Accessor para obtener el nombre completo
     */
    public function getNombreCompletoAttribute()
    {
        return trim($this->nombre . ' ' . $this->apellido);
    }

    /**
     * Accessor para verificar si es primer logueo
     */
    public function getPrimerLogueoAttribute()
    {
        return $this->estado_pass === 1;
    }

    /**
     * Scope para buscar por username
     */
    public function scopeByUsername($query, $username)
    {
        return $query->where('user', $username);
    }

    /**
     * Scope para buscar por email
     */
    public function scopeByEmail($query, $email)
    {
        return $query->where('email', $email);
    }

    /**
     * Scope para usuarios activos
     */
    public function scopeActivos($query)
    {
        return $query->where('estado_pass', '!=', 0);
    }

    /**
     * Verificar si el usuario tiene un perfil específico
     */
    public function tienePerfil($perfil)
    {
        return $this->perfil === $perfil || $this->id_perfil === $perfil;
    }

    /**
     * Verificar credenciales de usuario
     */
    public function verificarCredenciales($password)
    {
        return password_verify($password, $this->pass);
    }
}
