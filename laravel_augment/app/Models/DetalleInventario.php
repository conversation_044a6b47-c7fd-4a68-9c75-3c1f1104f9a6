<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DetalleInventario extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'detalle_inventario';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'id_pos',
        'id_referencia',
        'serial',
        'descripcion',
        'cantidad',
        'precio',
        'estado',
        'fecha_ingreso',
        'fecha_venta',
        'observaciones'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_pos' => 'integer',
        'id_referencia' => 'integer',
        'cantidad' => 'integer',
        'precio' => 'decimal:2',
        'fecha_ingreso' => 'datetime',
        'fecha_venta' => 'datetime'
    ];

    /**
     * Constantes para estados de inventario
     */
    const ESTADO_DISPONIBLE = 'DISPONIBLE';
    const ESTADO_VENDIDO = 'VENDIDO';
    const ESTADO_DEFECTUOSO = 'DEFECTUOSO';
    const ESTADO_DEVUELTO = 'DEVUELTO';

    /**
     * Relación con el punto de venta
     */
    public function punto()
    {
        return $this->belongsTo(Point::class, 'id_pos', 'idpos');
    }

    /**
     * Relación con la referencia del producto
     */
    public function referencia()
    {
        return $this->belongsTo(ReferenciaProducto::class, 'id_referencia', 'id');
    }

    /**
     * Scope para inventario disponible
     */
    public function scopeDisponible($query)
    {
        return $query->where('estado', self::ESTADO_DISPONIBLE);
    }

    /**
     * Scope para inventario vendido
     */
    public function scopeVendido($query)
    {
        return $query->where('estado', self::ESTADO_VENDIDO);
    }

    /**
     * Scope para inventario defectuoso
     */
    public function scopeDefectuoso($query)
    {
        return $query->where('estado', self::ESTADO_DEFECTUOSO);
    }

    /**
     * Scope para buscar por serial
     */
    public function scopeBySerial($query, $serial)
    {
        return $query->where('serial', $serial);
    }

    /**
     * Scope para buscar por punto
     */
    public function scopeByPunto($query, $idPunto)
    {
        return $query->where('id_pos', $idPunto);
    }

    /**
     * Scope para buscar por referencia
     */
    public function scopeByReferencia($query, $idReferencia)
    {
        return $query->where('id_referencia', $idReferencia);
    }

    /**
     * Scope para inventario en rango de fechas
     */
    public function scopeEntreFechas($query, $fechaInicio, $fechaFin)
    {
        return $query->whereBetween('fecha_ingreso', [$fechaInicio, $fechaFin]);
    }

    /**
     * Accessor para verificar si está disponible
     */
    public function getEstaDisponibleAttribute()
    {
        return $this->estado === self::ESTADO_DISPONIBLE;
    }

    /**
     * Accessor para verificar si está vendido
     */
    public function getEstaVendidoAttribute()
    {
        return $this->estado === self::ESTADO_VENDIDO;
    }

    /**
     * Accessor para obtener días en inventario
     */
    public function getDiasEnInventarioAttribute()
    {
        if (!$this->fecha_ingreso) {
            return 0;
        }

        $fechaFin = $this->fecha_venta ?: now();
        return $this->fecha_ingreso->diffInDays($fechaFin);
    }

    /**
     * Accessor para obtener valor total
     */
    public function getValorTotalAttribute()
    {
        return $this->cantidad * $this->precio;
    }

    /**
     * Marcar como vendido
     */
    public function marcarComoVendido()
    {
        $this->update([
            'estado' => self::ESTADO_VENDIDO,
            'fecha_venta' => now()
        ]);
    }

    /**
     * Marcar como defectuoso
     */
    public function marcarComoDefectuoso($observaciones = null)
    {
        $this->update([
            'estado' => self::ESTADO_DEFECTUOSO,
            'observaciones' => $observaciones
        ]);
    }

    /**
     * Devolver al inventario
     */
    public function devolverAlInventario($observaciones = null)
    {
        $this->update([
            'estado' => self::ESTADO_DISPONIBLE,
            'fecha_venta' => null,
            'observaciones' => $observaciones
        ]);
    }

    /**
     * Obtener información completa del producto
     */
    public function getInfoCompletaAttribute()
    {
        return [
            'id' => $this->id,
            'serial' => $this->serial,
            'descripcion' => $this->descripcion,
            'referencia' => $this->referencia ? $this->referencia->descripcion : null,
            'cantidad' => $this->cantidad,
            'precio' => $this->precio,
            'valor_total' => $this->valor_total,
            'estado' => $this->estado,
            'dias_inventario' => $this->dias_en_inventario,
            'fecha_ingreso' => $this->fecha_ingreso ? $this->fecha_ingreso->format('Y-m-d') : null,
            'fecha_venta' => $this->fecha_venta ? $this->fecha_venta->format('Y-m-d') : null,
            'punto' => $this->punto ? $this->punto->nombre : null
        ];
    }

    /**
     * Calcular rotación de inventario
     */
    public static function calcularRotacion($idPunto, $periodo = 30)
    {
        $fechaInicio = now()->subDays($periodo);
        
        $inventarioPromedio = self::where('id_pos', $idPunto)
            ->whereBetween('fecha_ingreso', [$fechaInicio, now()])
            ->avg('cantidad');
            
        $ventasEnPeriodo = self::where('id_pos', $idPunto)
            ->where('estado', self::ESTADO_VENDIDO)
            ->whereBetween('fecha_venta', [$fechaInicio, now()])
            ->sum('cantidad');
            
        if ($inventarioPromedio > 0) {
            return round($ventasEnPeriodo / $inventarioPromedio, 2);
        }
        
        return 0;
    }
}
