<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Point extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'puntos';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'idpos';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'nombre',
        'fecha',
        'hora',
        'efectiva',
        'direccion',
        'encuestas_respondidas',
        'cantidad_visitas',
        'precisiongps',
        'latitud',
        'longitud',
        'id_distri'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'idpos' => 'integer',
        'efectiva' => 'integer',
        'cantidad_visitas' => 'integer',
        'precisiongps' => 'float',
        'latitud' => 'float',
        'longitud' => 'float',
        'id_distri' => 'integer',
        'fecha' => 'date',
        'hora' => 'datetime'
    ];

    /**
     * Relación con visitas
     */
    public function visitas()
    {
        return $this->hasMany(Visita::class, 'id_pos', 'idpos');
    }

    /**
     * Relación con inventarios
     */
    public function inventarios()
    {
        return $this->hasMany(DetalleInventario::class, 'id_pos', 'idpos');
    }

    /**
     * Relación con información del punto (InfoPos)
     */
    public function infoPos()
    {
        return $this->hasOne(InfoPos::class, 'idpos', 'idpos');
    }

    /**
     * Relación con información DCS
     */
    public function dcsInfo()
    {
        return $this->hasOne(DcsPoint::class, 'id', 'idpos');
    }

    /**
     * Relación con el agente/distribuidor
     */
    public function agente()
    {
        return $this->belongsTo(User::class, 'id_distri', 'id');
    }

    /**
     * Scope para puntos efectivos
     */
    public function scopeEfectivos($query)
    {
        return $query->where('efectiva', 1);
    }

    /**
     * Scope para puntos no efectivos
     */
    public function scopeNoEfectivos($query)
    {
        return $query->where('efectiva', 0);
    }

    /**
     * Scope para buscar por nombre
     */
    public function scopeByNombre($query, $nombre)
    {
        return $query->where('nombre', 'like', '%' . $nombre . '%');
    }

    /**
     * Scope para buscar por agente
     */
    public function scopeByAgente($query, $idAgente)
    {
        return $query->where('id_distri', $idAgente);
    }

    /**
     * Scope para puntos con coordenadas
     */
    public function scopeConCoordenadas($query)
    {
        return $query->whereNotNull('latitud')
                    ->whereNotNull('longitud')
                    ->where('latitud', '!=', 0)
                    ->where('longitud', '!=', 0);
    }

    /**
     * Accessor para verificar si tiene coordenadas válidas
     */
    public function getTieneCoordendasAttribute()
    {
        return !empty($this->latitud) && !empty($this->longitud) && 
               $this->latitud != 0 && $this->longitud != 0;
    }

    /**
     * Accessor para obtener la última visita
     */
    public function getUltimaVisitaAttribute()
    {
        return $this->visitas()->latest('fecha')->first();
    }

    /**
     * Calcular distancia a un punto específico
     */
    public function calcularDistancia($latitud, $longitud)
    {
        if (!$this->tiene_coordenadas) {
            return null;
        }

        $earthRadius = 6371000; // Radio de la Tierra en metros

        $latFrom = deg2rad($this->latitud);
        $lonFrom = deg2rad($this->longitud);
        $latTo = deg2rad($latitud);
        $lonTo = deg2rad($longitud);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Verificar si está dentro del rango de tolerancia
     */
    public function estaDentroDeTolerancia($latitud, $longitud, $tolerancia = 100)
    {
        $distancia = $this->calcularDistancia($latitud, $longitud);
        return $distancia !== null && $distancia <= $tolerancia;
    }

    /**
     * Incrementar contador de visitas
     */
    public function incrementarVisitas()
    {
        $this->increment('cantidad_visitas');
    }

    /**
     * Marcar como visitado efectivamente
     */
    public function marcarComoEfectivo()
    {
        $this->update(['efectiva' => 1]);
    }
}
