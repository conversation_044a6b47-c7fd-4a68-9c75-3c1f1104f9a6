<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReferenciaProducto extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'referencias_productos';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = true;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'codigo',
        'descripcion',
        'categoria',
        'precio_sugerido',
        'codigo_barras',
        'marca',
        'modelo',
        'especificaciones',
        'activo'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'precio_sugerido' => 'decimal:2',
        'activo' => 'boolean',
        'especificaciones' => 'array'
    ];

    /**
     * Relación con detalles de inventario
     */
    public function inventarios()
    {
        return $this->hasMany(DetalleInventario::class, 'id_referencia', 'id');
    }

    /**
     * Scope para referencias activas
     */
    public function scopeActivas($query)
    {
        return $query->where('activo', true);
    }

    /**
     * Scope para buscar por categoría
     */
    public function scopeByCategoria($query, $categoria)
    {
        return $query->where('categoria', $categoria);
    }

    /**
     * Scope para buscar por código
     */
    public function scopeByCodigo($query, $codigo)
    {
        return $query->where('codigo', $codigo);
    }

    /**
     * Scope para buscar por código de barras
     */
    public function scopeByCodigoBarras($query, $codigoBarras)
    {
        return $query->where('codigo_barras', $codigoBarras);
    }

    /**
     * Scope para buscar por descripción
     */
    public function scopeByDescripcion($query, $descripcion)
    {
        return $query->where('descripcion', 'like', '%' . $descripcion . '%');
    }

    /**
     * Accessor para obtener el nombre completo
     */
    public function getNombreCompletoAttribute()
    {
        $nombre = $this->descripcion;
        
        if ($this->marca) {
            $nombre = $this->marca . ' ' . $nombre;
        }
        
        if ($this->modelo) {
            $nombre .= ' - ' . $this->modelo;
        }
        
        return $nombre;
    }

    /**
     * Accessor para verificar si está en stock
     */
    public function getEnStockAttribute()
    {
        return $this->inventarios()
            ->where('estado', DetalleInventario::ESTADO_DISPONIBLE)
            ->sum('cantidad') > 0;
    }

    /**
     * Obtener total en stock
     */
    public function getTotalStockAttribute()
    {
        return $this->inventarios()
            ->where('estado', DetalleInventario::ESTADO_DISPONIBLE)
            ->sum('cantidad');
    }

    /**
     * Obtener total vendido
     */
    public function getTotalVendidoAttribute()
    {
        return $this->inventarios()
            ->where('estado', DetalleInventario::ESTADO_VENDIDO)
            ->sum('cantidad');
    }

    /**
     * Obtener información completa del producto
     */
    public function getInfoCompletaAttribute()
    {
        return [
            'id' => $this->id,
            'codigo' => $this->codigo,
            'descripcion' => $this->descripcion,
            'nombre_completo' => $this->nombre_completo,
            'categoria' => $this->categoria,
            'marca' => $this->marca,
            'modelo' => $this->modelo,
            'precio_sugerido' => $this->precio_sugerido,
            'codigo_barras' => $this->codigo_barras,
            'especificaciones' => $this->especificaciones,
            'activo' => $this->activo,
            'en_stock' => $this->en_stock,
            'total_stock' => $this->total_stock,
            'total_vendido' => $this->total_vendido
        ];
    }

    /**
     * Obtener estadísticas de la referencia
     */
    public function getEstadisticas(string $fechaInicio = null, string $fechaFin = null)
    {
        $query = $this->inventarios();

        if ($fechaInicio && $fechaFin) {
            $query->whereBetween('fecha_ingreso', [$fechaInicio, $fechaFin]);
        }

        $inventarios = $query->get();

        return [
            'total_unidades' => $inventarios->sum('cantidad'),
            'valor_total' => $inventarios->sum('valor_total'),
            'disponibles' => $inventarios->where('estado', DetalleInventario::ESTADO_DISPONIBLE)->sum('cantidad'),
            'vendidas' => $inventarios->where('estado', DetalleInventario::ESTADO_VENDIDO)->sum('cantidad'),
            'defectuosas' => $inventarios->where('estado', DetalleInventario::ESTADO_DEFECTUOSO)->sum('cantidad'),
            'puntos_con_stock' => $inventarios->where('estado', DetalleInventario::ESTADO_DISPONIBLE)
                ->pluck('id_pos')->unique()->count(),
            'precio_promedio' => $inventarios->avg('precio'),
            'rotacion' => $this->calcularRotacion($fechaInicio, $fechaFin)
        ];
    }

    /**
     * Calcular rotación de la referencia
     */
    private function calcularRotacion(string $fechaInicio = null, string $fechaFin = null)
    {
        $fechaInicio = $fechaInicio ?: now()->subDays(30)->toDateString();
        $fechaFin = $fechaFin ?: now()->toDateString();

        $inventarioPromedio = $this->inventarios()
            ->whereBetween('fecha_ingreso', [$fechaInicio, $fechaFin])
            ->avg('cantidad');

        $ventasEnPeriodo = $this->inventarios()
            ->where('estado', DetalleInventario::ESTADO_VENDIDO)
            ->whereBetween('fecha_venta', [$fechaInicio, $fechaFin])
            ->sum('cantidad');

        if ($inventarioPromedio > 0) {
            return round($ventasEnPeriodo / $inventarioPromedio, 2);
        }

        return 0;
    }

    /**
     * Obtener puntos donde está disponible
     */
    public function getPuntosDisponibles()
    {
        return $this->inventarios()
            ->with('punto')
            ->where('estado', DetalleInventario::ESTADO_DISPONIBLE)
            ->get()
            ->groupBy('id_pos')
            ->map(function($inventarios, $idPos) {
                $punto = $inventarios->first()->punto;
                return [
                    'idpos' => $idPos,
                    'nombre' => $punto ? $punto->nombre : null,
                    'direccion' => $punto ? $punto->direccion : null,
                    'stock' => $inventarios->sum('cantidad'),
                    'precio_promedio' => $inventarios->avg('precio')
                ];
            })
            ->values();
    }
}
