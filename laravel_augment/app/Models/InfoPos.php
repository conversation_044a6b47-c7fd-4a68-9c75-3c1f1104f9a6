<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InfoPos extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'info_pos';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'idpos';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = false;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'idpos',
        'nombre',
        'nombre_propietario',
        'circuito',
        'ruta',
        'direccion',
        'telefono',
        'tel_op',
        'latitud',
        'longitud',
        'distribuidor',
        'fecha_visita',
        'categoria',
        'estado_comercial',
        'id_departamento',
        'id_municipio'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'idpos' => 'integer',
        'latitud' => 'float',
        'longitud' => 'float',
        'id_departamento' => 'integer',
        'id_municipio' => 'integer',
        'fecha_visita' => 'datetime'
    ];

    /**
     * Relación con el punto principal
     */
    public function punto()
    {
        return $this->belongsTo(Point::class, 'idpos', 'idpos');
    }

    /**
     * Relación con departamento
     */
    public function departamento()
    {
        return $this->belongsTo(Departamento::class, 'id_departamento', 'id');
    }

    /**
     * Relación con municipio
     */
    public function municipio()
    {
        return $this->belongsTo(Municipio::class, 'id_municipio', 'id');
    }

    /**
     * Relación con estado comercial
     */
    public function estadoComercial()
    {
        return $this->belongsTo(EstadoComercial::class, 'estado_comercial', 'id');
    }

    /**
     * Relación con categoría
     */
    public function categoriaInfo()
    {
        return $this->belongsTo(Categoria::class, 'categoria', 'id');
    }

    /**
     * Scope para buscar por circuito
     */
    public function scopeByCircuito($query, $circuito)
    {
        return $query->where('circuito', $circuito);
    }

    /**
     * Scope para buscar por ruta
     */
    public function scopeByRuta($query, $ruta)
    {
        return $query->where('ruta', $ruta);
    }

    /**
     * Scope para buscar por departamento
     */
    public function scopeByDepartamento($query, $idDepartamento)
    {
        return $query->where('id_departamento', $idDepartamento);
    }

    /**
     * Scope para buscar por municipio
     */
    public function scopeByMunicipio($query, $idMunicipio)
    {
        return $query->where('id_municipio', $idMunicipio);
    }

    /**
     * Scope para buscar por estado comercial
     */
    public function scopeByEstadoComercial($query, $estadoComercial)
    {
        return $query->where('estado_comercial', $estadoComercial);
    }

    /**
     * Scope para buscar por categoría
     */
    public function scopeByCategoria($query, $categoria)
    {
        return $query->where('categoria', $categoria);
    }

    /**
     * Scope para buscar por distribuidor
     */
    public function scopeByDistribuidor($query, $distribuidor)
    {
        return $query->where('distribuidor', 'like', '%' . $distribuidor . '%');
    }

    /**
     * Accessor para obtener el nombre completo del punto
     */
    public function getNombreCompletoAttribute()
    {
        return $this->nombre . ' - ' . $this->nombre_propietario;
    }

    /**
     * Accessor para verificar si tiene coordenadas
     */
    public function getTieneCoordendasAttribute()
    {
        return !empty($this->latitud) && !empty($this->longitud) && 
               $this->latitud != 0 && $this->longitud != 0;
    }

    /**
     * Accessor para obtener la dirección completa
     */
    public function getDireccionCompletaAttribute()
    {
        $direccion = $this->direccion;
        
        if ($this->municipio) {
            $direccion .= ', ' . $this->municipio->descripcion;
        }
        
        if ($this->departamento) {
            $direccion .= ', ' . $this->departamento->descripcion;
        }
        
        return $direccion;
    }

    /**
     * Accessor para obtener información de contacto
     */
    public function getContactoAttribute()
    {
        $contacto = [];
        
        if (!empty($this->telefono)) {
            $contacto['telefono'] = $this->telefono;
        }
        
        if (!empty($this->tel_op)) {
            $contacto['telefono_operador'] = $this->tel_op;
        }
        
        return $contacto;
    }

    /**
     * Verificar si el punto está activo comercialmente
     */
    public function estaActivo()
    {
        // Lógica para determinar si está activo basado en estado comercial
        return !empty($this->estado_comercial) && $this->estado_comercial != 'INACTIVO';
    }

    /**
     * Obtener información resumida del punto
     */
    public function getResumenAttribute()
    {
        return [
            'id' => $this->idpos,
            'nombre' => $this->nombre,
            'propietario' => $this->nombre_propietario,
            'direccion' => $this->direccion_completa,
            'circuito' => $this->circuito,
            'ruta' => $this->ruta,
            'categoria' => $this->categoria,
            'estado_comercial' => $this->estado_comercial,
            'tiene_coordenadas' => $this->tiene_coordenadas,
            'contacto' => $this->contacto
        ];
    }
}
