<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Answer extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'respuestas_posibles';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'id_pregunta',
        'respuesta',
        'orden',
        'activo'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_pregunta' => 'integer',
        'orden' => 'integer',
        'activo' => 'boolean'
    ];

    /**
     * Relación con la pregunta
     */
    public function pregunta()
    {
        return $this->belongsTo(Question::class, 'id_pregunta', 'id');
    }

    /**
     * Relación con preguntas hijas (para respuestas que generan preguntas condicionales)
     */
    public function preguntasHijas()
    {
        return $this->hasMany(Question::class, 'id_respuesta', 'id');
    }

    /**
     * Scope para respuestas activas
     */
    public function scopeActivas($query)
    {
        return $query->where('activo', true);
    }

    /**
     * Scope para respuestas por pregunta
     */
    public function scopeByPregunta($query, $idPregunta)
    {
        return $query->where('id_pregunta', $idPregunta);
    }

    /**
     * Scope ordenadas por orden
     */
    public function scopeOrdenadas($query)
    {
        return $query->orderBy('orden');
    }

    /**
     * Verificar si esta respuesta genera preguntas condicionales
     */
    public function tieneRespuestasCondicionales()
    {
        return $this->preguntasHijas()->count() > 0;
    }

    /**
     * Obtener preguntas condicionales asociadas
     */
    public function getPreguntasCondicionales()
    {
        return $this->preguntasHijas()->ordenadas()->get();
    }
}
