<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Survey extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'encuestas';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'titulo',
        'descripcion',
        'obligatorio',
        'navegar_atras',
        'aplicar',
        'estado_accion'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'obligatorio' => 'integer',
        'navegar_atras' => 'integer',
        'aplicar' => 'integer',
        'estado_accion' => 'integer'
    ];

    /**
     * Relación con preguntas
     */
    public function preguntas()
    {
        return $this->hasMany(Question::class, 'id_encuesta', 'id')->orderBy('orden');
    }

    /**
     * Relación con respuestas de encuesta
     */
    public function respuestas()
    {
        return $this->hasMany(SurveyResponse::class, 'id_encuesta', 'id');
    }

    /**
     * Relación con visitas que tienen esta encuesta
     */
    public function visitas()
    {
        return $this->belongsToMany(Visita::class, 'visita_encuestas', 'id_encuesta', 'id_visita');
    }

    /**
     * Scope para encuestas obligatorias
     */
    public function scopeObligatorias($query)
    {
        return $query->where('obligatorio', 1);
    }

    /**
     * Scope para encuestas opcionales
     */
    public function scopeOpcionales($query)
    {
        return $query->where('obligatorio', 0);
    }

    /**
     * Scope para encuestas activas
     */
    public function scopeActivas($query)
    {
        return $query->where('estado_accion', 1);
    }

    /**
     * Scope para encuestas que permiten navegar atrás
     */
    public function scopeConNavegacion($query)
    {
        return $query->where('navegar_atras', 1);
    }

    /**
     * Scope para encuestas aplicables
     */
    public function scopeAplicables($query)
    {
        return $query->where('aplicar', 1);
    }

    /**
     * Accessor para verificar si es obligatoria
     */
    public function getEsObligatoriaAttribute()
    {
        return $this->obligatorio === 1;
    }

    /**
     * Accessor para verificar si permite navegación hacia atrás
     */
    public function getPermiteNavegacionAttribute()
    {
        return $this->navegar_atras === 1;
    }

    /**
     * Accessor para verificar si está activa
     */
    public function getEstaActivaAttribute()
    {
        return $this->estado_accion === 1;
    }

    /**
     * Accessor para verificar si es aplicable
     */
    public function getEsAplicableAttribute()
    {
        return $this->aplicar === 1;
    }

    /**
     * Obtener el total de preguntas
     */
    public function getTotalPreguntasAttribute()
    {
        return $this->preguntas()->count();
    }

    /**
     * Obtener preguntas obligatorias
     */
    public function getPreguntasObligatoriasAttribute()
    {
        return $this->preguntas()->where('obligatorio', 1)->get();
    }

    /**
     * Obtener preguntas opcionales
     */
    public function getPreguntasOpcionalesAttribute()
    {
        return $this->preguntas()->where('obligatorio', 0)->get();
    }

    /**
     * Verificar si la encuesta está completa para una visita
     */
    public function estaCompletaParaVisita($idVisita)
    {
        $preguntasObligatorias = $this->preguntas()->where('obligatorio', 1)->count();
        $respuestasCompletas = $this->respuestas()
            ->where('id_visita', $idVisita)
            ->whereHas('pregunta', function($query) {
                $query->where('obligatorio', 1);
            })
            ->count();

        return $preguntasObligatorias === $respuestasCompletas;
    }

    /**
     * Obtener progreso de la encuesta para una visita
     */
    public function getProgresoParaVisita($idVisita)
    {
        $totalPreguntas = $this->total_preguntas;
        $preguntasRespondidas = $this->respuestas()
            ->where('id_visita', $idVisita)
            ->count();

        if ($totalPreguntas === 0) {
            return 100;
        }

        return round(($preguntasRespondidas / $totalPreguntas) * 100, 2);
    }

    /**
     * Clonar encuesta con todas sus preguntas
     */
    public function clonar($nuevoTitulo = null)
    {
        $nuevaEncuesta = $this->replicate();
        $nuevaEncuesta->titulo = $nuevoTitulo ?: $this->titulo . ' (Copia)';
        $nuevaEncuesta->save();

        // Clonar preguntas
        foreach ($this->preguntas as $pregunta) {
            $nuevaPregunta = $pregunta->replicate();
            $nuevaPregunta->id_encuesta = $nuevaEncuesta->id;
            $nuevaPregunta->save();

            // Clonar respuestas de la pregunta si las tiene
            foreach ($pregunta->respuestasPosibles as $respuesta) {
                $nuevaRespuesta = $respuesta->replicate();
                $nuevaRespuesta->id_pregunta = $nuevaPregunta->id;
                $nuevaRespuesta->save();
            }
        }

        return $nuevaEncuesta;
    }

    /**
     * Obtener estadísticas de la encuesta
     */
    public function getEstadisticas()
    {
        return [
            'total_preguntas' => $this->total_preguntas,
            'preguntas_obligatorias' => $this->preguntas()->where('obligatorio', 1)->count(),
            'preguntas_opcionales' => $this->preguntas()->where('obligatorio', 0)->count(),
            'total_respuestas' => $this->respuestas()->count(),
            'visitas_con_encuesta' => $this->visitas()->count(),
            'porcentaje_completitud' => $this->calcularPorcentajeCompletitud()
        ];
    }

    /**
     * Calcular porcentaje de completitud general
     */
    private function calcularPorcentajeCompletitud()
    {
        $visitasConEncuesta = $this->visitas()->count();
        if ($visitasConEncuesta === 0) {
            return 0;
        }

        $visitasCompletas = 0;
        foreach ($this->visitas as $visita) {
            if ($this->estaCompletaParaVisita($visita->id)) {
                $visitasCompletas++;
            }
        }

        return round(($visitasCompletas / $visitasConEncuesta) * 100, 2);
    }
}
