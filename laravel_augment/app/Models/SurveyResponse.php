<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SurveyResponse extends Model
{
    use HasFactory;

    /**
     * Nombre de la tabla en la base de datos
     */
    protected $table = 'respuestas_encuestas';

    /**
     * Clave primaria de la tabla
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es auto-incremental
     */
    public $incrementing = true;

    /**
     * Tipo de datos de la clave primaria
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe manejar timestamps
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa
     */
    protected $fillable = [
        'id_encuesta',
        'id_visita',
        'id_pregunta',
        'respuesta',
        'archivo_url',
        'fecha_respuesta'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos
     */
    protected $casts = [
        'id' => 'integer',
        'id_encuesta' => 'integer',
        'id_visita' => 'integer',
        'id_pregunta' => 'integer',
        'fecha_respuesta' => 'datetime'
    ];

    /**
     * Relación con la encuesta
     */
    public function encuesta()
    {
        return $this->belongsTo(Survey::class, 'id_encuesta', 'id');
    }

    /**
     * Relación con la visita
     */
    public function visita()
    {
        return $this->belongsTo(Visita::class, 'id_visita', 'id');
    }

    /**
     * Relación con la pregunta
     */
    public function pregunta()
    {
        return $this->belongsTo(Question::class, 'id_pregunta', 'id');
    }

    /**
     * Scope para respuestas por encuesta
     */
    public function scopeByEncuesta($query, $idEncuesta)
    {
        return $query->where('id_encuesta', $idEncuesta);
    }

    /**
     * Scope para respuestas por visita
     */
    public function scopeByVisita($query, $idVisita)
    {
        return $query->where('id_visita', $idVisita);
    }

    /**
     * Scope para respuestas por pregunta
     */
    public function scopeByPregunta($query, $idPregunta)
    {
        return $query->where('id_pregunta', $idPregunta);
    }

    /**
     * Scope para respuestas con archivo
     */
    public function scopeConArchivo($query)
    {
        return $query->whereNotNull('archivo_url');
    }

    /**
     * Accessor para verificar si tiene archivo
     */
    public function getTieneArchivoAttribute()
    {
        return !empty($this->archivo_url);
    }

    /**
     * Accessor para obtener URL completa del archivo
     */
    public function getArchivoUrlCompletaAttribute()
    {
        if (empty($this->archivo_url)) {
            return null;
        }

        return asset('storage/' . $this->archivo_url);
    }

    /**
     * Obtener información completa de la respuesta
     */
    public function getInfoCompletaAttribute()
    {
        return [
            'id' => $this->id,
            'encuesta' => $this->encuesta ? $this->encuesta->titulo : null,
            'pregunta' => $this->pregunta ? $this->pregunta->pregunta : null,
            'respuesta' => $this->respuesta,
            'tiene_archivo' => $this->tiene_archivo,
            'archivo_url' => $this->archivo_url_completa,
            'fecha_respuesta' => $this->fecha_respuesta ? $this->fecha_respuesta->format('Y-m-d H:i:s') : null
        ];
    }
}
