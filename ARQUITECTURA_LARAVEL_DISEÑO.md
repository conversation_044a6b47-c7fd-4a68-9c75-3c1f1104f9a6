# 🏗️ DISEÑO DE ARQUITECTURA LARAVEL

## 🎯 ARQUITECTURA PROPUESTA

### **📁 Estructura de Directorios Laravel**

```
laravel/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Api/
│   │   │   │   ├── AuthController.php
│   │   │   │   ├── UserController.php
│   │   │   │   ├── PointController.php
│   │   │   │   ├── SurveyController.php
│   │   │   │   ├── VisitController.php
│   │   │   │   ├── InventoryController.php
│   │   │   │   ├── CheckInController.php
│   │   │   │   ├── MasterDataController.php
│   │   │   │   └── FileController.php
│   │   │   └── Controller.php
│   │   ├── Middleware/
│   │   │   ├── JwtAuthMiddleware.php
│   │   │   ├── CountryConfigMiddleware.php
│   │   │   └── PermissionMiddleware.php
│   │   ├── Requests/
│   │   │   ├── LoginRequest.php
│   │   │   ├── CreatePointRequest.php
│   │   │   ├── SaveVisitRequest.php
│   │   │   └── SurveyResponseRequest.php
│   │   └── Resources/
│   │       ├── UserResource.php
│   │       ├── PointResource.php
│   │       └── SurveyResource.php
│   ├── Models/
│   │   ├── User.php
│   │   ├── Point.php
│   │   ├── InfoPos.php
│   │   ├── CarteraPunto.php
│   │   ├── Survey.php
│   │   ├── Question.php
│   │   ├── Answer.php
│   │   ├── Visit.php
│   │   ├── VisitDetail.php
│   │   ├── Inventory.php
│   │   ├── InventoryDetail.php
│   │   ├── Department.php
│   │   ├── Municipality.php
│   │   ├── DocumentType.php
│   │   ├── Category.php
│   │   ├── Circuit.php
│   │   ├── Route.php
│   │   ├── CommercialState.php
│   │   └── DcsPoint.php
│   ├── Services/
│   │   ├── AuthService.php
│   │   ├── UserService.php
│   │   ├── PointService.php
│   │   ├── SurveyService.php
│   │   ├── VisitService.php
│   │   ├── InventoryService.php
│   │   ├── CheckInService.php
│   │   ├── AwsService.php
│   │   ├── MailService.php
│   │   ├── PermissionService.php
│   │   ├── VersionService.php
│   │   └── JsonParserService.php
│   ├── Repositories/
│   │   ├── UserRepository.php
│   │   ├── PointRepository.php
│   │   ├── SurveyRepository.php
│   │   ├── VisitRepository.php
│   │   └── InventoryRepository.php
│   ├── Helpers/
│   │   ├── ResponseHelper.php
│   │   ├── ValidationHelper.php
│   │   └── StringHelper.php
│   └── Providers/
│       ├── CountryConfigServiceProvider.php
│       └── RepositoryServiceProvider.php
├── config/
│   ├── countries/
│   │   ├── uruguay.php
│   │   ├── chile.php
│   │   ├── colombia.php
│   │   ├── peru.php
│   │   └── claro.php
│   ├── database.php
│   ├── jwt.php
│   ├── aws.php
│   └── mail.php
├── database/
│   ├── migrations/
│   │   ├── 2024_01_01_000001_create_users_table.php
│   │   ├── 2024_01_01_000002_create_points_table.php
│   │   ├── 2024_01_01_000003_create_surveys_table.php
│   │   ├── 2024_01_01_000004_create_questions_table.php
│   │   ├── 2024_01_01_000005_create_answers_table.php
│   │   ├── 2024_01_01_000006_create_visits_table.php
│   │   ├── 2024_01_01_000007_create_visit_details_table.php
│   │   ├── 2024_01_01_000008_create_inventories_table.php
│   │   ├── 2024_01_01_000009_create_departments_table.php
│   │   ├── 2024_01_01_000010_create_municipalities_table.php
│   │   └── ... (más migraciones)
│   └── seeders/
│       ├── DepartmentSeeder.php
│       ├── MunicipalitySeeder.php
│       ├── DocumentTypeSeeder.php
│       ├── CategorySeeder.php
│       └── UserSeeder.php
├── routes/
│   ├── api.php
│   └── countries/
│       ├── uruguay.php
│       ├── chile.php
│       ├── colombia.php
│       └── peru.php
└── storage/
    └── app/
        └── countries/
            ├── uruguay/
            ├── chile/
            ├── colombia/
            └── peru/
```

---

## 🔧 CONFIGURACIÓN MULTI-AMBIENTE

### **🌍 Archivos .env por País**

#### **.env.uruguay**
```env
APP_NAME="Operador Movistar Uruguay"
APP_ENV=production
APP_KEY=base64:...

DB_CONNECTION=mysql
DB_HOST=movilbox-uruguay-aurora.c5ezqpc3jz9q.sa-east-1.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=movistar_uruguay
DB_USERNAME=uruguayseguro
DB_PASSWORD=hhjW26Jilv(

OPERATOR_URL=https://uruguay.movilbox.net/operador/
OPERATOR_BD=movistar_uruguay
OPERATOR_BD_POS=pos_distrimovistar_uruguay
OPERATOR_BD_DIS=distriu_

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=PAm/lbgX763wLKyGIP962k35QvN7uIuISWCfNWka
AWS_DEFAULT_REGION=sa-east-1
AWS_BUCKET=movilbox-uruguay

JWT_SECRET=...
JWT_TTL=60
```

#### **.env.chile**
```env
APP_NAME="Operador Movistar Chile"
DB_DATABASE=movistar_chile
AWS_BUCKET=movilbox-chile
OPERATOR_URL=https://chile.movilbox.net/operador/
```

### **📋 Config Files Personalizados**

#### **config/countries/uruguay.php**
```php
<?php
return [
    'name' => 'Uruguay',
    'operator' => 'Movistar',
    'database' => [
        'main' => 'movistar_uruguay',
        'pos' => 'pos_distrimovistar_uruguay',
        'distributor_prefix' => 'distriu_'
    ],
    'aws' => [
        'bucket' => 'movilbox-uruguay',
        'region' => 'sa-east-1'
    ],
    'urls' => [
        'operator' => 'https://uruguay.movilbox.net/operador/',
        'recovery' => 'https://uruguay.movilbox.net/operador/index.php?rpss='
    ],
    'settings' => [
        'sim_id_start' => 89,
        'coid' => 0,
        'item_asignacion' => 0
    ]
];
```

---

## 📦 DEPENDENCIAS Y PACKAGES

### **🔧 Composer Dependencies**

```json
{
    "require": {
        "php": "^8.1",
        "laravel/framework": "^10.0",
        "tymon/jwt-auth": "^2.0",
        "league/flysystem-aws-s3-v3": "^3.0",
        "aws/aws-sdk-php": "^3.0",
        "intervention/image": "^2.7",
        "maatwebsite/excel": "^3.1",
        "spatie/laravel-permission": "^5.0",
        "spatie/laravel-query-builder": "^5.0",
        "laravel/sanctum": "^3.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.0",
        "mockery/mockery": "^1.4.4",
        "fakerphp/faker": "^1.9.1"
    }
}
```

### **📋 Packages Específicos**

1. **tymon/jwt-auth** - Autenticación JWT
2. **league/flysystem-aws-s3-v3** - Integración AWS S3
3. **spatie/laravel-permission** - Sistema de permisos
4. **spatie/laravel-query-builder** - Query builder avanzado
5. **intervention/image** - Procesamiento de imágenes
6. **maatwebsite/excel** - Exportación de datos

---

## 🛣️ RUTAS Y MIDDLEWARE

### **🔗 routes/api.php**
```php
<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\PointController;
use App\Http\Controllers\Api\SurveyController;
use App\Http\Controllers\Api\VisitController;
use App\Http\Controllers\Api\InventoryController;
use App\Http\Controllers\Api\CheckInController;
use App\Http\Controllers\Api\MasterDataController;
use App\Http\Controllers\Api\FileController;

Route::prefix('api')->group(function () {
    // Rutas públicas
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/recuperarPass', [AuthController::class, 'recoverPassword']);

    // Rutas protegidas
    Route::middleware(['jwt.auth', 'country.config'])->group(function () {
        
        // Usuarios
        Route::get('/usuarios', [UserController::class, 'index']);
        Route::post('/cambiarPass', [AuthController::class, 'changePassword']);
        Route::post('/permisosDcs', [UserController::class, 'getDcsPermissions']);
        
        // Puntos de Venta
        Route::post('/buscarPuntos', [PointController::class, 'search']);
        Route::post('/puntosVisitados', [PointController::class, 'visitedPoints']);
        Route::post('/buscarCarteraPuntos', [PointController::class, 'searchPortfolio']);
        Route::post('/crearActualizarPuntos', [PointController::class, 'createOrUpdate']);
        Route::get('/camposDinamicos', [PointController::class, 'getDynamicFields']);
        Route::post('/camposDinamicosPunto', [PointController::class, 'getPointDynamicFields']);
        
        // Check-in y Tabs
        Route::post('/tabsCheckIn', [CheckInController::class, 'getTabInfo']);
        
        // Encuestas
        Route::post('/encuestas', [SurveyController::class, 'getSurveys']);
        Route::post('/encuestasOperador', [SurveyController::class, 'getOperatorSurveys']);
        Route::post('/guardarEncuesta', [SurveyController::class, 'saveSurvey']);
        
        // Visitas
        Route::post('/guardarVisita', [VisitController::class, 'saveVisit']);
        Route::post('/detalleVisita', [VisitController::class, 'getVisitDetail']);
        
        // Inventarios
        Route::post('/detalleInventario', [InventoryController::class, 'getInventoryDetail']);
        Route::post('/infoProducto', [InventoryController::class, 'getProductInfo']);
        
        // Datos Maestros
        Route::get('/departamentos', [MasterDataController::class, 'getDepartments']);
        Route::get('/municipios', [MasterDataController::class, 'getMunicipalities']);
        Route::get('/tiposDocumento', [MasterDataController::class, 'getDocumentTypes']);
        Route::get('/categorias', [MasterDataController::class, 'getCategories']);
        Route::get('/clasificacionCategorias', [MasterDataController::class, 'getCategoryClassifications']);
        
        // Archivos
        Route::post('/guardarImagenesFrm', [FileController::class, 'uploadImages']);
    });
});
```

### **🛡️ Middleware Personalizado**

#### **JwtAuthMiddleware.php**
```php
<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\AuthService;

class JwtAuthMiddleware
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();
        
        if (!$token) {
            return response()->json([
                'status' => 403,
                'error' => 'Forbidden',
                'message' => 'Access Denied'
            ], 403);
        }

        $user = $this->authService->validateToken($token);
        
        if (!$user) {
            return response()->json([
                'status' => 403,
                'error' => 'Forbidden', 
                'message' => 'Access Denied'
            ], 403);
        }

        $request->merge(['authenticated_user' => $user]);
        
        return $next($request);
    }
}
```

#### **CountryConfigMiddleware.php**
```php
<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CountryConfigMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Detectar país basado en dominio, header o configuración
        $country = $this->detectCountry($request);
        
        // Cargar configuración específica del país
        config(['app.current_country' => $country]);
        
        return $next($request);
    }

    private function detectCountry(Request $request): string
    {
        // Lógica para detectar país
        $host = $request->getHost();
        
        if (str_contains($host, 'uruguay')) return 'uruguay';
        if (str_contains($host, 'chile')) return 'chile';
        if (str_contains($host, 'colombia')) return 'colombia';
        if (str_contains($host, 'peru')) return 'peru';
        if (str_contains($host, 'claro')) return 'claro';
        
        return 'uruguay'; // default
    }
}
```

---

## 🗄️ MODELOS ELOQUENT CON RELACIONES

### **👤 User.php**
```php
<?php
namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    protected $table = 'usuarios';
    
    protected $fillable = [
        'cedula', 'nombre', 'apellido', 'username', 'password',
        'perfil', 'id_perfil', 'primer_logueo', 'fecha_hora',
        'tolerancia_visita'
    ];

    protected $hidden = ['password'];

    // JWT Methods
    public function getJWTIdentifier() { return $this->getKey(); }
    public function getJWTCustomClaims() { return []; }

    // Relationships
    public function visits()
    {
        return $this->hasMany(Visit::class, 'id_usuario');
    }

    public function permissions()
    {
        return $this->hasMany(Permission::class, 'id_usuario');
    }
}
```

### **🏪 Point.php**
```php
<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Point extends Model
{
    protected $table = 'puntos';
    protected $primaryKey = 'idpos';
    
    protected $fillable = [
        'nombre', 'fecha', 'hora', 'efectiva', 'direccion',
        'encuestas_respondidas', 'cantidad_visitas', 'precision_gps',
        'latitud', 'longitud', 'id_agente'
    ];

    // Relationships
    public function visits()
    {
        return $this->hasMany(Visit::class, 'id_pos');
    }

    public function inventories()
    {
        return $this->hasMany(Inventory::class, 'id_pos');
    }

    public function infoPos()
    {
        return $this->hasOne(InfoPos::class, 'idpos', 'idpos');
    }

    public function dcsInfo()
    {
        return $this->hasOne(DcsPoint::class, 'id', 'idpos');
    }
}
```

### **📝 Survey.php**
```php
<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Survey extends Model
{
    protected $table = 'encuestas';
    
    protected $fillable = [
        'titulo', 'descripcion', 'obligatorio', 
        'navegar_atras', 'aplicar', 'estado_accion'
    ];

    // Relationships
    public function questions()
    {
        return $this->hasMany(Question::class, 'id_encuesta');
    }

    public function responses()
    {
        return $this->hasMany(SurveyResponse::class, 'id_encuesta');
    }
}
```

---

## 🔧 SERVICE PROVIDERS

### **CountryConfigServiceProvider.php**
```php
<?php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class CountryConfigServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('country.config', function ($app) {
            $country = config('app.current_country', 'uruguay');
            return config("countries.{$country}");
        });
    }

    public function boot()
    {
        // Configurar conexiones de BD dinámicamente
        $this->configureDatabaseConnections();
    }

    private function configureDatabaseConnections()
    {
        $country = config('app.current_country', 'uruguay');
        $countryConfig = config("countries.{$country}");
        
        if ($countryConfig) {
            config([
                'database.connections.dynamic' => [
                    'driver' => 'mysql',
                    'host' => env('DB_HOST'),
                    'database' => $countryConfig['database']['main'],
                    'username' => env('DB_USERNAME'),
                    'password' => env('DB_PASSWORD'),
                ]
            ]);
        }
    }
}
```

---

## 📋 PLAN DETALLADO DE IMPLEMENTACIÓN

### **🎯 Fase 1: Setup Inicial (2-3 días)**
1. ✅ Crear proyecto Laravel 10
2. ✅ Instalar dependencias (JWT, AWS, etc.)
3. ✅ Configurar estructura de directorios
4. ✅ Setup de archivos .env por país
5. ✅ Configurar Service Providers

### **🎯 Fase 2: Autenticación y Middleware (2 días)**
1. ✅ Implementar JWT authentication
2. ✅ Crear middleware de autenticación
3. ✅ Crear middleware de configuración por país
4. ✅ Setup de rutas básicas

### **🎯 Fase 3: Modelos y Migraciones (3-4 días)**
1. ✅ Crear todas las migraciones
2. ✅ Implementar modelos Eloquent
3. ✅ Definir relaciones entre modelos
4. ✅ Crear seeders para datos maestros

### **🎯 Fase 4: Servicios Core (3-4 días)**
1. ✅ AuthService y UserService
2. ✅ PointService y SurveyService
3. ✅ VisitService y InventoryService
4. ✅ AwsService y MailService

### **🎯 Fase 5: Controladores API (4-5 días)**
1. ✅ AuthController y UserController
2. ✅ PointController y SurveyController
3. ✅ VisitController y InventoryController
4. ✅ CheckInController y FileController

### **🎯 Fase 6: Testing y Validación (2-3 días)**
1. ✅ Tests unitarios de servicios
2. ✅ Tests de integración de API
3. ✅ Validación de respuestas JSON
4. ✅ Tests de autenticación

### **🎯 Fase 7: Deployment y Documentación (2 días)**
1. ✅ Configuración de deployment
2. ✅ Documentación de API
3. ✅ Guías de instalación
4. ✅ Scripts de migración de datos

---

## 🚀 PRÓXIMO PASO

**¿Estás listo para comenzar con la Fase 2: Configuración Inicial?**

El siguiente prompt será crear el proyecto Laravel base con todas las dependencias y configuraciones iniciales.
