<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\UsuarioService;

class AuthToken
{
    public function handle(Request $request, Closure $next)
    {
        $header = $request->header('Authorization');
        if (!$header) {
            return response()->json(['estado' => 0, 'msg' => 'Token requerido', 'datos' => null], 403);
        }
        $token = str_replace('Bearer ', '', $header);
        $service = new UsuarioService();
        $usuario = $service->validarToken($token);
        if (!$usuario) {
            return response()->json(['estado' => 0, 'msg' => 'Token inválido', 'datos' => null], 403);
        }
        $request->merge(['authUsuario' => $usuario]);
        return $next($request);
    }
}
