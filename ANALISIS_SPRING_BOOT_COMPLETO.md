# 📋 ANÁLISIS COMPLETO DEL PROYECTO SPRING BOOT

## 🎯 RESUMEN EJECUTIVO

**Proyecto:** API REST para Operadores de Telecomunicaciones (Movistar/Claro)  
**Tecnología:** Spring Boot 2.1.7 + MySQL + AWS S3  
**Propósito:** Gestión de puntos de venta (PDVs), inventarios, visitas y encuestas  
**Países:** Uruguay, Chile, Colombia, Perú  

---

## 📡 INVENTARIO COMPLETO DE ENDPOINTS

### **🔐 AUTENTICACIÓN Y USUARIOS**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/login` | `user`, `pass`, `fechaHora`, `versionName`, `versionCode`, `tokenNotificacion` | Usuario + token JWT + menús + motivos + estados comerciales | Login con validación de versión y generación de token |
| `POST` | `/api/recuperarPass` | `email` | Estado de envío | Recuperación de contraseña por email |
| `POST` | `/api/cambiarPass` | `passAnterior`, `passNuevo` | Estado de cambio | Cambio de contraseña autenticado |
| `GET` | `/api/usuarios` | - | Lista de usuarios | Obtener todos los usuarios |
| `POST` | `/api/permisosDcs` | `fechaHora` | Permisos DCS del usuario | Obtener permisos específicos |

### **🏪 GESTIÓN DE PUNTOS DE VENTA (PDVs)**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/buscarPuntos` | `nombre`, `idpos`, `circuito`, `ruta`, `estadocom`, `pagina`, `agente`, `documento` | Lista paginada de puntos | Búsqueda avanzada con filtros |
| `POST` | `/api/puntosVisitados` | - | Puntos visitados por usuario | Historial de puntos visitados |
| `POST` | `/api/buscarCarteraPuntos` | `nombrePdv`, `id`, `documento`, `codigoUnicoTienda`, `idDepartamento`, `idMunicipio`, `idRuta`, `idCircuito`, `idEstadoComercial`, `idAgente` | Lista de cartera de puntos | Búsqueda en cartera de puntos |
| `POST` | `/api/crearActualizarPuntos` | `CrearActualizarPuntosPost` (objeto complejo) | Estado de operación | Crear o actualizar punto de venta |
| `POST` | `/api/camposDinamicosPunto` | `idPos` | Campos dinámicos del punto | Obtener campos personalizables |
| `GET` | `/api/camposDinamicos` | - | Lista de campos dinámicos | Obtener todos los campos dinámicos |

### **📋 CHECK-IN Y TABS DE INFORMACIÓN**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/tabsCheckIn` | `idPos`, `op` (1-5) | Información según opción | **Op 1:** Info punto + frecuencia<br>**Op 2:** Inventario<br>**Op 3:** Historial visitas<br>**Op 4:** Rotación<br>**Op 5:** Comentarios |

### **📝 ENCUESTAS Y FORMULARIOS**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/encuestas` | `idEncuestas` | Encuestas disponibles | Obtener encuestas para PDV |
| `POST` | `/api/encuestasOperador` | `idEncuestas` | Encuestas de operador | Encuestas específicas de operador |
| `POST` | `/api/guardarEncuesta` | Array de `EncuestaRespondida` | Estado de guardado | Guardar respuestas de encuesta |

### **🚶 GESTIÓN DE VISITAS**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/guardarVisita` | `visitas`, `encuestas`, `formulariosDinamicos`, `noEfectiva` | Estado de guardado | Guardar visita completa con encuestas |
| `POST` | `/api/detalleVisita` | `idVisita` | Detalle completo de visita | Obtener información detallada de visita |

### **📦 INVENTARIOS**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/detalleInventario` | `idPos`, `idReferencia`, `pagina` | Lista paginada de inventario | Detalle de inventario por PDV |
| `POST` | `/api/infoProducto` | `serial` | Información del producto | Buscar producto por serial |

### **🗂️ DATOS MAESTROS**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `GET` | `/api/departamentos` | - | Lista de departamentos | Obtener departamentos |
| `GET` | `/api/municipios` | - | Lista de municipios | Obtener municipios |
| `GET` | `/api/tiposDocumento` | - | Lista de tipos de documento | Obtener tipos de documento |
| `GET` | `/api/categorias` | - | Lista de categorías de puntos | Obtener categorías |
| `GET` | `/api/clasificacionCategorias` | - | Lista de escalas PDV | Obtener clasificaciones |

### **📁 ARCHIVOS**

| Método | Endpoint | Parámetros | Respuesta | Funcionalidad |
|--------|----------|------------|-----------|---------------|
| `POST` | `/api/guardarImagenesFrm` | `file[]` (MultipartFile), `data` (JSON) | Estado de subida | Subir imágenes a AWS S3 |

---

## 🗄️ MAPEO DE ENTIDADES JPA → ELOQUENT

### **👤 Usuario**
```java
@Entity
public class Usuario {
    @Id private Long id;
    @Column(name = "cedula") private String cedula;
    @Column(name = "nombre") private String nombre;
    @Column(name = "apellido") private String apellido;
    @Column(name = "user") private String username;
    @Column(name = "perfil") private String perfil;
    @Column(name = "id_perfil") private Integer idPerfil;
    @Column(name = "estado_pass") private Integer primerLogueo;
    @Column(name = "fecha_hora") private String fechaHora;
    @Column(name="tolerancia_visita") private int toleranciaVisita;
}
```

### **🏪 Puntos**
```java
@Entity
public class Puntos {
    @Id @Column(name = "idpos") private int idPos;
    @Column(name = "nombre") private String nombre;
    @Column(name = "fecha") private String fecha;
    @Column(name = "hora") private String hora;
    @Column(name = "efectiva") private int efectiva;
    @Column(name = "direccion") private String direccion;
    @Column(name = "encuestas_respondidas") private String encuestasRespondidas;
    @Column(name = "cantidad_visitas") private int cantidadVisitas;
    @Column(name = "precisiongps") private double precisionGps;
    @Column(name = "latitud") private double latitud;
    @Column(name = "longitud") private double longitud;
    @Column(name = "id_distri") private int idAgente;
}
```

### **ℹ️ InfoPos**
```java
@Entity
public class InfoPos {
    @Id @Column(name = "idpos") private int idpos;
    @Column(name = "nombre") private String nombre;
    @Column(name = "nombre_propietario") private String nombrePropietario;
    @Column(name = "circuito") private String circuito;
    @Column(name = "ruta") private String ruta;
    @Column(name = "direccion") private String direccion;
    @Column(name = "telefono") private String telefono;
    @Column(name = "tel_op") private String telefonoOp;
    @Column(name = "latitud") private double latitud;
    @Column(name = "longitud") private double longitud;
    @Column(name = "distribuidor") private String distribuidor;
    @Column(name = "fecha_visita") private String fechaVisita;
    @Column(name = "categoria") private String categoria;
}
```

### **🛒 CarteraPuntos**
```java
@Entity
public class CarteraPuntos {
    @Id @Column(name = "idpos") private int idPos;
    @Column(name = "razon") private String nombrePdv;
    @Column(name = "nombre_cli") private String nombreCliente;
    @Column(name = "tipo_doc") private String tipoDocumento;
    @Column(name = "cedula") private String numeroDocumento;
    @Column(name = "email") private String correo;
    @Column(name = "tel") private String telefonoFijo;
    @Column(name = "celular") private String celular;
    @Column(name = "tel_op") private String telefonoOp;
    @Column(name = "categoria") private int idCategoria;
    @Column(name = "id_escala") private int idCategoriaClasificacion;
    @Column(name = "depto") private int idDepartamento;
    @Column(name = "ciudad") private int idMunicipio;
}
```

### **❓ Preguntas**
```java
@Entity
public class Preguntas {
    @Id @Column(name = "id") private int id;
    @Column(name = "pregunta") private String descripcion;
    @Column(name = "id_encuesta") private int idEncuesta;
    @Column(name = "tipo") private int tipo;
    @Column(name = "id_respuesta") private int idRespuestaPadre;
    @Column(name = "obligatorio") private int obligatorio;
    @Column(name = "orden") private int orden;
}
```

### **📝 Encuesta**
```java
@Entity
public class Encuesta {
    @Id @Column(name = "id") private Long id;
    @Column(name = "titulo") private String titulo;
    @Column(name = "descripcion") private String descripcion;
    @Column(name = "obligatorio") private int obligatorio;
    @Column(name = "navegar_atras") private int navegaAtras;
    private int aplicar;
    @Column(name = "estado_accion") private int estadoAccion;
}
```

### **🏛️ Departamentos**
```java
@Entity
public class Departamentos {
    @Id @Column(name = "id") private int id;
    @Column(name = "descripcion") private String descripcion;
}
```

### **🏘️ Municipios**
```java
@Entity
public class Municipios {
    @Id @Column(name = "id") private int id;
    @Column(name = "descripcion") private String descripcion;
    @Column(name = "id_depto") private int idDepartamento;
}
```

---

## 🔧 SERVICIOS Y LÓGICA DE NEGOCIO

### **👤 IUsuarioService**
```java
public interface IUsuarioService {
    Map<String,Object> getUsuario(String username, String pass);
    List<?> getAllUsuarios();
    String generarToken(Usuario us, String tokenNotificacion, String versionName, int versionCode, Map<String, Object> datos);
    Usuario validarToken(String token);
    int validarEmail(String email);
    Boolean guardarRecoPass(int idus, String recover);
    int cambarPass(String passold, String passnew, int idus);
    Map<String,Object> getPermisos(int idUs, String fechahora);
    Map<String, Object> getPuntos(int idUs);
    List<EstadosCom> getEstadosCom(String fecha);
    List<Motivos> listarMotivos();
}
```

### **🏪 IPuntosService**
```java
public interface IPuntosService {
    Map<String, Object> buscarPuntos(int idUs, String nombre, int idpos, int circuito, int ruta, int estadocom, int pagina, int idAgente, String documento);
    DcsPuntos buscarDcsPuntos(Long idPos);
    List<EscalasPdv> clasificacionCategorias();
    List<Departamentos> getDepartamentos();
    List<Municipios> getMunicipios();
    List<TiposDocumento> getTiposDocumeno();
    List<CategoriasPuntos> getCategoriasPuntos();
    List<CarteraPuntos> getCarteraPuntos(String nombre, int idpos, String documento, String codigoUnicoTienda, int idDepartamento, int idMunicipio, int idRuta, int idCircuito, int idEstadoComercial, int idAgente);
    Map<String, Object> crearActualizarPuntos(CrearActualizarPuntosPost datos, int idUs);
    Map<String, Object> camposDinamicosPunto(int idPos);
    Map<String, Object> camposDinamicos();
}
```

### **📝 IEncuestaService**
```java
public interface IEncuestaService {
    Map<String, Object> verEncuestas(Usuario usus, String idEncuestas);
    Map<String, Object> verEncuestasOperador(Usuario usus, String idEncuestasOperador);
    Map<String, Object> responderEncuesta(List<EncuestaRespondida> encuestasRespondidas, Map<String,Object> datosVisita, long idUsuario, int idPerfil, long idVisita);
    Map<String, Object> responderFormularioDinamico(List<FormularioDinamico> formularioDinamico, Map<String, Object> datosVisita, long idUsuario, int idPerfil, long idVisita);
    boolean guardarNombreImg(long idApp, String nombreImagen);
}
```

### **🚶 IVisitaService**
```java
public interface IVisitaService {
    Map<String, Object> guardarVisita(Map<String, Object> datosVisita);
    Map<String, Object> guardarNoefectiva(Map<String, Object> noEfectiva, long idUsuario, long idPos, long idVisita);
    void reversarVisita(long idVisita);
    Map<String, Object> detalleVisita(int idVisita);
}
```

### **📦 IInventarioService**
```java
public interface IInventarioService {
    List<DetalleInventario> getDetalleInventario(int idPos, int idReferencia, int pagina);
    Map<String,Object> getDetalleInfoProducto(String serial);
}
```

### **📋 ITabsCheckIn**
```java
public interface ITabsCheckIn {
    InfoPos infoPuntos(int idpos);
    Map<String,Object> infoFrecuencia(int idpos);
    List<InfoHistorial> infoHistorial(int idPos);
    List<InfoInventario> infoInventario(int idPos);
    List<InfoRotacion> infoRotacion(int idPos);
}
```

---

## ⚙️ CONFIGURACIONES MULTI-PAÍS

### **🗂️ Archivos de Configuración**
- `application.properties` (Uruguay - Principal)
- `application-localClaroDev.properties`
- `application-localClaroProd.properties`
- `application-localMovChileDev.properties`
- `application-localMovChileProd.properties`
- `application-localMovColDev.properties`
- `application-localMovColProd.properties`
- `application-localMovPeruDev.properties`
- `application-localMovUruDev.properties`
- `application-localMovUruProd.properties`

### **🔧 Configuración Principal (Uruguay)**
```properties
spring.application.name=servicio-operador-movistar-uruguay-v3_3_4
spring.datasource.url=***********************************************************************************************************
spring.datasource.username=uruguayseguro
spring.datasource.password=hhjW26Jilv(

operador.ur=https://uruguay.movilbox.net/operador/
operador.bd=movistar_uruguay
operador.bd_pos=pos_distrimovistar_uruguay
operador.bd_dis=distriu_

aws.accessKeyId=********************
aws.secretKey=PAm/lbgX763wLKyGIP962k35QvN7uIuISWCfNWka
aws.region=sa-east-1
aws.bucketName=movilbox-uruguay
```

### **📦 Archivos POM Multi-País**
- `pom.xml` (Uruguay)
- `pom.claro.xml`
- `pom.movistar-chi.xml`
- `pom.movistar-col.xml`
- `pom.movistar-uru.xml`

---

## 🔐 SEGURIDAD Y AUTENTICACIÓN

### **JWT Configuration**
- **Filter:** `JWTAuthorizationFilter`
- **Endpoints Públicos:** `/api/login`, `/api/recuperarPass`
- **Header:** `Authorization: Bearer <token>`
- **Validación:** Método `validarToken()` en cada endpoint protegido

### **Estructura de Respuesta Estándar**
```json
{
    "estado": 1,        // 1 = éxito, 0 = error
    "msg": "mensaje",   // Mensaje descriptivo
    "datos": {}         // Datos de respuesta
}
```

---

## 📊 FUNCIONALIDADES CLAVE

1. **🔐 Autenticación JWT** con validación de versión de app
2. **🏪 Gestión completa de PDVs** con búsquedas avanzadas
3. **📝 Sistema de encuestas dinámicas** con formularios personalizables
4. **🚶 Registro de visitas** con geolocalización y validaciones
5. **📦 Control de inventarios** con rotación y detalles
6. **📁 Subida de archivos** a AWS S3
7. **🌍 Configuración multi-país** con bases de datos separadas
8. **📊 Reportes y estadísticas** de visitas y ventas
9. **🔒 Sistema de permisos** DCS por usuario
10. **📱 Soporte para aplicaciones móviles** con tokens de notificación

---

## 🎯 PRÓXIMOS PASOS PARA MIGRACIÓN

1. **✅ Análisis completado**
2. **🔄 Diseño de arquitectura Laravel** (Siguiente fase)
3. **🏗️ Setup inicial del proyecto Laravel**
4. **🗄️ Migración de modelos y base de datos**
5. **🎮 Migración de controladores y endpoints**
6. **⚙️ Migración de servicios y lógica de negocio**
7. **🌍 Configuración multi-país**
8. **🧪 Testing y validación**

