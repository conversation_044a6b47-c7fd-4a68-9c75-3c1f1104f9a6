<?php
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UsuarioController;

Route::prefix('api')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);

    Route::middleware('auth.token')->group(function () {
        Route::get('/usuarios', [UsuarioController::class, 'index']);
        // Resto de rutas migradas deberian declararse aqui
    });
});
