#!/bin/bash

# setup_auto.sh - Script automático para instalar PHP (desde repos oficiales de Ubuntu 24.04)
# y la última versión de Laravel.
# NO requiere interacción del usuario y NO usa repositorios de terceros.

echo "-----------------------------------------------------------------"
echo "Iniciando la instalación automática de PHP y Laravel en Ubuntu 24.04..."
echo "Se utilizarán los repositorios oficiales de Ubuntu."
echo "-----------------------------------------------------------------"
echo ""

# --- Sección 1: Configurar DEBIAN_FRONTEND para no interactividad ---
export DEBIAN_FRONTEND=noninteractive
echo "[INFO] DEBIAN_FRONTEND configurado como noninteractive."
echo ""

# --- Sección 2: Actualizar el sistema ---
echo "[INFO] Actualizando lista de paquetes del sistema..."
sudo apt-get update -y
echo ""

# --- Sección 3: Instalar dependencias comunes ---
echo "[INFO] Instalando dependencias comunes (curl, unzip, git)..."
sudo apt-get install -y curl unzip git
echo ""

# --- Sección 4: Instalar PHP desde los repositorios oficiales de Ubuntu 24.04 ---
# Ubuntu 24.04 LTS debería incluir PHP 8.3 en sus repositorios principales.
PHP_PACKAGE_VERSION="php8.3" # Versión esperada en Ubuntu 24.04

echo "[INFO] Instalando PHP $PHP_PACKAGE_VERSION y extensiones comunes desde repositorios oficiales..."
# Lista de extensiones comunes para Laravel
sudo apt-get install -y \
    $PHP_PACKAGE_VERSION \
    $PHP_PACKAGE_VERSION-cli \
    $PHP_PACKAGE_VERSION-fpm \
    $PHP_PACKAGE_VERSION-common \
    $PHP_PACKAGE_VERSION-mysql \
    $PHP_PACKAGE_VERSION-zip \
    $PHP_PACKAGE_VERSION-gd \
    $PHP_PACKAGE_VERSION-mbstring \
    $PHP_PACKAGE_VERSION-curl \
    $PHP_PACKAGE_VERSION-xml \
    $PHP_PACKAGE_VERSION-bcmath \
    $PHP_PACKAGE_VERSION-intl \
    $PHP_PACKAGE_VERSION-tokenizer \
    $PHP_PACKAGE_VERSION-sqlite3 \
    $PHP_PACKAGE_VERSION-dom \
    $PHP_PACKAGE_VERSION-opcache
echo ""

echo "[INFO] Verificando la versión de PHP instalada..."
php -v
echo ""

# --- Sección 5: Instalar Composer (Gestor de dependencias para PHP) ---
echo "[INFO] Instalando Composer globalmente..."
# Descargar el instalador de Composer
curl -sS https://getcomposer.org/installer -o composer-setup.php
if [ $? -ne 0 ]; then
    echo "[ERROR] No se pudo descargar el instalador de Composer. Abortando."
    exit 1
fi

# Ejecutar el instalador de Composer y moverlo a /usr/local/bin
# No se realiza la verificación del hash aquí para mantenerlo simple y sin dependencias externas
# para la obtención del hash, pero en un entorno de producción es recomendable.
sudo php composer-setup.php --install-dir=/usr/local/bin --filename=composer
if [ $? -ne 0 ]; then
    echo "[ERROR] Falló la instalación de Composer. Abortando."
    rm composer-setup.php
    exit 1
fi

# Eliminar el script de instalación de Composer
rm composer-setup.php
echo ""

echo "[INFO] Verificando la instalación de Composer..."
composer --version
if [ $? -ne 0 ]; then
    echo "[ERROR] Composer no se instaló correctamente. Abortando."
    exit 1
fi
echo ""

# --- Sección 6: Crear un proyecto Laravel de ejemplo ---
# Laravel se instala creando un nuevo proyecto. Composer se encargará de obtener la última versión estable.
# El proyecto se creará en el directorio /opt por defecto para evitar problemas de permisos
# si el script se ejecuta como root y luego un usuario normal intenta acceder.
# Puedes cambiar esta ubicación.

# PROJECT_PARENT_DIR="/"
# PROJECT_NAME="laravel"
# PROJECT_PATH="$PROJECT_PARENT_DIR/$PROJECT_NAME"

# echo "[INFO] Creando un proyecto Laravel de ejemplo llamado '$PROJECT_NAME' en '$PROJECT_PARENT_DIR'..."
# echo "[INFO] Esto puede tardar unos minutos dependiendo de tu conexión a internet."

# Crear el directorio padre si no existe y asegurarse de que el usuario que ejecuta el script pueda escribir en él
# (si el script no se ejecuta como root, esto podría fallar sin sudo)
# Si el script se ejecuta como root, root será el propietario.
# if [ ! -d "$PROJECT_PARENT_DIR" ]; then
#     sudo mkdir -p "$PROJECT_PARENT_DIR"
#     sudo chmod 777 "$PROJECT_PARENT_DIR" # Permisos amplios para el ejemplo, ajusta según necesidad
# fi

# Navegar al directorio padre para crear el proyecto
# cd "$PROJECT_PARENT_DIR" || { echo "[ERROR] No se pudo cambiar al directorio $PROJECT_PARENT_DIR. Abortando."; exit 1; }

# Crear el proyecto Laravel. Composer se ejecuta como el usuario actual.
# Si el script se ejecuta con sudo, el proyecto será propiedad de root.
# Para producción, considera ejecutar composer como un usuario no privilegiado.
# composer create-project laravel/laravel "$PROJECT_NAME"
# if [ $? -ne 0 ]; then
#     echo "[ERROR] No se pudo crear el proyecto Laravel '$PROJECT_NAME'. Verifica los mensajes de Composer."
#     exit 1
# fi
# echo ""

# echo "[INFO] Proyecto Laravel '$PROJECT_NAME' creado en '$PROJECT_PATH'."
# echo ""

# # --- Sección 7: Configurar permisos para el proyecto Laravel ---
# echo "[INFO] Configurando permisos básicos para el proyecto Laravel en '$PROJECT_PATH'..."
# # Asumiendo que el servidor web podría ejecutarse como www-data o el usuario actual.
# # Si se ejecuta como root, el propietario de los archivos del proyecto será root.
# # Ajusta 'www-data' si tu servidor web usa un usuario diferente (ej: nginx, apache).
# WEB_USER="www-data" # Usuario común para servidores web en Ubuntu

# # Otorgar propiedad al usuario web para los directorios que Laravel necesita escribir.
# # Esto es más seguro si el script se ejecuta como root y el servidor web es www-data.
# # Si el script lo corre un usuario normal y ese mismo usuario corre el servidor de desarrollo,
# # puede que no sea estrictamente necesario cambiar el propietario a www-data.

# sudo chown -R $USER:$USER "$PROJECT_PATH" # Que el usuario que ejecutó el script sea dueño
# sudo chown -R $WEB_USER:$WEB_USER "$PROJECT_PATH/storage"
# sudo chown -R $WEB_USER:$WEB_USER "$PROJECT_PATH/bootstrap/cache"

# sudo chmod -R 775 "$PROJECT_PATH/storage"
# sudo chmod -R 775 "$PROJECT_PATH/bootstrap/cache"
# echo ""

# echo "-----------------------------------------------------------------"
# echo "¡Instalación automática de PHP y Laravel completada!"
# echo "PHP $(php -r 'echo PHP_VERSION;') y Composer $(composer --version --no-ansi | cut -d' ' -f3) están instalados."
# echo "Se ha creado un proyecto Laravel de ejemplo en: $PROJECT_PATH"
# echo "Para probar el servidor de desarrollo de Laravel, puedes ejecutar:"
# echo "cd $PROJECT_PATH"
# echo "php artisan serve"
# echo "-----------------------------------------------------------------"

exit 0