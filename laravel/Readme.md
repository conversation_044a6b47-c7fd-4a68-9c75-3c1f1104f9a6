# Laravel Backend Migration

Este directorio contiene un esqueleto de proyecto Laravel que replica los endpoints existentes en el proyecto Spring Boot.

## Instalación

1. Instalar dependencias con Composer:
   ```bash
   composer install
   ```

2. <PERSON><PERSON><PERSON> `.env.example` a `.env` y configurar las credenciales de base de datos y el JWT_SECRET.

3. Generar la clave de aplicación y la clave JWT (si se usa):
   ```bash
   php artisan key:generate
   php artisan jwt:secret
   ```

4. Ejecutar migraciones y pruebas opcionalmente.

Este proyecto incluye un middleware `AuthToken` para validar el token enviado en el encabezado `Authorization` y varios controladores de ejemplo.
