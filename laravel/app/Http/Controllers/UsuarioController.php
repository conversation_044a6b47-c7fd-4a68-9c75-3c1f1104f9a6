<?php
namespace App\Http\Controllers;

use App\Services\UsuarioService;
use Illuminate\Http\Request;

class UsuarioController extends Controller
{
    private UsuarioService $service;

    public function __construct()
    {
        $this->service = new UsuarioService();
    }

    public function index(Request $request)
    {
        $usuarios = $this->service->getAllUsuarios();
        return response()->json(['estado' => 1, 'msg' => 'OK', 'datos' => $usuarios]);
    }
}
