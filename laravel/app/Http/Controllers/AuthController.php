<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\UsuarioService;

class AuthController extends Controller
{
    private UsuarioService $service;

    public function __construct()
    {
        $this->service = new UsuarioService();
    }

    public function login(Request $request)
    {
        $user = $request->input('user');
        $pass = $request->input('pass');
        if (!$user || !$pass) {
            return response()->json(['estado' => 0, 'msg' => 'Usuario y/o password vacios', 'datos' => null]);
        }

        $usuario = $this->service->autenticar($user, $pass);
        if (!$usuario) {
            return response()->json(['estado' => 0, 'msg' => 'Credenciales inválidas', 'datos' => null]);
        }

        $token = $this->service->generarToken($usuario);

        return response()->json([
            'estado' => 1,
            'msg' => 'OK',
            'datos' => [
                'usuario' => $usuario,
                'token' => $token
            ]
        ]);
    }
}
