<?php
namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UsuarioService
{
    public function getAllUsuarios()
    {
        $query = "SELECT usuarios.id as id,cedula,nombre,apellido,user,IF(per.descripcion IS NULL,'NINGUNO',per.descripcion) as perfil,id_perfil,estado_pass,(SELECT configuracion_general.radio_precision_gps FROM configuracion_general) AS tolerancia_visita FROM usuarios left join perfiles as per on (usuarios.id_perfil = per.id) limit 5";
        return DB::select($query);
    }

    public function finByUsername(string $user, string $pass): array
    {
        $response = [];
        $result = DB::selectOne("select password,bloqueo,id from usuarios where user = ?", [$user]);
        if (!$result) {
            $response['estado'] = 2;
            $response['msg'] = 'Error usuario y/o contraseña incorrectos';
            return $response;
        }

        $passbd = $result->password;
        $contBloqueo = (int) $result->bloqueo;
        $idus = (int) $result->id;

        if (Hash::check($pass, $passbd)) {
            $query = "select usuarios.id as id,cedula,nombre,apellido,user,IF(per.descripcion is null,'NINGUNO',per.descripcion) as perfil,id_perfil,estado_pass,now() as fecha_hora,(SELECT configuracion_general.radio_precision_gps FROM configuracion_general) AS tolerancia_visita FROM usuarios left join perfiles as per on (usuarios.id_perfil = per.id) where usuarios.estado = 1 and estado_bloqueo = 1 and usuarios.user = ?";
            $usuario = DB::selectOne($query, [$user]);
            $response['estado'] = 1;
            $response['usuario'] = $usuario;
            if ($contBloqueo > 0) {
                DB::update("update usuarios set bloqueo = 0 where id = ?", [$idus]);
            }
            return $response;
        } else {
            $contBloqueo++;
            if ($contBloqueo >= 3) {
                DB::update("update usuarios set bloqueo = ?, estado_bloqueo = 0 where id = ?", [$contBloqueo, $idus]);
                $response['msg'] = 'Error tu usuario ha sido bloqueado por nro de intentos fallidos';
            } else {
                DB::update("update usuarios set bloqueo = ? where id = ?", [$contBloqueo, $idus]);
                $response['msg'] = 'Error contraseña incorrecta, intentos disponibles: '.(3 - $contBloqueo);
            }
            $response['estado'] = 0;
            return $response;
        }
    }

    public function generarToken(int $idUsuario, string $tokenNotificacion, string $versionName, int $versionCode, array $datos = []): string
    {
        $current = time();
        $token = $idUsuario.rand(1000,9999).$current;
        DB::update("update usuarios set token_log = ?, fecha_ge_to = CURDATE(), hora_ge_to = CURTIME(), tiempo_session = NOW(), token_noti = ? where id = ?", [$token, $tokenNotificacion, $idUsuario]);
        DB::insert("INSERT INTO audi_logueo_app (fecha, hora, version_name, version_code, id_usuario) VALUES (CURDATE(), CURTIME(), ?, ?, ?)", [$versionName, $versionCode, $idUsuario]);
        return $token;
    }

    public function validarToken(string $token)
    {
        $query = "select usuarios.id as id,cedula,nombre,apellido,user,IF(per.descripcion is null,'NINGUNO',per.descripcion) as perfil,id_perfil,estado_pass,now() as fecha_hora,(SELECT configuracion_general.radio_precision_gps FROM configuracion_general) AS tolerancia_visita FROM usuarios left join perfiles as per on (usuarios.id_perfil = per.id) where usuarios.estado = 1 and estado_bloqueo = 1 and usuarios.token_log = ?";
        return DB::selectOne($query, [$token]);
    }

    public function validarEmail(string $email): int
    {
        $result = DB::selectOne("select id from usuarios where email = ?", [$email]);
        return $result ? (int) $result->id : 0;
    }

    public function guardarRecoPass(int $idus, string $recover): bool
    {
        $update = DB::update("update usuarios set recover = ?, fecha_reco = CURDATE(), hora_reco = ADDTIME(CURTIME(),'00:30:00') where id = ?", [$recover, $idus]);
        if ($update > 0) {
            DB::insert("insert into audi_usuarios (id_usuario,recover,usuario,movimiento) SELECT id,recover,id,2 FROM usuarios WHERE id = ?", [$idus]);
            return true;
        }
        return false;
    }

    public function cambiarPass(string $passold, string $passnew, int $idus): int
    {
        $result = DB::selectOne("select password from usuarios where id = ?", [$idus]);
        if (!$result) {
            return 0;
        }
        $passbd = $result->password;
        if (Hash::check($passold, $passbd)) {
            $hash = Hash::make($passnew);
            $ok = DB::update("update usuarios set password = ?, estado_pass = 1 where id = ?", [$hash, $idus]);
            if ($ok > 0) {
                DB::insert("insert into audi_usuarios (id_usuario,password,movimiento) SELECT id,password,2 FROM usuarios WHERE id = ?", [$idus]);
                return 1;
            }
            return 0;
        }
        return 2;
    }

    public function getPermisos(int $idUs, string $fechahora = null): array
    {
        if ($fechahora) {
            $query = "select id,tipo,group_concat(id_tipo) as id_tipos, if(movimiento = 1,movimiento,0) as accion, p2 from audi_niveles_dcs_detallado where id_usus = ? and concat(fecha,' ',hora) > ? group by tipo, p2,accion UNION SELECT id,4 AS tipo,group_concat(id) as id_tipos, estado AS accion, 0 AS p2 FROM territorios WHERE f_update > ? HAVING id_tipos IS NOT NULL UNION ALL SELECT id,5 AS tipo,group_concat(id) as id_tipos, estado AS accion, territorio AS p2 FROM zonas WHERE f_update > ? HAVING id_tipos IS NOT NULL";
            $niveles = DB::select($query, [$idUs, $fechahora, $fechahora, $fechahora]);
        } else {
            $query = "select id,tipo,group_concat(id_tipo) as id_tipos, 1 as accion, p2 from niveles_dcs where id_usus = ? group by tipo, p2";
            $niveles = DB::select($query, [$idUs]);
        }
        return ['niveles' => $niveles];
    }

    public function getPuntos(int $idUs): array
    {
        $query = "select pos.idpos,pos.cedula,pos.id_distri,pos.razon as nombre,pos.detalle_direccion as direccion,vis.fecha,vis.hora,vis.efectiva,IF((SELECT GROUP_CONCAT(DISTINCT id_encuesta) encuesta FROM enc__respuestas_encuesta WHERE tipo_encuestado = 2 AND id_encuestado = pos.idpos) IS NULL, '',(SELECT GROUP_CONCAT(DISTINCT id_encuesta) encuesta FROM enc__respuestas_encuesta WHERE tipo_encuestado = 2 AND id_encuestado = pos.idpos)) AS encuestas_respondidas,(SELECT COUNT(id) FROM visitas_operador AS vc WHERE vc.punto = vis.punto AND vc.usuario = vis.usuario AND vc.fecha = CURDATE()) AS cantidad_visitas, pos.precisiongps, pos.latitud, pos.longitud from visitas_operador as vis inner join puntos as pos on (pos.idpos = vis.punto) where vis.app = 1 AND vis.usuario = ? AND vis.fecha = CURDATE() AND pos.estado = 1 AND pos.aprobado = 1 GROUP BY idpos";
        $puntos = DB::select($query, [$idUs]);
        return ['puntosVisitados' => $puntos];
    }

    public function estadosComerciales(string $fecha = ''): array
    {
        if ($fecha !== '') {
            $query = "select id,descripcion, 1 as accion from categorias where fecha_hora_movi > ?";
            return DB::select($query, [$fecha]);
        }
        $query = "select id,descripcion,1 as accion from categorias";
        return DB::select($query);
    }

    public function listarMotivos(): array
    {
        $query = "SELECT id,motivo FROM motivos_visita_claro";
        return DB::select($query);
    }

}
