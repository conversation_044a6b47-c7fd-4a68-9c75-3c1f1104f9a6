package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class RespuestasOperador {
	
	@Id
	@Column(name = "id_respuesta")
	private int id;
	
	@Column(name = "respuesta")
	private String descripcion;
	
	@Column(name = "id_pregunta")
	private int idPregunta;
	
	@Column(name = "id_encuesta")
	private int idEncuesta;
	
	@Column(name = "orden")
	private int orden;
	
	@Column(name = "evidencia")
	private int evidencia;
	
	@Column(name = "evidencia_obligatoria")
	private int evidenciaObligatoria;
	
	@Column(name = "evidencia_observacion")
	private int evidenciaObservacion;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public int getIdPregunta() {
		return idPregunta;
	}

	public void setIdPregunta(int idPregunta) {
		this.idPregunta = idPregunta;
	}

	public int getIdEncuesta() {
		return idEncuesta;
	}

	public void setIdEncuesta(int idEncuesta) {
		this.idEncuesta = idEncuesta;
	}

	public int getOrden() {
		return orden;
	}

	public void setOrden(int orden) {
		this.orden = orden;
	}

	public int getEvidencia() {
		return evidencia;
	}

	public void setEvidencia(int evidencia) {
		this.evidencia = evidencia;
	}

	public int getEvidenciaObligatoria() {
		return evidenciaObligatoria;
	}

	public void setEvidenciaObligatoria(int evidenciaObligatoria) {
		this.evidenciaObligatoria = evidenciaObligatoria;
	}

	public int getEvidenciaObservacion() {
		return evidenciaObservacion;
	}

	public void setEvidenciaObservacion(int evidenciaObservacion) {
		this.evidenciaObservacion = evidenciaObservacion;
	}
	
	
}
