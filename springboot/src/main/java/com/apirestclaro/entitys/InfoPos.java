package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class InfoPos {

	@Id
	@Column(name = "idpos")
	private int idpos;

	@Column(name = "nombre")
	private String nombre;
	
	@Column(name = "nombre_propietario")
	private String nombrePropietario;

	@Column(name = "circuito")
	private String circuito;

	@Column(name = "ruta")
	private String ruta;

	@Column(name = "direccion")
	private String direccion;
	
	@Column(name = "telefono")
	private String telefono;	

	@Column(name = "tel_op")
	private String telefonoOp;
	
	@Column(name = "latitud")
	private double latitud;
	
	@Column(name = "longitud")
	private double longitud;
	
	@Column(name = "distribuidor")
	private String distribuidor;

	@Column(name = "fecha_visita")
	private String fechaVisita;
	
	@Column(name = "categoria")
	private String categoria;

	@Column(name = "tipologia")
	private String tipologia;

	public String getTipologia() {
		return tipologia;
	}

	public void setTipologia(String tipologia) {
		this.tipologia = tipologia;
	}
	
	public String getCategoria() {
		return categoria;
	}

	public void setCategoria(String categoria) {
		this.categoria = categoria;
	}

	private int efectiva;

	public int getIdpos() {
		return idpos;
	}

	public void setIdpos(int idpos) {
		this.idpos = idpos;
	}

	public String getNombre() {
		return nombre;
	}

	public void setNombre(String nombre) {
		this.nombre = nombre;
	}

	public String getCircuito() {
		return circuito;
	}

	public void setCircuito(String circuito) {
		this.circuito = circuito;
	}

	public String getRuta() {
		return ruta;
	}

	public void setRuta(String ruta) {
		this.ruta = ruta;
	}

	public String getDireccion() {
		return direccion;
	}

	public void setDireccion(String direccion) {
		this.direccion = direccion;
	}

	public String getTelefono() {
		return telefono;
	}

	public void setTelefono(String telefono) {
		this.telefono = telefono;
	}

	public String getTelefonoOp() {
		return telefonoOp;
	}

	public void setTelefonoOp(String telefonoOp) {
		this.telefonoOp = telefonoOp;
	}

	public double getLatitud() {
		return latitud;
	}

	public void setLatitud(double latitud) {
		this.latitud = latitud;
	}

	public double getLongitud() {
		return longitud;
	}

	public void setLongitud(double longitud) {
		this.longitud = longitud;
	}

	public String getDistribuidor() {
		return distribuidor;
	}

	public void setDistribuidor(String distribuidor) {
		this.distribuidor = distribuidor;
	}

	public String getFechaVisita() {
		return this.fechaVisita;
	}

	public void setFechaVisita(String fechaVisita) {
		this.fechaVisita = fechaVisita;
	}

	public int getEfectiva() {
		return this.efectiva;
	}

	public void setEfectiva(int efectiva) {
		this.efectiva = efectiva;
	}
	
	public String getNombrePropietario() {
		return nombrePropietario;
	}

	public void setNombrePropietario(String nombrePropietario) {
		this.nombrePropietario = nombrePropietario;
	}

}
