package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class InfoRotacion {
	/*
	@Id
	@Column(name = "id")
	private int id;
	 */
	
	@Column(name = "colocadas")
	private int colocadas;
	
	@Column(name = "activas")
	private int activas;
	
	@Column(name = "bajas")
	private int bajas;
	
	@Id
	@Column(name = "mes")
	private String mes;
/*
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}
 */
	public int getColocadas() {
		return colocadas;
	}

	public void setColocadas(int colocadas) {
		this.colocadas = colocadas;
	}

	public int getActivas() {
		return activas;
	}

	public void setActivas(int activas) {
		this.activas = activas;
	}

	public int getBajas() {
		return bajas;
	}

	public void setBajas(int bajas) {
		this.bajas = bajas;
	}

	public String getMes() {
		return mes;
	}

	public void setMes(String mes) {
		this.mes = mes;
	}
	

}
