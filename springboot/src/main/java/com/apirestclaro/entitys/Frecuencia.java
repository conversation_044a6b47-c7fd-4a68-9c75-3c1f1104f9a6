package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class Frecuencia {
	
	@Id
	@Column(name = "id")
	private int id;
	
	@Column(name = "proxvisita")
	private String proxVisita;
	
	@Column(name = "tipofrec")
	private String tipoFrec;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getProxVisita() {
		return proxVisita;
	}

	public void setProxVisita(String proxVisita) {
		this.proxVisita = proxVisita;
	}

	public String getTipoFrec() {
		return tipoFrec;
	}

	public void setTipoFrec(String tipoFrec) {
		this.tipoFrec = tipoFrec;
	}

}
