package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class Encuesta {

	@Id
	@Column(name = "id")
	private Long id;

	@Column(name = "titulo")
	private String titulo;

	@Column(name = "descripcion")
	private String descripcion;

	@Column(name = "obligatorio")
	private int obligatorio;

	@Column(name = "navegar_atras")
	private int navegaAtras;

	private int aplicar;

	@Column(name = "estado_accion")
	private int estadoAccion;

	public String getTitulo() {
		return titulo;
	}

	public void setTitulo(String titulo) {
		this.titulo = titulo;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public int getObligatorio() {
		return obligatorio;
	}

	public void setObligatorio(int obligatorio) {
		this.obligatorio = obligatorio;
	}

	public int getNavegaAtras() {
		return navegaAtras;
	}

	public void setNavegaAtras(int navegaAtras) {
		this.navegaAtras = navegaAtras;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getEstadoAccion() {
		return this.estadoAccion;
	}

	public void setEstadoAccion(int estadoAccion) {
		this.estadoAccion = estadoAccion;
	}

	public int getAplicar() {
		return this.aplicar;
	}

	public void setAplicar(int aplicar) {
		this.aplicar = aplicar;
	}

}
