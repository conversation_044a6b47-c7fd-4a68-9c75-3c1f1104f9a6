package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class DetalleVisita {
	
	@Id
	@Column(name = "id")
	private int id;
	
	@Column(name = "fecha")
	private String fecha;
	
	@Column(name = "usuario")
	private String usuario;
	
	@Column(name = "hora")
	private String horaCheckIn;
	
	@Column(name = "hora_cierre")
	private String horaCheckOut;
	
	@Column(name = "tipo_visita")
	private String tipoVisita;
	
	@Column(name = "visita_en_punto")
	private int visitaEnPunto;
	
	@Column(name = "qr")
	private String marcacionQr;
	
	@Column(name = "observacion_qr")
	private String observacionQr;
	
	@Column(name = "motivo_no_venta")
	private String motivoNoVenta;
	
	@Column(name = "observacion_no_venta")
	private String observacionNoVenta;
	
	@Column(name = "lat")
	private double latitudVendedor;
	
	@Column(name = "lng")
	private double longitudVendedor;
	
	@Column(name = "precisiongps")
	private double presicionGpsVendedor;
	
	@Column(name = "lat_pdv")
	private double latitudPunto;
	
	@Column(name = "long_pdv")
	private double longitudPunto;
	
	@Column(name = "precisiongps_punto")
	private double presicionGpsPunto;
	
	@Column(name = "tiempo_visita")
	private String tiempoVisita;
	

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getFecha() {
		return fecha;
	}
	
	

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public void setFecha(String fecha) {
		this.fecha = fecha;
	}

	public String getHoraCheckIn() {
		return horaCheckIn;
	}

	public void setHoraCheckIn(String horaCheckIn) {
		this.horaCheckIn = horaCheckIn;
	}


	public String getHoraCheckOut() {
		return horaCheckOut;
	}

	public void setHoraCheckOut(String horaCheckOut) {
		this.horaCheckOut = horaCheckOut;
	}

	public String getTipoVisita() {
		return tipoVisita;
	}

	public void setTipoVisita(String tipoVisita) {
		this.tipoVisita = tipoVisita;
	}

	public int getVisitaEnPunto() {
		return visitaEnPunto;
	}

	public void setVisitaEnPunto(int visitaEnPunto) {
		this.visitaEnPunto = visitaEnPunto;
	}

	public String getMarcacionQr() {
		return marcacionQr;
	}

	public void setMarcacionQr(String marcacionQr) {
		this.marcacionQr = marcacionQr;
	}
	
	public String getObservacionQr() {
		return observacionQr;
	}

	public void setObservacionQr(String observacionQr) {
		this.observacionQr = observacionQr;
	}

	public String getMotivoNoVenta() {
		return motivoNoVenta;
	}

	public void setMotivoNoVenta(String motivoNoVenta) {
		this.motivoNoVenta = motivoNoVenta;
	}

	public String getObservacionNoVenta() {
		return observacionNoVenta;
	}

	public void setObservacionNoVenta(String observacionNoVenta) {
		this.observacionNoVenta = observacionNoVenta;
	}

	public double getLatitudVendedor() {
		return latitudVendedor;
	}

	public void setLatitudVendedor(double latitudVendedor) {
		this.latitudVendedor = latitudVendedor;
	}

	public double getLongitudVendedor() {
		return longitudVendedor;
	}

	public void setLongitudVendedor(double longitudVendedor) {
		this.longitudVendedor = longitudVendedor;
	}

	public double getPresicionGpsVendedor() {
		return presicionGpsVendedor;
	}

	public void setPresicionGpsVendedor(double presicionGpsVendedor) {
		this.presicionGpsVendedor = presicionGpsVendedor;
	}

	public double getLatitudPunto() {
		return latitudPunto;
	}

	public void setLatitudPunto(double latitudPunto) {
		this.latitudPunto = latitudPunto;
	}

	public double getLongitudPunto() {
		return longitudPunto;
	}

	public void setLongitudPunto(double longitudPunto) {
		this.longitudPunto = longitudPunto;
	}

	public double getPresicionGpsPunto() {
		return presicionGpsPunto;
	}

	public void setPresicionGpsPunto(double presicionGpsPunto) {
		this.presicionGpsPunto = presicionGpsPunto;
	}

	public String getTiempoVisita() {
		return tiempoVisita;
	}

	public void setTiempoVisita(String tiempoVisita) {
		this.tiempoVisita = tiempoVisita;
	}
	
	
	
}
