package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class CarteraPuntos {
	
	@Id
	@Column(name = "idpos")
	private int idPos;

	@Column(name = "razon")
	private String nombrePdv;
	
	@Column(name = "nombre_cli")
	private String nombreCliente;

	@Column(name = "tipo_doc")
	private String tipoDocumento;

	@Column(name = "cedula")
	private String numeroDocumento;

	@Column(name = "email")
	private String correo;

	@Column(name = "tel")
	private String telefonoFijo;

	@Column(name = "celular")
	private String celular;

	@Column(name = "tel_op")
	private String telefonoOp;

	@Column(name = "categoria")
	private int idCategoria;

	@Column(name = "id_escala")
	private int idCategoriaClasificacion;

	@Column(name = "depto")
	private int idDepartamento;

	@Column(name = "ciudad")
	private int idMunicipio;

	@Column(name = "territorio")
	private int idCircuito;

	@Column(name = "zona")
	private int idRuta;

	@Column(name = "estado_com")
	private int idEstadoComercial;

	@Column(name = "barrio")
	private String barrio;

	@Column(name = "detalle_direccion")
	private String direccion;


	@Column(name = "latitud")
	private double latitud;

	@Column(name = "longitud")
	private double longitud;

	@Column(name = "id_distri")
	private int idAgente;

	public int getIdPos() {
		return idPos;
	}

	public void setIdPos(int idPos) {
		this.idPos = idPos;
	}

	public String getNombrePdv() {
		return nombrePdv;
	}

	public void setNombrePdv(String nombrePdv) {
		this.nombrePdv = nombrePdv;
	}

	public String getNombreCliente() {
		return nombreCliente;
	}

	public void setNombreCliente(String nombreCliente) {
		this.nombreCliente = nombreCliente;
	}

	public String getTipoDocumento() {
		return tipoDocumento;
	}

	public void setTipoDocumento(String tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}

	public String getNumeroDocumento() {
		return numeroDocumento;
	}

	public void setNumeroDocumento(String numeroDocumento) {
		this.numeroDocumento = numeroDocumento;
	}

	public String getCorreo() {
		return correo;
	}

	public void setCorreo(String correo) {
		this.correo = correo;
	}

	public String getTelefonoFijo() {
		return telefonoFijo;
	}

	public void setTelefonoFijo(String telefonoFijo) {
		this.telefonoFijo = telefonoFijo;
	}

	public String getCelular() {
		return celular;
	}

	public void setCelular(String celular) {
		this.celular = celular;
	}

	public String getTelefonoOp() {
		return telefonoOp;
	}

	public void setTelefonoOp(String telefonoOp) {
		this.telefonoOp = telefonoOp;
	}

	public int getIdCategoria() {
		return idCategoria;
	}

	public void setIdCategoria(int idCategoria) {
		this.idCategoria = idCategoria;
	}

	public int getIdCategoriaClasificacion() {
		return idCategoriaClasificacion;
	}

	public void setIdCategoriaClasificacion(int idCategoriaClasificacion) {
		this.idCategoriaClasificacion = idCategoriaClasificacion;
	}

	public int getIdDepartamento() {
		return idDepartamento;
	}

	public void setIdDepartamento(int idDepartamento) {
		this.idDepartamento = idDepartamento;
	}

	public int getIdMunicipio() {
		return idMunicipio;
	}

	public void setIdMunicipio(int idMunicipio) {
		this.idMunicipio = idMunicipio;
	}

	public int getIdCircuito() {
		return idCircuito;
	}

	public void setIdCircuito(int idCircuito) {
		this.idCircuito = idCircuito;
	}

	public int getIdRuta() {
		return idRuta;
	}

	public void setIdRuta(int idRuta) {
		this.idRuta = idRuta;
	}

	public int getIdEstadoComercial() {
		return idEstadoComercial;
	}

	public void setIdEstadoComercial(int idEstadoComercial) {
		this.idEstadoComercial = idEstadoComercial;
	}

	public String getBarrio() {
		return barrio;
	}

	public void setBarrio(String barrio) {
		this.barrio = barrio;
	}

	public String getDireccion() {
		return direccion;
	}

	public void setDireccion(String direccion) {
		this.direccion = direccion;
	}

	public double getLatitud() {
		return latitud;
	}

	public void setLatitud(double latitud) {
		this.latitud = latitud;
	}

	public double getLongitud() {
		return longitud;
	}

	public void setLongitud(double longitud) {
		this.longitud = longitud;
	}
	
	public int getIdAgente() {
		return idAgente;
	}

	public void setIdAgente(int idAgente) {
		this.idAgente = idAgente;
	}

}
