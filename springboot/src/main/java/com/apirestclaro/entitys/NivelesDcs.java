package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class NivelesDcs {
	
	@Id
	private long id;
	
	@Column(name = "tipo")
	private int tipo;
	
	@Column(name = "id_tipos")
	private String idTipos;
	
	@Column(name = "accion")
	private int accion;

	private int p2;
	
	public int getTipo() {
		return tipo;
	}
	public void setTipo(int tipo) {
		this.tipo = tipo;
	}
	public String getIdTipos() {
		return idTipos;
	}
	public void setIdTipo(String idTipos) {
		this.idTipos = idTipos;
	}
	public int getAccion() {
		return accion;
	}
	public void setAccion(int accion) {
		this.accion = accion;
	}

	public long getId() {
		return this.id;
	}

	public void setId(long id) {
		this.id = id;
	}
	public void setIdTipos(String idTipos) {
		this.idTipos = idTipos;
	}

	public int getP2() {
		return this.p2;
	}

	public void setP2(int p2) {
		this.p2 = p2;
	}
	
}
