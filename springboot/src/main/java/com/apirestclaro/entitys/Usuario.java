package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class Usuario {
	
	@Id
	@Column(name = "id")
	private Long id;
	
	@Column(name = "cedula")
	private String cedula;
	
	@Column(name = "nombre")
	private String nombre;
	
	@Column(name = "apellido")
	private String apellido;
	
	@Column(name = "user")
	private String username;
	
	@Column(name = "perfil")
	private String perfil;
	
	@Column(name = "id_perfil")
	private Integer idPerfil;
	
	@Column(name = "estado_pass")
	private Integer primerLogueo;
	
	@Column(name = "fecha_hora")
	private String fechaHora;

	@Column(name="tolerancia_visita")
	private int toleranciaVisita;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getNombre() {
		return nombre;
	}
	public void setNombre(String nombre) {
		this.nombre = nombre;
	}
	public String getApellido() {
		return apellido;
	}
	public void setApellido(String apellido) {
		this.apellido = apellido;
	}
	public String getPerfil() {
		return perfil;
	}
	public void setPerfil(String perfil) {
		this.perfil = perfil;
	}
	public String getCedula() {
		return cedula;
	}
	public void setCedula(String cedula) {
		this.cedula = cedula;
	}
	public Integer getIdPerfil() {
		return idPerfil;
	}
	public void setIdPerfil(Integer idPerfil) {
		this.idPerfil = idPerfil;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public Integer getPrimerLogueo() {
		return primerLogueo;
	}
	public void setPrimerLogueo(Integer primerLogueo) {
		this.primerLogueo = primerLogueo;
	}
	public String getFechaHora() {
		return fechaHora;
	}
	public void setFechaHora(String fechaHora) {
		this.fechaHora = fechaHora;
	}
	
	public int getToleranciaVisita() {
		return this.toleranciaVisita;
	}

	public void setToleranciaVisita(int toleranciaVisita) {
		this.toleranciaVisita = toleranciaVisita;
	}
	
}
