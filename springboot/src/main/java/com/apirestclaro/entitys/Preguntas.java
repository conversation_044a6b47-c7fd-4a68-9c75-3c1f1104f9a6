package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class Preguntas {
    
	@Id
	@Column(name = "id")
	private int id;
	
	@Column(name = "pregunta")
	private String descripcion;
	
	@Column(name = "id_encuesta")
	private int idEncuesta;
	
	@Column(name = "tipo")
	private int tipo;
	
	@Column(name = "id_respuesta")
	private int idRespuestaPadre;
	
	@Column(name = "obligatorio")
	private int obligatorio;
	
	@Column(name = "orden")
	private int orden;
	

	public int getObligatorio() {
		return obligatorio;
	}

	public void setObligatorio(int obligatorio) {
		this.obligatorio = obligatorio;
	}

	public int getOrden() {
		return orden;
	}

	public void setOrden(int orden) {
		this.orden = orden;
	}

	public int getIdRespuestaPadre() {
		return idRespuestaPadre;
	}

	public void setIdRespuestaPadre(int idRespuestaPadre) {
		this.idRespuestaPadre = idRespuestaPadre;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public int getIdEncuesta() {
		return idEncuesta;
	}

	public void setIdEncuesta(int idEncuesta) {
		this.idEncuesta = idEncuesta;
	}

	public int getTipo() {	
		return tipo;
	}

	public void setTipo(int tipo) {
		this.tipo = tipo;
	}
}
