package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class InfoProducto {
	
	@Id
	@Column(name = "id")
	private int id;
	
	@Column(name = "serial")
	private String serial;
	
	@Column(name = "pn")
	private String pn;
	
	@Column(name = "referencia")
	private String referencia;
	
	@Column(name = "paquete")
	private String paquete;
	
	@Column(name = "movil")
	private String movil;
	
	@Column(name = "id_combo")
	private int id_combo;
	
	@Column(name = "archivo_actualiza")
	private int archivo_actualiza;
	
	@Column(name = "archivo")
	private int archivo;
	
	@Column(name = "estado")
	private String estado;
	
	@Column(name = "fecha_aceptacion")
	private String fecha_aceptacion;
	
	@Column(name = "hora_aceptacion")
	private String hora_aceptacion;
	
	@Column(name = "id_sol_traslado")
	private int id_sol_traslado;
	
	@Column(name = "id_bodega")
	private int id_bodega;
	
	@Column(name = "id_bodega_distri")
	private int id_bodega_distri;
	
	@Column(name = "fecha_asignacion")
	private String fecha_asignacion;
	
	@Column(name = "hora_asignacion")
	private String hora_asignacion;
	
	@Column(name = "id_vendedor")
	private int id_vendedor;
	
	@Column(name = "distri")
	private int distri;
	
	@Column(name = "distribuidor")
	private String distribuidor;
	
	@Column(name = "venta")
	private int venta;
	
	@Column(name = "fecha_venta")
	private String fecha_venta;
	
	@Column(name = "hora_venta")
	private String hora_venta;
	
	@Column(name = "fecha_ingreso")
	private String fechaIngreso;
	
	@Column(name = "fecha_ac")
	private String fechaAc;
	
	@Column(name = "hora_ac")
	private String horaAc;
	
	@Column(name = "vendedor")
	private String vendedor;
	
	@Column(name = "nombre_punto")
	private String nombrePunto;
	
	@Column(name = "nombre_corto")
	private String nombreCorto;
	
	@Column(name = "id_pos")
	private int idPos;
	
	@Column(name = "coid")
	private String coid;
	
	@Column(name = "activo")
	private int activo;
	
	@Column(name = "nombre_bodega")
	private String nombreBodega;

	public String getNombreBodega() {
		return nombreBodega;
	}
	
	public void setNombreBodega(String nombreBodega) {
		this.nombreBodega = nombreBodega;
	}

	public int getActivo() {
		return activo;
	}

	public void setActivo(int activo) {
		this.activo = activo;
	}

	public String getCoid() {
		return coid;
	}

	public void setCoid(String coid) {
		this.coid = coid;
	}

	public String getNombreCorto() {
		return nombreCorto;
	}

	public void setNombreCorto(String nombreCorto) {
		this.nombreCorto = nombreCorto;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getSerial() {
		return serial;
	}

	public void setSerial(String serial) {
		this.serial = serial;
	}

	public String getPn() {
		return pn;
	}

	public void setPn(String pn) {
		this.pn = pn;
	}

	public String getReferencia() {
		return referencia;
	}

	public void setReferencia(String referencia) {
		this.referencia = referencia;
	}

	public String getPaquete() {
		return paquete;
	}

	public void setPaquete(String paquete) {
		this.paquete = paquete;
	}

	public String getMovil() {
		return movil;
	}

	public void setMovil(String movil) {
		this.movil = movil;
	}

	public int getId_combo() {
		return id_combo;
	}

	public void setId_combo(int id_combo) {
		this.id_combo = id_combo;
	}

	public int getArchivo_actualiza() {
		return archivo_actualiza;
	}

	public void setArchivo_actualiza(int archivo_actualiza) {
		this.archivo_actualiza = archivo_actualiza;
	}

	public int getArchivo() {
		return archivo;
	}

	public void setArchivo(int archivo) {
		this.archivo = archivo;
	}

	public String getEstado() {
		return estado;
	}

	public void setEstado(String estado) {
		this.estado = estado;
	}

	public String getFecha_aceptacion() {
		return fecha_aceptacion;
	}

	public void setFecha_aceptacion(String fecha_aceptacion) {
		this.fecha_aceptacion = fecha_aceptacion;
	}

	public String getHora_aceptacion() {
		return hora_aceptacion;
	}

	public void setHora_aceptacion(String hora_aceptacion) {
		this.hora_aceptacion = hora_aceptacion;
	}

	public int getId_sol_traslado() {
		return id_sol_traslado;
	}

	public void setId_sol_traslado(int id_sol_traslado) {
		this.id_sol_traslado = id_sol_traslado;
	}

	public int getId_bodega() {
		return id_bodega;
	}

	public void setId_bodega(int id_bodega) {
		this.id_bodega = id_bodega;
	}

	public int getId_bodega_distri() {
		return id_bodega_distri;
	}

	public void setId_bodega_distri(int id_bodega_distri) {
		this.id_bodega_distri = id_bodega_distri;
	}

	public String getFecha_asignacion() {
		return fecha_asignacion;
	}

	public void setFecha_asignacion(String fecha_asignacion) {
		this.fecha_asignacion = fecha_asignacion;
	}

	public String getHora_asignacion() {
		return hora_asignacion;
	}

	public void setHora_asignacion(String hora_asignacion) {
		this.hora_asignacion = hora_asignacion;
	}

	public int getId_vendedor() {
		return id_vendedor;
	}

	public void setId_vendedor(int id_vendedor) {
		this.id_vendedor = id_vendedor;
	}

	public int getDistri() {
		return distri;
	}

	public void setDistri(int distri) {
		this.distri = distri;
	}

	public int getVenta() {
		return venta;
	}

	public void setVenta(int venta) {
		this.venta = venta;
	}

	public String getFecha_venta() {
		return fecha_venta;
	}

	public void setFecha_venta(String fecha_venta) {
		this.fecha_venta = fecha_venta;
	}

	public String getHora_venta() {
		return hora_venta;
	}

	public void setHora_venta(String hora_venta) {
		this.hora_venta = hora_venta;
	}

	public String getFechaIngreso() {
		return fechaIngreso;
	}

	public void setFechaIngreso(String fecha_ingreso) {
		this.fechaIngreso = fecha_ingreso;
	}

	public String getDistribuidor() {
		return distribuidor;
	}

	public void setDistribuidor(String distribuidor) {
		this.distribuidor = distribuidor;
	}

	public String getFechaAc() {
		return fechaAc;
	}

	public void setFechaAc(String fechaAc) {
		this.fechaAc = fechaAc;
	}

	public String getHoraAc() {
		return horaAc;
	}

	public void setHoraAc(String horaAc) {
		this.horaAc = horaAc;
	}

	public String getVendedor() {
		return vendedor;
	}

	public void setVendedor(String vendedor) {
		this.vendedor = vendedor;
	}

	public String getNombrePunto() {
		return nombrePunto;
	}

	public void setNombrePunto(String nombrePunto) {
		this.nombrePunto = nombrePunto;
	}

	public int getIdPos() {
		return idPos;
	}

	public void setIdPos(int idPos) {
		this.idPos = idPos;
	}
	
}
