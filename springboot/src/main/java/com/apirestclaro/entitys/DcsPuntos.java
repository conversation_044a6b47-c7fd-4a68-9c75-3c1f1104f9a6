package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class DcsPuntos {
	
	@Id
	private Long id;
	@Column(name = "territorio")
	private Long idCircuito;
	@Column(name = "zona")
	private Long idRuta;
	@Column(name = "id_regional")
	private Long idRegional;
	@Column(name = "id_distri")
	private Long idDistribuidor;

	private double latitud;

	private double longitud;

	public Long getIdCircuito() {
		return idCircuito;
	}

	public void setIdCircuito(Long idCircuito) {
		this.idCircuito = idCircuito;
	}

	public Long getIdRuta() {
		return idRuta;
	}

	public void setIdRuta(Long idRuta) {
		this.idRuta = idRuta;
	}

	public Long getIdRegional() {
		return idRegional;
	}

	public void setIdRegional(Long idRegional) {
		this.idRegional = idRegional;
	}

	public Long getIdDistribuidor() {
		return idDistribuidor;
	}

	public void setIdDistribuidor(Long idDistribuidor) {
		this.idDistribuidor = idDistribuidor;
	}

	public double getLatitud() {
		return this.latitud;
	}

	public void setLatitud(double latitud) {
		this.latitud = latitud;
	}

	public double getLongitud() {
		return this.longitud;
	}

	public void setLongitud(double longitudPdv) {
		this.longitud = longitudPdv;
	}

}
