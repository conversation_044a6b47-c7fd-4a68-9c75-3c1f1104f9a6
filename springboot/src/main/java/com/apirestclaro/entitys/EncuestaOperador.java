package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class EncuestaOperador {
	
	@Id
	@Column(name = "id_encuesta")
	private Long id;
	
	@Column(name = "titulo")
	private String titulo;
	
	@Column(name = "estado_encuesta")
	private int estadoEncuesta;

	@Column(name = "descripcion_encuesta")
	private String descripcion;
	
	@Column(name = "fecha_crea")
	private String fechaCrea;
	
	@Column(name = "hora_crea")
	private String horaCrea;
	
	@Column(name = "fecha_inicio")
	private String fechaInicio;
	
	@Column(name = "fecha_fin")
	private String fechaFin;
	
	@Column(name = "vigente")
	private int vigente;

	@Column(name = "obligatorio")
	private int obligatorio;

	@Column(name = "navegar_atras")
	private int navegaAtras;
	
	@Column(name = "estado_accion")
	private int estadoAccion;
	
	
	@Column(name = "aplicar")
	private int aplicar;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTitulo() {
		return titulo;
	}

	public void setTitulo(String titulo) {
		this.titulo = titulo;
	}

	public int getEstadoEncuesta() {
		return estadoEncuesta;
	}

	public void setEstadoEncuesta(int estadoEncuesta) {
		this.estadoEncuesta = estadoEncuesta;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public String getFechaCrea() {
		return fechaCrea;
	}

	public void setFechaCrea(String fechaCrea) {
		this.fechaCrea = fechaCrea;
	}

	public String getHoraCrea() {
		return horaCrea;
	}

	public void setHoraCrea(String horaCrea) {
		this.horaCrea = horaCrea;
	}

	public String getFechaInicio() {
		return fechaInicio;
	}

	public void setFechaInicio(String fechaInicio) {
		this.fechaInicio = fechaInicio;
	}

	public String getFechaFin() {
		return fechaFin;
	}

	public void setFechaFin(String fechaFin) {
		this.fechaFin = fechaFin;
	}

	public int getVigente() {
		return vigente;
	}

	public void setVigente(int vigente) {
		this.vigente = vigente;
	}

	public int getObligatorio() {
		return obligatorio;
	}

	public void setObligatorio(int obligatorio) {
		this.obligatorio = obligatorio;
	}

	public int getNavegaAtras() {
		return navegaAtras;
	}

	public void setNavegaAtras(int navegaAtras) {
		this.navegaAtras = navegaAtras;
	}

	public int getEstadoAccion() {
		return estadoAccion;
	}

	public void setEstadoAccion(int estadoAccion) {
		this.estadoAccion = estadoAccion;
	}


	public int getAplicar() {
		return aplicar;
	}

	public void setAplicar(int aplicar) {
		this.aplicar = aplicar;
	}

}
