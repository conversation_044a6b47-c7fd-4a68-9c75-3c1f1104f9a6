package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class PreguntasOperador {
	
	@Id
	@Column(name = "id_pregunta")
	private int id;
	
	@Column(name = "pregunta")
	private String descripcion;
	
	@Column(name = "id_encuesta")
	private int idEncuesta;
	
	@Column(name = "tipo_pregunta")
	private int tipo;
	
	@Column(name = "obligatorio")
	private int obligatorio;
	
	@Column(name = "orden")
	private int orden;
	
	@Column(name = "id_respuesta")
	private int idRespuestaPadre;
	
	@Column(name = "evidencia")
	private int evidencia;
	
	@Column(name = "evidencia_obligatoria")
	private int evidenciaObligatoria;
	
	@Column(name = "evidencia_observacion")
	private int evidenciaObservacion;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public int getIdEncuesta() {
		return idEncuesta;
	}

	public void setIdEncuesta(int idEncuesta) {
		this.idEncuesta = idEncuesta;
	}

	public int getTipo() {
		return tipo;
	}

	public void setTipo(int tipo) {
		this.tipo = tipo;
	}

	public int getObligatorio() {
		return obligatorio;
	}

	public void setObligatorio(int obligatorio) {
		this.obligatorio = obligatorio;
	}

	public int getOrden() {
		return orden;
	}

	public void setOrden(int orden) {
		this.orden = orden;
	}

	public int getIdRespuestaPadre() {
		return idRespuestaPadre;
	}

	public void setIdRespuestaPadre(int idRespuestaPadre) {
		this.idRespuestaPadre = idRespuestaPadre;
	}

	public int getEvidencia() {
		return evidencia;
	}

	public void setEvidencia(int evidencia) {
		this.evidencia = evidencia;
	}

	public int getEvidenciaObligatoria() {
		return evidenciaObligatoria;
	}

	public void setEvidenciaObligatoria(int evidenciaObligatoria) {
		this.evidenciaObligatoria = evidenciaObligatoria;
	}

	public int getEvidenciaObservacion() {
		return evidenciaObservacion;
	}

	public void setEvidenciaObservacion(int evidenciaObservacion) {
		this.evidenciaObservacion = evidenciaObservacion;
	}
	
	
}
