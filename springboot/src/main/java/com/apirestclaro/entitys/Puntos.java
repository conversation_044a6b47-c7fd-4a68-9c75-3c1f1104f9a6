package com.apirestclaro.entitys;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class Puntos {

	@Id
	@Column(name = "idpos")
	private int idPos;

	@Column(name = "nombre")
	private String nombre;

	@Column(name = "fecha")
	private String fecha;

	@Column(name = "hora")
	private String hora;

	@Column(name = "efectiva")
	private int efectiva;

	@Column(name = "direccion")
	private String direccion;

	@Column(name = "encuestas_respondidas", columnDefinition="Test() default ''")
	private String encuestasRespondidas;

	@Column(name = "cantidad_visitas")
	private int cantidadVisitas;

	@Column(name = "precisiongps")
	private double precisionGps;
	
	@Column(name = "latitud")
	private double latitud;
	
	@Column(name = "longitud")
	private double longitud;

	@Column(name = "id_distri")
	private int idAgente;
	
	@Column(name = "cedula")
	private String documento;

	public int getEfectiva() {
		return efectiva;
	}

	public void setEfectiva(int efectiva) {
		this.efectiva = efectiva;
	}

	public int getIdPos() {
		return idPos;
	}

	public void setIdPos(int idPos) {
		this.idPos = idPos;
	}

	public String getNombre() {
		return nombre;
	}

	public void setNombre(String nombre) {
		this.nombre = nombre;
	}

	public String getFecha() {
		return fecha;
	}

	public void setFecha(String fecha) {
		this.fecha = fecha;
	}

	public String getHora() {
		return hora;
	}

	public void setHora(String hora) {
		this.hora = hora;
	}

	public String getDireccion() {
		return direccion;
	}

	public void setDireccion(String direccion) {
		this.direccion = direccion;
	}

	public String getEncuestasRespondidas() {
		return encuestasRespondidas;
	}

	public void setEncuestasRespondidas(String encuestasRespondidas) {
		this.encuestasRespondidas = encuestasRespondidas;
	}


	public int getCantidadVisitas() {
		return this.cantidadVisitas;
	}

	public void setCantidadVisitas(int cantidadVisitas) {
		this.cantidadVisitas = cantidadVisitas;
	}

	public double getPrecisionGps() {
		return this.precisionGps;
	}

	public void setPrecisionGps(double precisionGps) {
		this.precisionGps = precisionGps;
	}

	public double getLatitud() {
		return latitud;
	}

	public void setLatitud(double latitud) {
		this.latitud = latitud;
	}

	public double getLongitud() {
		return longitud;
	}

	public void setLongitud(double longitud) {
		this.longitud = longitud;
	}

	public int getIdAgente() {
		return idAgente;
	}

	public void setIdAgente(int idAgente) {
		this.idAgente = idAgente;
	}

	public String getDocumento() {
		return documento;
	}

	public void setDocumento(String documento) {
		this.documento = documento;
	}
}
