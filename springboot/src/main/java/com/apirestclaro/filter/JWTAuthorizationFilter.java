package com.apirestclaro.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import com.apirestclaro.entitys.Usuario;
import com.apirestclaro.functions.Functions;
import com.apirestclaro.functions.SimpleGrantedAuthorityMixin;
import com.apirestclaro.services.IUsuarioService;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JWTAuthorizationFilter extends BasicAuthenticationFilter {
		
	private IUsuarioService usuarioservice;
	
	public JWTAuthorizationFilter(AuthenticationManager authenticationManager) {
		super(authenticationManager);
	}
	
	public JWTAuthorizationFilter(AuthenticationManager authenticationManager, IUsuarioService usuarioservice) {
		super(authenticationManager);
		this.usuarioservice = usuarioservice;
	}
	

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		
		String header = request.getHeader("Authorization");

		if (!requiresAuthentication(header)) {
			chain.doFilter(request, response);
			return;
		}

		boolean tokenValido;
		
		Usuario us = null;
		String tokenus = "";

		try {
			tokenus = header.replace("Bearer ", "");
			//us = usuarioservice.validarToken(tokenus);
			
			if(!tokenus.isEmpty()) {
				tokenValido = true;	
			}
			else {
				tokenValido = false;
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			tokenValido = false;
		}

		UsernamePasswordAuthenticationToken authentication = null;

		if (tokenValido) {
			String username = "OperadorAp";
			//Functions.setUsuario(us);

			List grantedAuthorities = AuthorityUtils.commaSeparatedStringToAuthorityList("Admin");

			Object roles = new ObjectMapper().writeValueAsString(grantedAuthorities);			
			
			Collection<? extends GrantedAuthority> authorities = Arrays
					.asList(new ObjectMapper().addMixIn(SimpleGrantedAuthority.class, SimpleGrantedAuthorityMixin.class)
							.readValue(roles.toString().getBytes(), SimpleGrantedAuthority[].class));

			
			authentication = new UsernamePasswordAuthenticationToken(username, null, authorities);

		}	
		
		SecurityContextHolder.getContext().setAuthentication(authentication);
		chain.doFilter(request, response);

	}

	protected boolean requiresAuthentication(String header) {
		if (header == null || !header.startsWith("Bearer ")) {
			return false;
		}
		return true;
	}

}
