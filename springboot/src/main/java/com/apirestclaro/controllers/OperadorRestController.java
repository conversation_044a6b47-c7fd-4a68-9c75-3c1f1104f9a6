package com.apirestclaro.controllers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.apirestclaro.dao.InfoHistorial;
import com.apirestclaro.entitys.CarteraPuntos;
import com.apirestclaro.entitys.CategoriasPuntos;
import com.apirestclaro.entitys.DcsPuntos;
import com.apirestclaro.entitys.Departamentos;
import com.apirestclaro.entitys.DetalleInventario;
import com.apirestclaro.entitys.EscalasPdv;
import com.apirestclaro.entitys.InfoInventario;
import com.apirestclaro.entitys.InfoPos;
import com.apirestclaro.entitys.InfoRotacion;
import com.apirestclaro.entitys.Motivos;
import com.apirestclaro.entitys.Municipios;
import com.apirestclaro.entitys.Puntos;
import com.apirestclaro.entitys.TiposDocumento;
import com.apirestclaro.entitys.Usuario;
import com.apirestclaro.functions.Functions;
import com.apirestclaro.pojos.CrearActualizarPuntosPost;
import com.apirestclaro.pojos.EncuestaRespondida;
import com.apirestclaro.pojos.FormularioDinamico;
import com.apirestclaro.pojos.ImgFormularioDinamico;
import com.apirestclaro.pojos.Menu;
import com.apirestclaro.services.IAwsService;
import com.apirestclaro.services.IEncuestaService;
import com.apirestclaro.services.IInventarioService;
import com.apirestclaro.services.IPermisosService;
import com.apirestclaro.services.IPuntosService;
import com.apirestclaro.services.ITabsCheckIn;
import com.apirestclaro.services.IUsuarioService;
import com.apirestclaro.services.IVersionService;
import com.apirestclaro.services.IVisitaService;
import com.apirestclaro.services.JsonParserService;
import com.apirestclaro.services.MailService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


@RestController
@Validated
@RequestMapping("/api")
public class OperadorRestController {

	@Autowired
	private IUsuarioService usuarioservice;

	@Autowired
	private MailService mailservice;
	
	@Autowired
	private IEncuestaService encuestaservice;
	
	@Autowired
	private IPuntosService puntosservice;
	
	@Autowired
	private ITabsCheckIn tabscheckin;
	
	@Autowired
	private IVisitaService visitaService;

	@Autowired
	private IVersionService versionService;
	
	@Autowired
	private IPermisosService permisosService;
	
	@Autowired
	private IInventarioService inventarioService;
	
	@Autowired
	private IAwsService awsService;
	
	@Autowired
	private JsonParserService jsonParseService;

	@Value("${operador.ur}")
	private String uri;

	private Map<String, Object> mapa;


	@GetMapping("/usuarios")
	public @ResponseBody Map<String, Object> listar() {
		mapa = new HashMap<String, Object>();

		mapa.put("estado", 1);
		mapa.put("msg", "OK");
		mapa.put("datos", usuarioservice.getAllUsuarios());

		return mapa;
	}

	private String toMayusculas(String valor) {
		if (valor == null || valor.isEmpty()) {
			return valor;
		} else {
			return valor.toUpperCase().charAt(0) + valor.substring(1, valor.length()).toLowerCase();
		}
	}
	
	private Usuario validarToken(String header) {
		String tokenus = "";
		Usuario us = null;
		try {
			if(header.isEmpty()) {
				return null;
			}
			tokenus = header.replace("Bearer ", "");
			us = usuarioservice.validarToken(tokenus);
			
			if(us != null) {
				return us;
			}
			else {
				return null;
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	private ResponseEntity<?> accesoDenegado (){
		Map<String, Object> denegado = new HashMap<String, Object>();
		denegado.put("status", 403);
		denegado.put("error", "Forbidden");
		denegado.put("message", "Access Denied");
		return new ResponseEntity<>(denegado,HttpStatus.FORBIDDEN);	
	}
	
	/// datos en formato json
	@PostMapping("/login")
	public ResponseEntity<?> login(@RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();

		String user = null;
		String pass = null;
		String fechahora = "";
		String versionName = "";
		String tokenNotificacion = "";
		int versionCode = 0;
		

		if (datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
			mapa.put("datos", null);
		} else {
			if (datos.containsKey("user") && datos.containsKey("pass")) {
				user = datos.get("user").toString();
				pass = datos.get("pass").toString();
				if(datos.containsKey("fechaHora")) {
					fechahora = datos.get("fechaHora").toString();
				}

				if(datos.containsKey("versionName")) {
					versionName = datos.get("versionName").toString();
				}

				if(datos.containsKey("versionCode")) {
					versionCode = (int) datos.get("versionCode");
				}

				if(datos.containsKey("tokenNotificacion")) {
					tokenNotificacion = datos.get("tokenNotificacion").toString();;
				}
				

				if(!versionName.isEmpty()){
					if(versionService.validarVersion(versionName, versionCode)){
						mapa.put("estado", 0);
						mapa.put("msg", "Usuario y/o password vacios");
						mapa.put("datos", null);
					

						Map<String,Object> us = usuarioservice.getUsuario(user, pass);
						if (Integer.parseInt(us.get("estado").toString()) != 1) {
							mapa.put("estado", 0);
							mapa.put("msg",us.get("msg").toString());
							mapa.put("datos", null);
						} else {
							Usuario usresponse = (Usuario)us.get("usuario");
							String token = usuarioservice.generarToken(usresponse, tokenNotificacion, versionName, versionCode,datos);

							if (token.equals("Error")) {
								mapa.put("estado", 0);
								mapa.put("msg", "Error al generar el token de acceso");
								mapa.put("datos", null);
							} else {
								
								Map<String, Object> usuario = new HashMap<String, Object>();
								usuario.put("id", usresponse.getId());
								usuario.put("cedula", usresponse.getCedula());
								usuario.put("nombre", toMayusculas(usresponse.getNombre()) + " " + toMayusculas(usresponse.getApellido()));
								usuario.put("perfil", usresponse.getPerfil());
								usuario.put("idPerfil", usresponse.getIdPerfil());
								usuario.put("cambioPass", usresponse.getPrimerLogueo());
								usuario.put("fechaHora", usresponse.getFechaHora());
								usuario.put("toleranciaVisita", usresponse.getToleranciaVisita());
								usuario.put("token", token);
								
								List<Menu> menus = new ArrayList<Menu>();
								menus.add(new Menu(1,1,"Menu Principal", 1,"Principal"));
								menus.add(new Menu(2,1,"Menu Principal", 2, "Buscar Pdv"));
								menus.add(new Menu(3,2,"Gestión de PDVs", 1, "Crear nuevo pdv"));
								menus.add(new Menu(4,2,"Gestión de PDVs", 2, "Editar pdv"));

								List<Motivos> motivos = usuarioservice.listarMotivos();
								
								Map<String, Object> usuarioresp = new HashMap<String, Object>();
								usuarioresp.put("usuario", usuario);
								usuarioresp.put("menu", menus);
								usuarioresp.put("motivos", motivos);
								//usuarioresp.put("permisos", usuarioservice.getPermisos(usresponse.getId().intValue(),fechahora));
								usuarioresp.put("estadosCom", usuarioservice.getEstadosCom(fechahora));
								
								mapa.put("estado", 1);
								mapa.put("msg", "OK");
								mapa.put("datos", usuarioresp);

							}
						}

					}else{
						mapa.put("estado", 0);
						mapa.put("msg", "La aplicación se encuentra en una versión desactualizada");
						mapa.put("datos", null);
					}
				}else{
					mapa.put("estado", 0);
					mapa.put("msg", "El parametro versión se encuentra vacío");
					mapa.put("datos", null);
				}
			} else {
				mapa.put("estado", 0);
				mapa.put("msg", "Usuario y/o password vacios");
				mapa.put("datos", null);
			}
		}
		return new ResponseEntity<>(mapa, HttpStatus.OK);

	}

	@PostMapping("/permisosDcs")
	public ResponseEntity<?> permisosDcs(HttpServletRequest request,@RequestBody Map<String, Object> datos) {
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		String fechahora = "";

		if(datos.containsKey("fechaHora")) {
			fechahora = datos.get("fechaHora").toString();
		}		
		
		mapa.put("estado", 1);
		mapa.put("msg", "OK");
		mapa.put("datos", usuarioservice.getPermisos(usus.getId().intValue(),fechahora));

		return new ResponseEntity<>(mapa, HttpStatus.OK);
	
	}

	@PostMapping("/recuperarPass")
	public Map<String, Object> recuperarPass(@RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();
		try {
			if (datos.isEmpty()) {
				mapa.put("estado", 0);
				mapa.put("msg", "Error no se han recibido los datos");
				mapa.put("datos", null);
			} else {
				String email = datos.get("email").toString();
				int idus = usuarioservice.validarEmail(email);

				if (idus > 0) {

					int generatedLong = new Random().nextInt();

					if (generatedLong < 0) {
						generatedLong = generatedLong * -1;
					}

					String recover = Functions.getMD5(Integer.toString(generatedLong));

					if (usuarioservice.guardarRecoPass(idus, recover)) {
						String from = "Link de ingreso a la plataforma Movilbox Pos";
						String subject = "Solicitud de restablecimiento de password.";
						String body = "Estimado Usuario,<br><br>"
								+ "Hemos recibido una solicitud para restablecer su password de acceso.<br>"
								+ "Para asignar un nuevo password de acceso, por favor <a href='" + uri
								+ "index.php?rpss=" + recover + "' target='_blank'> hacer click aqui.</a><br><br>"
								+ "Usted tiene hasta las:  para realizar esta operacion.<br><br><strong>Nota:</strong> Esto es un mensaje automatico, favor no responder al mismo."
								+ "<br><br>";

						mailservice.enviarEmail(from, email, subject, body);

						mapa.put("estado", 1);
						mapa.put("msg", "Se ha enviado un mensaje a tu correo electronico");
						mapa.put("datos", null);
					} else {
						mapa.put("estado", 0);
						mapa.put("msg", "Error al tratar de actualizar los datos");
						mapa.put("datos", null);
					}

				} else {
					mapa.put("estado", 0);
					mapa.put("msg", "Error el email ingresado no se encuentra registrado");
					mapa.put("datos", null);
				}
			}

			return mapa;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	@PostMapping("/cambiarPass")
	public ResponseEntity<?> cambiarPass(HttpServletRequest request,@RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}

		if (datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
			mapa.put("datos", null);
		} else {
			if (datos.containsKey("passAnterior") && datos.containsKey("passNuevo")) {
				String passold = datos.get("passAnterior").toString();
				String passnew = datos.get("passNuevo").toString();
				//Usuario idus = Functions.getUsuario();
				int rest = usuarioservice.cambarPass(passold, passnew, usus.getId().intValue());
				if(rest > 0)
				{
					if(rest == 1) {
						mapa.put("estado", 1);
						mapa.put("msg", "La contraseña se ha actuaizado con exito");
						mapa.put("datos", null);
					}
					else {
						mapa.put("estado", 0);
						mapa.put("msg", "Error la contraseña anterior no es valida");
						mapa.put("datos", null);
					}
				}
				else {
					mapa.put("estado", 0);
					mapa.put("msg", "Error al tratar de actualizar los datos");
					mapa.put("datos", null);
				}	

			} else {
				mapa.put("estado", 0);
				mapa.put("msg", "La contraseña anterior y la nueva contraseña estas vacios");
				mapa.put("datos", null);
			}
		}
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}

	@CacheEvict(value = "cachename", allEntries=true)
	@PostMapping("/encuestas")
	public ResponseEntity<?> verEncuesta(HttpServletRequest request,@RequestBody Map<String, Object> encuestas) {

		mapa = new HashMap<String, Object>();
		String idEncuestas = (String) encuestas.get("idEncuestas");
		
		String header = request.getHeader("Authorization");		
		//Usuario usus = Functions.getUsuario();
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		/* Se consulta por los permisos DCS del usuario y se setea una intiable estatica en la clase Function*/
		String permisosRutas = permisosService.permisosRutas(usus.getId());
		Functions.setPermisosRuta(permisosRutas);

		mapa.put("estado", 1);
		mapa.put("msg", "");
		mapa.put("datos",encuestaservice.verEncuestas(usus, idEncuestas));	
		
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@CacheEvict(value = "cachename", allEntries=true)
	@PostMapping("/encuestasOperador")
	public ResponseEntity<?> verEncuestaOperador(HttpServletRequest request,@RequestBody Map<String, Object> encuestas) {

		mapa = new HashMap<String, Object>();
		String idEncuestas = (String) encuestas.get("idEncuestas");
		
		String header = request.getHeader("Authorization");		
		//Usuario usus = Functions.getUsuario();
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		/* Se consulta por los permisos DCS del usuario y se setea una intiable estatica en la clase Function*/
		String permisosRutas = permisosService.permisosRutas(usus.getId());
		Functions.setPermisosRuta(permisosRutas);

		mapa.put("estado", 1);
		mapa.put("msg", "");
		mapa.put("datos",encuestaservice.verEncuestasOperador(usus, idEncuestas));	
		
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@PostMapping("/puntosVisitados")
	public ResponseEntity<?> puntosVisitados(HttpServletRequest request) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		//Usuario usus = Functions.getUsuario();
		if(usus == null) {
			return this.accesoDenegado();
		}
	
		Map<String, Object> listapuntos = usuarioservice.getPuntos(usus.getId().intValue());
		if(listapuntos != null) {
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", listapuntos);
		}
		else {
			mapa.put("estado", 0);
			mapa.put("msg", "Error al tratar de consultar los puntos");
			mapa.put("datos", null);
		}
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@PostMapping("/buscarPuntos")
	public ResponseEntity<?> buscarPuntos(HttpServletRequest request,@RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}
		//Usuario usus = Functions.getUsuario();
		if(datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
			mapa.put("datos", null);
		}
		else {
			String nombre = "";
			int idpos = 0;
			int circuito = 0;
			int ruta = 0;
			int estadocom = 0;
			int pagina = 0;
			int idAgente = 0;
			String documento = "";
			
			
			if(datos.containsKey("nombre")) {
				nombre = datos.get("nombre").toString().trim();
			}
			if(datos.containsKey("idpos")) {
				if(Functions.esNumero(datos.get("idpos").toString())) {
					idpos = Integer.parseInt(datos.get("idpos").toString());
				}
			}
			if(datos.containsKey("circuito")) {
				if(Functions.esNumero(datos.get("circuito").toString())) {
					circuito = Integer.parseInt(datos.get("circuito").toString());
				}
			}
			if(datos.containsKey("ruta")) {
				if(Functions.esNumero(datos.get("ruta").toString())) {
					ruta = Integer.parseInt(datos.get("ruta").toString());
				}
			}		
			if(datos.containsKey("estadocom")) {
				if(Functions.esNumero(datos.get("estadocom").toString())) {
					estadocom = Integer.parseInt(datos.get("estadocom").toString());
				}
			}
			
			if(datos.containsKey("pagina")) {
				if(Functions.esNumero(datos.get("pagina").toString())) {
					pagina = Integer.parseInt(datos.get("pagina").toString());
				}
			}
			
			if(datos.containsKey("agente")) {
				if(Functions.esNumero(datos.get("agente").toString())) {
					idAgente = Integer.parseInt(datos.get("agente").toString());
				}
			}
			if(datos.containsKey("documento")) {
				documento = datos.get("documento").toString().trim();
			}

			/* Se consulta por los permisos DCS del usuario y se setea una intiable estatica en la clase Function*/
			Map<String, Object> permisosDcs = permisosService.buscarNiveles(usus.getId());
			Functions.setPermisosDcs(permisosDcs);
			 
			Map<String,Object> response = puntosservice.buscarPuntos(usus.getId().intValue(),nombre, idpos, circuito, ruta, estadocom, pagina, idAgente, documento);
			/*
			List<Puntos> puntos = (List<Puntos>) response.get("listaPuntos");
			int cantidad = 0;
			if(puntos.size() > 0) {
				cantidad = puntos.size();
			}
			float paginas = 0;
			if(cantidad > 0) {
				paginas = (float)cantidad / 20;
			}
			
			double paginasf = Math.ceil(paginas);
			
			responsep.put("puntos", puntos);*/
			
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", response);
		}
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@PostMapping("/tabsCheckIn")
	public ResponseEntity<?> tabsCheckIn(HttpServletRequest request,@RequestBody Map<String, Object> datos) {
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}

		mapa = new HashMap<String, Object>();
		int idpos = 0;
		int op = 0;
		if(datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
			mapa.put("datos", null);
		}
		else {
			if(datos.containsKey("idPos")) {
				idpos = Integer.parseInt(datos.get("idPos").toString());
			}
			if(datos.containsKey("op")) {
				op = Integer.parseInt(datos.get("op").toString());
			}	
			
			if(idpos <= 0 || op <= 0) {
				mapa.put("estado", 0);
				mapa.put("msg", "Error el idpos y la opcion deben ser mayores a cero");
				mapa.put("datos", null);
			}
			else {
				Map<String,Object> response = new HashMap<String,Object>();
				switch(op) {
					case 1:
						InfoPos punto = tabscheckin.infoPuntos(idpos);
						Map<String,Object> infoFrec = tabscheckin.infoFrecuencia(idpos);
						if(punto != null) {
							response.put("idPos",punto.getIdpos());
							response.put("distribuidor",punto.getDistribuidor());
							response.put("nombre",punto.getNombre());
							response.put("nombrePropietario",punto.getNombrePropietario());
							response.put("circuito",punto.getCircuito());
							response.put("ruta",punto.getRuta());
							response.put("direccion",punto.getDireccion().trim());
							response.put("telefono",punto.getTelefono());
							response.put("telefonoOp",punto.getTelefonoOp());
							response.put("latitud",punto.getLatitud());
							response.put("longitud",punto.getLongitud());
							response.put("fechaVisita",punto.getFechaVisita());
							response.put("efectiva",punto.getEfectiva());
							response.put("proximaVisita",infoFrec.get("proximaVisita"));
							response.put("frecuenciaVisita",infoFrec.get("infoFrecuencia"));
							response.put("categoria",punto.getCategoria());
							response.put("tipologia",punto.getTipologia());
							
							Map<String,Object> infoPos = new HashMap<String,Object>();
							infoPos.put("infoPunto", response);
							
							mapa.put("estado", 1);
							mapa.put("msg", "");
							mapa.put("datos", infoPos);
						}
						else {
							mapa.put("estado", 0);
							mapa.put("msg", "Error al consultar la informacion del punto");
							mapa.put("datos", null);
						}						
						break;
					case 2:
						List<InfoInventario> inventario = tabscheckin.infoInventario(idpos);
						if(inventario != null) {
							response.put("infoInventario",inventario);
							mapa.put("estado", 1);
							mapa.put("msg", "");
							mapa.put("datos", response);
						}
						else {
							mapa.put("estado", 0);
							mapa.put("msg", "Error al consultar el inventario del punto");
							mapa.put("datos", null);
						}
						break;
					case 3:
						List<InfoHistorial> infohisto = tabscheckin.infoHistorial(idpos);
						if(infohisto != null) {
							response.put("historialVisitas",infohisto);
							mapa.put("estado", 1);
							mapa.put("msg", "");
							mapa.put("datos", response);
						}
						else {
							mapa.put("estado", 0);
							mapa.put("msg", "Error al consultar el historial del punto");
							mapa.put("datos", null);
						}
						break;
					case 4:
						List<InfoRotacion> rotacion = tabscheckin.infoRotacion(idpos);
						if(rotacion != null) {
							response.put("infoRotacion",rotacion);
							mapa.put("estado", 1);
							mapa.put("msg", "");
							mapa.put("datos", response);
						}
						else {
							mapa.put("estado", 0);
							mapa.put("msg", "Error al consultar el inventario del punto");
							mapa.put("datos", null);
						}
						break;
					case 5:
						
						List<?> comentarios = new ArrayList<>();
						
						response.put("infoComentarios",comentarios);
						mapa.put("estado", 1);
						mapa.put("msg", "");
						mapa.put("datos", response);
						
						break;
					default:
						
				}
			}
		}
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@SuppressWarnings("unchecked")
	@PostMapping("/guardarVisita")
	public ResponseEntity<?> guardarVisita(HttpServletRequest request,@RequestBody Map<String, Object> datos) {
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		Map<String,Object> response = new HashMap<>();
		ObjectMapper mapper = new ObjectMapper();
		long idPos = 0;
		Boolean efectiva = false;
		long idVisita = 0;
		List<EncuestaRespondida> listaEncuestas = new ArrayList<>();
		List<FormularioDinamico> listaFormularios = new ArrayList<>();
		Map<String, Object> visitas = new HashMap<>();
		Map<String, Object> noEfectiva = new HashMap<>();
		
		try {
		
			if (datos.isEmpty()) {
				mapa.put("estado", 0);
				mapa.put("msg", "Error no se han recibido los datos");
			} else {

				//Usuario usus = Functions.getUsuario();


					long idUsuario = usus.getId();
					int idPerfil = usus.getIdPerfil();

					if (datos.containsKey("visitas")){
						visitas = (Map<String, Object>) datos.get("visitas");
					
						/* Se captura el id del punto de venta */
						idPos = Long.parseLong(visitas.get("idPdv").toString());

						if (datos.containsKey("encuestas")){
							listaEncuestas = mapper.convertValue(
								datos.get("encuestas"), 
								new TypeReference<List<EncuestaRespondida>>(){}
							);
						}
						
						if (datos.containsKey("formulariosDinamicos")){
							listaFormularios = mapper.convertValue(
								datos.get("formulariosDinamicos"), 
								new TypeReference<List<FormularioDinamico>>(){}
							);
						}
						
						if (datos.containsKey("noEfectiva")){
							noEfectiva = (Map<String, Object>) datos.get("noEfectiva");
						}
						
						if(!listaEncuestas.isEmpty()) {
							efectiva = true;
						}
						if(!listaFormularios.isEmpty()) {
							efectiva = true;
						}
						
						DcsPuntos dcsPuntos = puntosservice.buscarDcsPuntos(idPos);
						
						if(dcsPuntos != null) {
						
							long idCircuito = dcsPuntos.getIdCircuito();
							long idRuta = dcsPuntos.getIdRuta();
							long idRegional = dcsPuntos.getIdRegional();
							long idDistribuidor = dcsPuntos.getIdDistribuidor();
							
							visitas.put("idCircuito", idCircuito);
							visitas.put("idRuta", idRuta);
							visitas.put("idRegional", idRegional);
							visitas.put("idDistribuidor", idDistribuidor);
							visitas.put("idUsuario", idUsuario);
							visitas.put("efectiva", efectiva);

							/* Se guarda la vista */
							response = visitaService.guardarVisita(visitas);

							if(response != null){

								int estado = (int) response.get("estado");
								String mensaje = (String) response.get("mensaje");

								if(estado == 1){
									idVisita = (long) response.get("idVisita");

									if(idVisita > 0){

										/* Se guarda los procesos de la visita la encuesta*/
										if(!listaEncuestas.isEmpty()) {
											Map<String, Object> infoEncuesta = encuestaservice.responderEncuesta(listaEncuestas, visitas, idUsuario, idPerfil, idVisita);

											if(infoEncuesta != null){

												int estadoEncuesta = (int) infoEncuesta.get("estado");
												String mensajeEncuesta = (String) infoEncuesta.get("mensaje");

												if(estadoEncuesta == 1){
													mapa.put("estado", 1);
													mapa.put("msg", "");
												}else{
													mapa.put("estado", 0);
													mapa.put("msg", mensajeEncuesta);
													visitaService.reversarVisita(idVisita);
												}

											}else{
												mapa.put("estado", 0);
												mapa.put("msg", "Error al guardar la encuesta");
												visitaService.reversarVisita(idVisita);
											}

										}
										
										//Proceso de formularios dinamicos en la visita
										if(!listaFormularios.isEmpty()) {
											Map<String, Object> infoEncuesta = encuestaservice.responderFormularioDinamico(listaFormularios, visitas, idUsuario, idPerfil, idVisita);

											if(infoEncuesta != null){

												int estadoEncuesta = (int) infoEncuesta.get("estado");
												String mensajeEncuesta = (String) infoEncuesta.get("mensaje");

												if(estadoEncuesta == 1){
													mapa.put("estado", 1);
													mapa.put("msg", "");
												}else{
													mapa.put("estado", 0);
													mapa.put("msg", mensajeEncuesta);
													visitaService.reversarVisita(idVisita);
												}

											}else{
												mapa.put("estado", 0);
												mapa.put("msg", "Error al guardar la encuesta");
												visitaService.reversarVisita(idVisita);
											}

										}

										/* Se guarda los procesos de la visita no efectiva*/
										if(noEfectiva.size() > 0) {
											Map<String, Object> infoVisitaDetalle = visitaService.guardarNoefectiva(noEfectiva, idUsuario, idPos, idVisita);

											if(infoVisitaDetalle != null){

												int estadoVisita = (int) infoVisitaDetalle.get("estado");
												String mensajeVisita = (String) infoVisitaDetalle.get("mensaje");

												if(estadoVisita == 1){
													mapa.put("estado", 1);
													mapa.put("msg", "");
												}else{
													mapa.put("estado", 0);
													mapa.put("msg", mensajeVisita);
													visitaService.reversarVisita(idVisita);
												}

											}else{
												mapa.put("estado", 0);
												mapa.put("msg", "Error al guardar el detalle de la visita");
												visitaService.reversarVisita(idVisita);
											}
										}

									}else{
										mapa.put("estado", 0);
										mapa.put("msg", "Error al guardar la visita");
									}
									
								}else{
									mapa.put("estado", 0);
									mapa.put("msg", mensaje);
								}

							}else{
								mapa.put("estado", 0);
								mapa.put("msg", "Ha ocurrido un error inesperado, por favor ten paciencia que pronto lo solucionaremos");
							}
						
						}else {
							
							mapa.put("estado", 0);
							mapa.put("msg", "El punto de venta no cuenta con permisos en DCS para poder realizar la encuesta");
							
						}
					}else{
						mapa.put("estado", 0);
						mapa.put("msg", "No se encuentra los datos de la visita");
					}
				
				
				
			}

			mapa.put("datos", null);
			
			return new ResponseEntity<>(mapa, HttpStatus.OK);
		
		} catch (Exception e) {
			e.printStackTrace();
			visitaService.reversarVisita(idVisita);

			mapa.put("estado", 0);
			mapa.put("msg", "Error del servidor: " + e.getMessage());
			mapa.put("datos", null);

			return new ResponseEntity<>(mapa, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	
	}

	@PostMapping("/guardarEncuesta")
	public ResponseEntity<?> guardarEncuesta(HttpServletRequest request,@RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		//Usuario usus = Functions.getUsuario();
		Map<String,Object> datosVisita = new HashMap<>();
		ObjectMapper mapper = new ObjectMapper();
		

		long idUsuario = usus.getId();
		int idPerfil = usus.getIdPerfil();

		List<EncuestaRespondida> ListaEncuestas = mapper.convertValue(
			datos.get("encuestas"), 
			new TypeReference<List<EncuestaRespondida>>(){}
		);

		/* Se guarda los procesos de la visita la encuesta*/
		if(!ListaEncuestas.isEmpty()) {
			Map<String, Object> infoEncuesta = encuestaservice.responderEncuesta(ListaEncuestas, datosVisita, idUsuario, idPerfil, 0);

			if(infoEncuesta != null){

				int estadoEncuesta = (int) infoEncuesta.get("estado");
				String mensajeEncuesta = (String) infoEncuesta.get("mensaje");

				if(estadoEncuesta == 1){
					mapa.put("estado", 1);
					mapa.put("msg", "");
				}else{
					mapa.put("estado", 0);
					mapa.put("msg", mensajeEncuesta);
				}

			}else{
				mapa.put("estado", 0);
				mapa.put("msg", "Error al guardar la encuesta");
			}

		}
			
		mapa.put("datos", null);
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@PostMapping("/detalleInventario")
	public ResponseEntity<?> detalleInventario(HttpServletRequest request,@RequestBody Map<String, Object> datos){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		int idPos;
		int idReferencia;
		int pagina;
		if (datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
		} else {
			if(datos.containsKey("idPos") && datos.containsKey("idReferencia") && datos.containsKey("pagina")) {
				idPos = Integer.parseInt(datos.get("idPos").toString());
				idReferencia = Integer.parseInt(datos.get("idReferencia").toString());
				pagina = Integer.parseInt(datos.get("pagina").toString());
			}
			else {
				idPos = 0;
				idReferencia = 0;
				pagina = 0;
			}
			Map<String,Object> response = new HashMap<String,Object>();
			
			List<DetalleInventario> detalle = inventarioService.getDetalleInventario(idPos, idReferencia, pagina);
			
			if(detalle != null) {
				response.put("detalleInventario",detalle);
				mapa.put("estado", 1);
				mapa.put("msg", "");
				mapa.put("datos", response);
			}
			
		}

		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@PostMapping("/infoProducto")
	public ResponseEntity<?> infoProducto(HttpServletRequest request,@RequestBody Map<String, Object> datos){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		String serial;
		if (datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
		} else {
			if(datos.containsKey("serial")) {
				serial = datos.get("serial").toString();
			}
			else {
				mapa.put("estado", 0);
				mapa.put("msg", "Error no se han recibido los datos");
				return new ResponseEntity<>(mapa, HttpStatus.OK);
			}
			Map<String,Object> response = new HashMap<>();
			response = inventarioService.getDetalleInfoProducto(serial);
			
			if (response != null) {
				mapa.put("estado", 1);
				mapa.put("msg", "");
				mapa.put("datos", response);
			}
			else {
				mapa.put("estado", 0);
				mapa.put("msg", "No se encontro información del producto");
				mapa.put("datos", null);
			}
			
			return new ResponseEntity<>(mapa, HttpStatus.OK);	
			
		}
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	
	@PostMapping("/detalleVisita")
	public ResponseEntity<?> detalleVisita(HttpServletRequest request,@RequestBody Map<String, Object> datos){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		int idVisita = 0;
		if (datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
		} else {
			if(datos.containsKey("idVisita")) {
				idVisita = Integer.parseInt(datos.get("idVisita").toString());
			}
			else {
				mapa.put("estado", 0);
				mapa.put("msg", "Error no se han recibido los datos");
				return new ResponseEntity<>(mapa, HttpStatus.OK);
			}
			Map<String,Object> response = new HashMap<>();
			response = visitaService.detalleVisita(idVisita);
			
			if(response != null) {
				mapa.put("estado", 1);
				mapa.put("msg", "");
				mapa.put("datos", response);
			}
			else {
				mapa.put("estado", 0);
				mapa.put("msg", "No se encontro información de la visita");
				mapa.put("datos", null);
			}	
			return new ResponseEntity<>(mapa, HttpStatus.OK);
		}
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@GetMapping("/clasificacionCategorias")
	public ResponseEntity<?> clasificacionCategorias(HttpServletRequest request){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		List<EscalasPdv> response = puntosservice.clasificacionCategorias();
		
		if(response != null) {
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", response);
		}
		else {
			mapa.put("estado", 0);
			mapa.put("msg", "No se encontraron categorias");
			mapa.put("datos", null);
		}	
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}
	
	
	@GetMapping("/departamentos")
	public ResponseEntity<?> departamentos(HttpServletRequest request){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		List<Departamentos> response = puntosservice.getDepartamentos();
		
		if(response != null) {
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", response);
		}
		else {
			mapa.put("estado", 0);
			mapa.put("msg", "No se encontraron Departamentos");
			mapa.put("datos", null);
		}	
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}
	
	
	@GetMapping("/municipios")
	public ResponseEntity<?> municipios(HttpServletRequest request){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		List<Municipios> response = puntosservice.getMunicipios();
		
		if(response != null) {
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", response);
		}
		else {
			mapa.put("estado", 0);
			mapa.put("msg", "No se encontraron Municipios");
			mapa.put("datos", null);
		}	
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}
	
	@GetMapping("/tiposDocumento")
	public ResponseEntity<?> tiposDocumento(HttpServletRequest request){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		List<TiposDocumento> response = puntosservice.getTiposDocumeno();
		
		if(response != null) {
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", response);
		}
		else {
			mapa.put("estado", 0);
			mapa.put("msg", "No se encontraron tipos de documento");
			mapa.put("datos", null);
		}	
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}
	
	@GetMapping("/categorias")
	public ResponseEntity<?> categorias(HttpServletRequest request){
		
		mapa = new HashMap<String, Object>();
		
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		
		if(usus == null) {
			return this.accesoDenegado();
		}
		List<CategoriasPuntos> response = puntosservice.getCategoriasPuntos();
		
		if(response != null) {
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", response);
		}
		else {
			mapa.put("estado", 0);
			mapa.put("msg", "No se encontraron Categorias de puntos");
			mapa.put("datos", null);
		}	
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}

	
	@PostMapping("/buscarCarteraPuntos")
	public ResponseEntity<?> buscarCarteraPuntos(HttpServletRequest request,@RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}
		//Usuario usus = Functions.getUsuario();
		if(datos.isEmpty()) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
			mapa.put("datos", null);
		}
		else {
			String nombre = "";
			int idpos = 0;
			String  documento = "";
			String codigoUnicoTienda = "";
			int idDepartamento = 0;
			int idMunicipio = 0;
			int idRuta = 0;
			int idCircuito = 0;
			int idEstadoComercial = 0;
			int idAgente = 0;

			
			if(datos.containsKey("nombrePdv") && datos.get("nombrePdv") != null) {
				nombre = datos.get("nombrePdv").toString();
			}
			if(datos.containsKey("id")) {
				if(Functions.esNumero(datos.get("id").toString())) {
					idpos = Integer.parseInt(datos.get("id").toString());
				}
			}
			if(datos.containsKey("documento")) {
				documento = datos.get("documento").toString();
			}
			if(datos.containsKey("codigoUnicoTienda")) {
				codigoUnicoTienda = datos.get("codigoUnicoTienda").toString();
			}		
			if(datos.containsKey("idDepartamento")) {
				if(Functions.esNumero(datos.get("idDepartamento").toString())) {
					idDepartamento = Integer.parseInt(datos.get("idDepartamento").toString());
				}
			}
			if(datos.containsKey("idMunicipio")) {
				if(Functions.esNumero(datos.get("idMunicipio").toString())) {
					idMunicipio = Integer.parseInt(datos.get("idMunicipio").toString());
				}
			}
			if(datos.containsKey("idRuta")) {
				if(Functions.esNumero(datos.get("idRuta").toString())) {
					idRuta = Integer.parseInt(datos.get("idRuta").toString());
				}
			}
			if(datos.containsKey("idCircuito")) {
				if(Functions.esNumero(datos.get("idCircuito").toString())) {
					idCircuito = Integer.parseInt(datos.get("idCircuito").toString());
				}
			}
			if(datos.containsKey("idEstadoComercial")) {
				if(Functions.esNumero(datos.get("idEstadoComercial").toString())) {
					idEstadoComercial = Integer.parseInt(datos.get("idEstadoComercial").toString());
				}
			}
			if(datos.containsKey("idAgente")) {
				if(Functions.esNumero(datos.get("idAgente").toString())) {
					idAgente = Integer.parseInt(datos.get("idAgente").toString());
				}
			}
			
			
			/* Se consulta por los permisos DCS del usuario y se setea una intiable estatica en la clase Function*/
			Map<String, Object> permisosDcs = permisosService.buscarNiveles(usus.getId());
			Functions.setPermisosDcs(permisosDcs);
			 
			List<CarteraPuntos> puntos = puntosservice.getCarteraPuntos(nombre,idpos,documento,codigoUnicoTienda,idDepartamento,idMunicipio,idRuta,idCircuito,idEstadoComercial,idAgente);
			
			mapa.put("estado", 1);
			mapa.put("msg", "");
			mapa.put("datos", puntos);
		}
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}
	
	@PostMapping("/crearActualizarPuntos")
	public ResponseEntity<?> crearActualizarPuntos(HttpServletRequest request,@RequestBody @Valid CrearActualizarPuntosPost datos) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}

		// validar si se envia el telefono_op y que sea de 10 caracteres
		if (datos.getTelefonoOp().toString() != "0" && datos.getTelefonoOp().length() != 10) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error al intentar Crear/Actualizar el punto.");
			mapa.put("datos", "El campo teléfono PDV debe ser de 10 digitos.");			
		} else {
			//Usuario usus = Functions.getUsuario();
			String msgAccion = "Creado";
			if(datos.getIdPos() > 0) {
				msgAccion = "Actualizado";
			}					
			Map<String,Object> resultCrear = puntosservice.crearActualizarPuntos(datos, Integer.parseInt(usus.getId().toString()));
			if ((Boolean) resultCrear.get("estado")) {
				mapa.put("estado", 1);
				mapa.put("msg", "El punto ha sido Creado/Actualizado con éxito.");
				mapa.put("datos", null);
			} else {
				mapa.put("estado", 0);
				mapa.put("msg", "Error al intentar Crear/Actualizar el punto.");
				mapa.put("datos", (String) resultCrear.get("msg"));
			}
		}
		
		return new ResponseEntity<>(mapa, HttpStatus.OK);	
	}

	@PostMapping("/camposDinamicosPunto")
	public ResponseEntity<?> camposDinamicosPunto(HttpServletRequest request, @RequestBody Map<String, Object> datos) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");    
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}

		if (datos.isEmpty() || !datos.containsKey("idPos")) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error no se han recibido los datos");
			mapa.put("datos", null);
			return new ResponseEntity<>(mapa, HttpStatus.BAD_REQUEST);
		}

		int idPos = Integer.parseInt(datos.get("idPos").toString());
		
		Map<String,Object> campos = puntosservice.camposDinamicosPunto(idPos);
		if((Boolean) campos.get("estado")){
			mapa.put("estado", 1);
			mapa.put("msg", "OK");
			mapa.put("datos", campos.get("datos"));
		} else {
			mapa.put("estado", 0);
			mapa.put("msg", (String) campos.get("msg"));
			mapa.put("datos", campos.get("datos"));
		}
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}

	@GetMapping("/camposDinamicos")
	public ResponseEntity<?> camposDinamicos(HttpServletRequest request) {

		mapa = new HashMap<String, Object>();
		String header = request.getHeader("Authorization");    
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}
		
		Map<String,Object> campos = puntosservice.camposDinamicos();
		if((Boolean) campos.get("estado")){
			mapa.put("estado", 1);
			mapa.put("msg", "OK");
			mapa.put("datos", campos.get("datos"));
		} else {
			mapa.put("estado", 0);
			mapa.put("msg", (String) campos.get("msg"));
			mapa.put("datos", campos.get("datos"));
		}
		return new ResponseEntity<>(mapa, HttpStatus.OK);
	}

	@PostMapping("/guardarImagenesFrm")
	public ResponseEntity<?> guardarImagenesFrm(HttpServletRequest request,@RequestPart("file") List<MultipartFile> imageFiles, @RequestParam("data") String data) {

		mapa = new HashMap<String, Object>();
		boolean editarNombreImg = false;
		String header = request.getHeader("Authorization");	
		Usuario usus = this.validarToken(header);
		if(usus == null) {
			return this.accesoDenegado();
		}
		try {
			System.out.println(data);
			List<ImgFormularioDinamico> listaImagenes = new ArrayList<>();
			
			listaImagenes = jsonParseService.parseJsonToList(data);
			
			int resp = 0;
			for (MultipartFile imageFile : imageFiles) {
				for (ImgFormularioDinamico formImg : listaImagenes) {
			        if (formImg.getPathFile().equals(imageFile.getOriginalFilename())) {
						resp = awsService.subirArchivo(imageFile);
						if(resp > 0) {
							editarNombreImg = encuestaservice.guardarNombreImg(formImg.getId(), formImg.getPathFile());
							if (!editarNombreImg){
								mapa.put("estado", 0);
								mapa.put("msg", "Error al editar la imagen");
								mapa.put("datos", null);
								return new ResponseEntity<>(mapa, HttpStatus.BAD_REQUEST);
							}
						}
			        }
			        else {
			        	System.out.println("|" + formImg.getPathFile() + " - " + imageFile.getOriginalFilename() + "/n");
			        }
			    }
			}
			if (resp > 0 ) {
				mapa.put("estado", 1);
				mapa.put("msg", "La imagen se a subido con éxito.");
				mapa.put("datos", null);
			}
			else {
				mapa.put("estado", 0);
				mapa.put("msg", "Error al intentar subir la imagen");
				mapa.put("datos", null);
			}	
			return new ResponseEntity<>(mapa, HttpStatus.OK);
		}
		catch(Exception e) {
			mapa.put("estado", 0);
			mapa.put("msg", "Error al intentar subir la imagen: " + e.getMessage());
			mapa.put("datos", null);
			return new ResponseEntity<>(mapa, HttpStatus.BAD_REQUEST);
		}
	}
	
}
