package com.apirestclaro.services;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.serviciologerrores.functions.LogErrores;

@Service
public class AwsServiceImpl implements IAwsService {
	
	
	@Autowired
	private AmazonS3 amazons3;
	
	@Value("${aws.bucket}")
	private String bucketName;
	
	@Value("${aws.folder}")
	private String folder;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;

 	@Override
	public int subirArchivo(MultipartFile file) {
		String tempDir = System.getProperty("java.io.tmpdir");
		// System.out.println("tempDir:" + tempDir);
		File fileName = new File(tempDir, file.getOriginalFilename());	
		// System.out.println("fileName:" + fileName);
		try(FileOutputStream stream = new FileOutputStream(fileName)){
			stream.write(file.getBytes());
			String newFileName =  fileName.getName();	
			PutObjectRequest request = new PutObjectRequest(bucketName, folder + "encuestas/" + newFileName, fileName);
			// System.out.println("s3-key:" + request.getKey());
			PutObjectResult result = amazons3.putObject(request);
			// System.out.println("s3-etag:" + result.getETag());
			return 1;
		}catch(IOException e) {
			LogErrores.escribirLog(e.getMessage(), "", ruta_log);
			return 0; 
		}
	}

}
 