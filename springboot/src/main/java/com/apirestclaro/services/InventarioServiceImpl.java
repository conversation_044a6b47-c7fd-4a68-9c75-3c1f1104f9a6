package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.InventarioDao;
import com.apirestclaro.entitys.DetalleInventario;

@Service
public class InventarioServiceImpl implements IInventarioService {
	
	@Autowired
	InventarioDao detalleInv;

	@Override
	public List<DetalleInventario> getDetalleInventario(int idPos, int idReferencia, int pagina) {
		return detalleInv.getDetalleInventario(idPos, idReferencia,pagina);
	}

	@Override
	public Map<String, Object> getDetalleInfoProducto(String serial) {
		return detalleInv.getDetalleInfoProducto(serial);
	}	
	
	

}
