package com.apirestclaro.services;

import java.util.List;
import java.util.Map;


import com.apirestclaro.entitys.CarteraPuntos;
import com.apirestclaro.entitys.CategoriasPuntos;
import com.apirestclaro.entitys.DcsPuntos;
import com.apirestclaro.entitys.Departamentos;
import com.apirestclaro.entitys.EscalasPdv;
import com.apirestclaro.entitys.Municipios;
import com.apirestclaro.entitys.TiposDocumento;
import com.apirestclaro.pojos.CrearActualizarPuntosPost;



public interface IPuntosService {
	
	public Map<String, Object> buscarPuntos(int idUs,String nombre, int idpos, int circuito, int ruta, int estadocom,int pagina, int idAgente, String documento);
	
	public DcsPuntos buscarDcsPuntos(Long idPos); 
	
	public List<EscalasPdv> clasificacionCategorias();
	
	public List<Departamentos> getDepartamentos();
	
	public List<Municipios> getMunicipios();
	
	public List<TiposDocumento> getTiposDocumeno();
	
	public List<CategoriasPuntos> getCategoriasPuntos();

	public List<CarteraPuntos> getCarteraPuntos(String nombre, int idpos, String documento, String codigoUnicoTienda,
			int idDepartamento, int idMunicipio, int idRuta, int idCircuito, int idEstadoComercial, int idAgente);

	public Map<String, Object> crearActualizarPuntos(CrearActualizarPuntosPost datos, int idUs);

	public Map<String, Object> camposDinamicosPunto(int idPos);
	
	public Map<String, Object> camposDinamicos();

}
