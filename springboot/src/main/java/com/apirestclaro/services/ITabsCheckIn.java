package com.apirestclaro.services;


import java.util.List;
import java.util.Map;

import com.apirestclaro.dao.InfoHistorial;
import com.apirestclaro.entitys.InfoInventario;
import com.apirestclaro.entitys.InfoPos;
import com.apirestclaro.entitys.InfoRotacion;

public interface ITabsCheckIn {
	
	public InfoPos infoPuntos(int idpos);
	
	public Map<String,Object> infoFrecuencia(int idpos);
	
	public List<InfoHistorial> infoHistorial(int idPos);
	
	public List<InfoInventario> infoInventario(int idPos);
	
	public List<InfoRotacion> infoRotacion(int idPos);

}
