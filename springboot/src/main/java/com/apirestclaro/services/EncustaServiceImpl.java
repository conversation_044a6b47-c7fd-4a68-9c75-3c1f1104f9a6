package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.EncuestaDao;
import com.apirestclaro.entitys.Usuario;
import com.apirestclaro.pojos.EncuestaRespondida;
import com.apirestclaro.pojos.FormularioDinamico;

@Service
public class EncustaServiceImpl implements IEncuestaService {

	@Autowired
	private EncuestaDao encuastadao;

	@Override
	public Map<String, Object> verEncuestas(Usuario usus, String idEncuestas) {
		return encuastadao.verEncuestas(usus, idEncuestas);
	}

	@Override
	public Map<String, Object> responderEncuesta(List<EncuestaRespondida> encuestasRespondidas,Map<String,Object> datosVisita, long idUsuario, int idPerfil, long idVisita) {

		return encuastadao.guardarEncuesta(encuestasRespondidas, datosVisita, idUsuario, idPerfil, idVisita);
	}

	@Override
	public Map<String, Object> verEncuestasOperador(Usuario usus, String idEncuestasOperador) {
		
		return encuastadao.verEncuestasOperador(usus, idEncuestasOperador);
	}

	@Override
	public Map<String, Object> responderFormularioDinamico (List<FormularioDinamico> formularioDinamico,
			Map<String, Object> datosVisita, long idUsuario, int idPerfil, long idVisita) {
		//
		return encuastadao.guardarFormularioDinamico(formularioDinamico, datosVisita, idUsuario, idPerfil, idVisita);
	}

	@Override
	public boolean guardarNombreImg(long idApp, String nombreImagen) {
		return encuastadao.guardarNombreImg(idApp, nombreImagen);
	}
	
}
