package com.apirestclaro.services;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.PermisosDao;

@Service
public class PermisosService implements IPermisosService {

    @Autowired
    private PermisosDao permisosDao;

    @Override
    public Map<String, Object> buscarNiveles(long idUsuario) {
        return permisosDao.buscarNiveles(idUsuario);
    }

    @Override
    public String permisosRutas(long idUsuario) {
        return permisosDao.retornarRutas(idUsuario);
    }

}