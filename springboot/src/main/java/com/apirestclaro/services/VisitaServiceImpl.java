package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.VisitaDao;
import com.apirestclaro.pojos.NoEfectiva;

@Service
public class VisitaServiceImpl implements IVisitaService {
	
    @Autowired
    private VisitaDao visitaDao;

	@Override
	public Map<String, Object> guardarVisita(Map<String, Object> datosVisita) {
		
		return visitaDao.guardarVisita(datosVisita);
	}

	@Override
	public Map<String, Object> guardarNoefectiva(Map<String, Object> noEfectiva, long idUsuario, long idPos,
			long idVisita) {

		return visitaDao.guardarVisitaNoEfectiva(noEfectiva, idUsuario, idPos, idVisita);
	}

	@Override
	public void reversarVisita(long idVisita) {
		visitaDao.reversarVisita(idVisita);
	}

	@Override
	public Map<String, Object> detalleVisita(int idVisita) {
		return visitaDao.detalleVisita(idVisita);
	}
	
}
