package com.apirestclaro.services;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Service;

import com.apirestclaro.pojos.ImgFormularioDinamico;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class JsonParserService {
	
	private final ObjectMapper objectMapper;

    public JsonParserService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public List<ImgFormularioDinamico> parseJsonToList(String json) throws IOException {
        return objectMapper.readValue(json, new TypeReference<List<ImgFormularioDinamico>>() {});
    }
}
