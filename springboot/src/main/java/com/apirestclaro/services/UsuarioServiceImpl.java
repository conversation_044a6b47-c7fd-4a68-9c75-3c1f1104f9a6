package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.UsuarioDao;
import com.apirestclaro.entitys.EstadosCom;
import com.apirestclaro.entitys.Motivos;
import com.apirestclaro.entitys.Usuario;

@Service
public class UsuarioServiceImpl implements IUsuarioService {

	@Autowired
	private UsuarioDao usuariodao;

	@Override
	public Map<String, Object> getUsuario(String username, String pass) {
		return usuariodao.finByUsername(username, pass);
	}

	@Override
	public List<?> getAllUsuarios() {
		return usuariodao.findAll();
	}

	@Override
	public String generarToken(Usuario us, String tokenNotificacion, String versionName, int versionCode,Map<String, Object> datos) {
		return usuariodao.generarToken(us, tokenNotificacion, versionName, versionCode,datos);
	}

	@Override
	public Usuario validarToken(String token) {
		return usuariodao.validarToken(token);
	}

	@Override
	public int validarEmail(String email) {
		return usuariodao.validarEmail(email);
	}

	@Override
	public Boolean guardarRecoPass(int idus, String recover) {
		return usuariodao.guardarRecoPass(idus, recover);
	}

	@Override
	public int cambarPass(String passold, String passnew, int idus) {
		return usuariodao.cambiarPass(passold, passnew, idus);
	}

	@Override
	public Map<String, Object> getPermisos(int idUs, String fechahora) {
		return usuariodao.getPermisos(idUs, fechahora);
	}

	@Override
	public Map<String, Object> getPuntos(int idUs) {
		return usuariodao.getPuntos(idUs);
	}

	@Override
	public List<EstadosCom> getEstadosCom(String fecha) {
		return usuariodao.estadosComerciales(fecha);
	}

	@Override
	public List<Motivos> listarMotivos() {
		return usuariodao.listarMotivos();
	}

}
