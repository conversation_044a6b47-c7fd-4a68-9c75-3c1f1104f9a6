package com.apirestclaro.services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.ConfigEmailDao;
import com.apirestclaro.entitys.ConfigEmail;

@Service
public class ConfigEmailServiceImpl implements IConfigEmailService {

	
	@Autowired
	private ConfigEmailDao emaildao; 
	
	@Override
	public ConfigEmail consultarConfig() {
	   return emaildao.consultarConfig();
	}

}
