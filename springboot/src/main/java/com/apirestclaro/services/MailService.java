package com.apirestclaro.services;

import java.util.Properties;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import com.apirestclaro.entitys.ConfigEmail;

@Service
public class MailService {

	@Autowired
	private IConfigEmailService configemailservice;
	
	private Properties mailProperties = new Properties();
	
	
	private JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
	//private Properties mailProperties = new Properties();
	 
	
	public void enviarEmail (String from,String to, String subject, String body) throws MessagingException {
		
		ConfigEmail configuracion; 
		configuracion = configemailservice.consultarConfig();
		mailProperties.put("mail.smtp.auth",true);
		mailProperties.put("mail.smtp.starttls.enable",false);
		mailProperties.put("mail.smtp.quitwait",false);
		mailProperties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        mailProperties.put("mail.smtp.socketFactory.fallback", false);
		
        javaMailSender.setJavaMailProperties(mailProperties);
		javaMailSender.setHost(configuracion.getHost());
		javaMailSender.setPort(Integer.parseInt(configuracion.getPort()));
		javaMailSender.setUsername(configuracion.getUser());
		javaMailSender.setPassword(configuracion.getPass());
		
		MimeMessage message = javaMailSender.createMimeMessage();
		
		MimeMessageHelper helper = new MimeMessageHelper(message, true);
		helper.setFrom(from + " <"+ configuracion.getUser() + ">");
		helper.setTo(to);
		helper.setSubject(subject);
		helper.setText(body,true);
		
		/*SimpleMailMessage mail = new SimpleMailMessage(); 
		
		mail.setFrom(configuracion.getUser());
		mail.setTo(to);
		mail.setSubject(subject);
		mail.setText(body);*/
		
		javaMailSender.send(message);	
	}
}
