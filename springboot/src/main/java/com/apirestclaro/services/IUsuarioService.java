package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import com.apirestclaro.entitys.Departamentos;
import com.apirestclaro.entitys.EstadosCom;
import com.apirestclaro.entitys.Motivos;
import com.apirestclaro.entitys.Municipios;
import com.apirestclaro.entitys.Usuario;

public interface IUsuarioService {
	
	public Map<String,Object> getUsuario(String username,String pass);
	
	public List<?> getAllUsuarios();
	
	public String generarToken(Usuario us, String tokenNotificacion, String versionName, int versionCode,Map<String, Object> datos);
	
	public Usuario validarToken(String token);
	
	public int validarEmail (String email);
	
	public Boolean guardarRecoPass(int idus,String recover);
	
	public int cambarPass(String passold, String passnew, int idus);
	
	public Map<String,Object> getPermisos(int idUs, String fechahora);
	
	public Map<String, Object> getPuntos(int idUs);
	
	public List<EstadosCom> getEstadosCom (String fecha);

	public List<Motivos> listarMotivos();
	

}
