package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import com.apirestclaro.entitys.Usuario;
import com.apirestclaro.pojos.EncuestaRespondida;
import com.apirestclaro.pojos.FormularioDinamico;


public interface IEncuestaService {
	
	public Map<String, Object>  verEncuestas(Usuario usus, String idEncuestas);
	
	public Map<String, Object>  verEncuestasOperador(Usuario usus, String idEncuestasOperador);

	public Map<String, Object>  responderEncuesta(List<EncuestaRespondida> encuestasRespondidas,Map<String,Object> datosVisita, long idUsuario, int idPerfil, long idVisita);

	public Map<String, Object> responderFormularioDinamico(List<FormularioDinamico> formularioDinamico,
			Map<String, Object> datosVisita, long idUsuario, int idPerfil, long idVisita);
	
	public boolean guardarNombreImg(long idApp, String nombreImagen);

}
