package com.apirestclaro.services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.VersionDao;

@Service
public class VersionService implements IVersionService{

    @Autowired
    private VersionDao versionDao;

    @Override
    public boolean validarVersion(String versionName, int versionCode) {
        return versionDao.validarVersion(versionName, versionCode);
    }

}