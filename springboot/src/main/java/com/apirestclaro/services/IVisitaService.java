package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import com.apirestclaro.pojos.NoEfectiva;

public interface IVisitaService {
	
	public Map<String, Object> guardarVisita(Map<String, Object> datosVisita);

	public Map<String, Object> guardarNoefectiva(Map<String, Object> noEfectiva, long idUsuario, long idPos, long idVisita);

	public void reversarVisita(long idVisita);
	
	public Map<String, Object> detalleVisita(int idVisita);

}
