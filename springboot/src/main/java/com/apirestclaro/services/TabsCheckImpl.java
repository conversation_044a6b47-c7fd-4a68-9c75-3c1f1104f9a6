package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.InfoHistorial;
import com.apirestclaro.dao.TabsCheckInDao;
import com.apirestclaro.entitys.InfoInventario;
import com.apirestclaro.entitys.InfoPos;
import com.apirestclaro.entitys.InfoRotacion;

@Service
public class TabsCheckImpl implements ITabsCheckIn {

	@Autowired
	TabsCheckInDao tabscheckin;
	
	@Override
	public InfoPos infoPuntos(int idpos) {
		return tabscheckin.infoPuntos(idpos);
	}

	@Override
	public Map<String, Object> infoFrecuencia(int idpos) {
		return tabscheckin.infoFrecuencia(idpos);
	}

	@Override
	public List<InfoHistorial> infoHistorial(int idPos) {
		return tabscheckin.infoHistorial(idPos);
	}

	@Override
	public List<InfoInventario> infoInventario(int idPos) {
		return tabscheckin.infoInventario(idPos);
	}

	@Override
	public List<InfoRotacion> infoRotacion(int idPos) {
		return tabscheckin.infoRotacion(idPos);
	}
}
