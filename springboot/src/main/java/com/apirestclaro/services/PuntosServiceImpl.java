package com.apirestclaro.services;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.apirestclaro.dao.PuntosDao;
import com.apirestclaro.entitys.CarteraPuntos;
import com.apirestclaro.entitys.CategoriasPuntos;
import com.apirestclaro.entitys.DcsPuntos;
import com.apirestclaro.entitys.Departamentos;
import com.apirestclaro.entitys.EscalasPdv;
import com.apirestclaro.entitys.Municipios;
import com.apirestclaro.entitys.TiposDocumento;
import com.apirestclaro.pojos.CrearActualizarPuntosPost;

@Service
public class PuntosServiceImpl implements IPuntosService {

	@Autowired
	private PuntosDao puntosdao;
	
	@Override
	public Map<String, Object> buscarPuntos(int idUs, String nombre, int idPos, int circuito, int ruta, int estadocom, int pagina, int idAgente, String documento) {
		return puntosdao.buscarPuntos(idUs, nombre, idPos, circuito, ruta, estadocom,pagina,idAgente, documento);
	}

	@Override
	public DcsPuntos buscarDcsPuntos(Long idPos) {
		return puntosdao.buscarDcsPuntos(idPos);
	}

	@Override
	public List<EscalasPdv>clasificacionCategorias() {
	
		return puntosdao.clasificacionCategorias();
	}

	@Override
	public List<Departamentos> getDepartamentos() {
		
		return puntosdao.getDepartamentos();
	}

	@Override
	public List<Municipios> getMunicipios() {
		return puntosdao.getMunicipios();
	}

	@Override
	public List<TiposDocumento> getTiposDocumeno() {
		return puntosdao.getTiposDocumento();
	}

	@Override
	public List<CategoriasPuntos> getCategoriasPuntos() {
		return puntosdao.getCategoriasPuntos();
	}
	
	@Override
	public List<CarteraPuntos> getCarteraPuntos(String nombre,int idpos,String documento,String codigoUnicoTienda,int idDepartamento,int idMunicipio,int idRuta,int idCircuito,int idEstadoComercial, int idAgente) {
		return puntosdao.getCarteraPuntos(nombre,idpos,documento,codigoUnicoTienda,idDepartamento,idMunicipio,idRuta,idCircuito,idEstadoComercial,idAgente);
	}

	@Override
	public Map<String, Object> crearActualizarPuntos(CrearActualizarPuntosPost datos, int idUs) {
		return puntosdao.crearActualizarPuntos(datos,idUs);
	}

	@Override
	public Map<String, Object> camposDinamicosPunto(int idPos) {
		return puntosdao.camposDinamicosPunto(idPos);
	}

	@Override
	public Map<String, Object> camposDinamicos() {
		return puntosdao.camposDinamicos();
	}

}
