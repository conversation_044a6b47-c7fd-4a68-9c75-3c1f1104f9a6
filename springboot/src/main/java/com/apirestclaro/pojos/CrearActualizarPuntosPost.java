package com.apirestclaro.pojos;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

public class CrearActualizarPuntosPost {
	
    @NotBlank(message = "El campo 'nombrePdv' no puede estar vacío")
    @NotNull(message = "El campo 'nombrePdv' no puede ser nulo")
    private String nombrePdv;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idPos' debe ser un número entero")
    private int idPos;

    @NotBlank(message = "El campo 'nombreCliente' no puede estar vacío")
    @NotNull(message = "El campo 'nombreCliente' no puede ser nulo")
    private String nombreCliente;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'tipoDocumento' debe ser un número entero")
    @Positive(message = "El campo 'tipoDocumento' debe ser mayor que cero")
    private int tipoDocumento;

    @NotBlank(message = "El campo 'numeroDocumento' no puede estar vacío")
    @NotNull(message = "El campo 'numeroDocumento' no puede ser nulo")
    private String numeroDocumento;

    @NotBlank(message = "El campo 'correo' no puede estar vacío")
    @NotNull(message = "El campo 'correo' no puede ser nulo")
    private String correo;

    @NotBlank(message = "El campo 'telefonoFijo' no puede estar vacío")
    @NotNull(message = "El campo 'telefonoFijo' no puede ser nulo")
    private String telefonoFijo;

    @NotBlank(message = "El campo 'celular' no puede estar vacío")
    @NotNull(message = "El campo 'celular' no puede ser nulo")
    private String celular;

    private String telefonoOp;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idCategoria' debe ser un número entero")
    @Positive(message = "El campo 'idCategoria' debe ser mayor que cero")
    private int idCategoria;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idCategoriaClasificacion' debe ser un número entero")
    @Positive(message = "El campo 'idCategoriaClasificacion' debe ser mayor que cero")
    private int idCategoriaClasificacion;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idDepartamento' debe ser un número entero")
    @Positive(message = "El campo 'idDepartamento' debe ser mayor que cero")
    private int idDepartamento;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idMunicipio' debe ser un número entero")
    @Positive(message = "El campo 'idMunicipio' debe ser mayor que cero")
    private int idMunicipio;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idCircuito' debe ser un número entero")
    @Positive(message = "El campo 'idCircuito' debe ser mayor que cero")
    private int idCircuito;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idRuta' debe ser un número entero")
    @Positive(message = "El campo 'idRuta' debe ser mayor que cero")
    private int idRuta;

    @Digits(integer = Integer.MAX_VALUE, fraction = 0, message = "El campo 'idEstadoComercial' debe ser un número entero")
    @Positive(message = "El campo 'idEstadoComercial' debe ser mayor que cero")
    private int idEstadoComercial;

    @NotBlank(message = "El campo 'barrio' no puede estar vacío")
    @NotNull(message = "El campo 'barrio' no puede ser nulo")
    private String barrio;

    @NotBlank(message = "El campo 'direccion' no puede estar vacío")
    @NotNull(message = "El campo 'direccion' no puede ser nulo")
    private String direccion;

    @DecimalMin(value = "-90.0", inclusive = false, message = "El campo 'latitud' debe ser un número mayor que -90")
    @DecimalMax(value = "90.0", inclusive = false, message = "El campo 'latitud' debe ser un número menor que 90")
    private double latitud;

    @DecimalMin(value = "-180.0", inclusive = false, message = "El campo 'longitud' debe ser un número mayor que -180")
    @DecimalMax(value = "180.0", inclusive = false, message = "El campo 'longitud' debe ser un número menor que 180")
    private double longitud;

	private List<CamposDinamicosPost> camposDinamicos;
	

	public String getNombrePdv() {
		return nombrePdv;
	}

	public void setNombrePdv(String nombrePdv) {
		this.nombrePdv = nombrePdv;
	}

	public int getIdPos() {
		return idPos;
	}

	public void setIdPos(int idPos) {
		this.idPos = idPos;
	}

	public String getNombreCliente() {
		return nombreCliente;
	}

	public void setNombreCliente(String nombreCliente) {
		this.nombreCliente = nombreCliente;
	}

	public int getTipoDocumento() {
		return tipoDocumento;
	}

	public void setTipoDocumento(int tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}

	public String getNumeroDocumento() {
		return numeroDocumento;
	}

	public void setNumeroDocumento(String numeroDocumento) {
		this.numeroDocumento = numeroDocumento;
	}

	public String getCorreo() {
		return correo;
	}

	public void setCorreo(String correo) {
		this.correo = correo;
	}

	public String getTelefonoFijo() {
		return telefonoFijo;
	}

	public void setTelefonoFijo(String telefonoFijo) {
		this.telefonoFijo = telefonoFijo;
	}

	public String getCelular() {
		return celular;
	}

	public void setCelular(String celular) {
		this.celular = celular;
	}

	public String getTelefonoOp() {
		return telefonoOp != null ? telefonoOp : "0";
	}

	public void setTelefonoOp(String telefonoOp) {
		this.telefonoOp = telefonoOp;
	}

	public int getIdCategoria() {
		return idCategoria;
	}

	public void setIdCategoria(int idCategoria) {
		this.idCategoria = idCategoria;
	}

	public int getIdCategoriaClasificacion() {
		return idCategoriaClasificacion;
	}

	public void setIdCategoriaClasificacion(int idCategoriaClasificacion) {
		this.idCategoriaClasificacion = idCategoriaClasificacion;
	}

	public int getIdDepartamento() {
		return idDepartamento;
	}

	public void setIdDepartamento(int idDepartamento) {
		this.idDepartamento = idDepartamento;
	}

	public int getIdMunicipio() {
		return idMunicipio;
	}

	public void setIdMunicipio(int idMunicipio) {
		this.idMunicipio = idMunicipio;
	}

	public int getIdCircuito() {
		return idCircuito;
	}

	public void setIdCircuito(int idCircuito) {
		this.idCircuito = idCircuito;
	}

	public int getIdRuta() {
		return idRuta;
	}

	public void setIdRuta(int idRuta) {
		this.idRuta = idRuta;
	}

	public int getIdEstadoComercial() {
		return idEstadoComercial;
	}

	public void setIdEstadoComercial(int idEstadoComercial) {
		this.idEstadoComercial = idEstadoComercial;
	}

	public String getBarrio() {
		return barrio;
	}

	public void setBarrio(String barrio) {
		this.barrio = barrio;
	}

	public String getDireccion() {
		return direccion;
	}

	public void setDireccion(String direccion) {
		this.direccion = direccion;
	}

	public double getLatitud() {
		return latitud;
	}

	public void setLatitud(double latitud) {
		this.latitud = latitud;
	}

	public double getLongitud() {
		return longitud;
	}

	public void setLongitud(double longitud) {
		this.longitud = longitud;
	}
    
	public List<CamposDinamicosPost> getCamposDinamicos() {
		return camposDinamicos != null ? camposDinamicos : null;
	}

	public void setCamposDinamicos(List<CamposDinamicosPost> camposDinamicos) {
		this.camposDinamicos = camposDinamicos;
	}

}
