package com.apirestclaro.dao;

import javax.persistence.Query;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;

import com.serviciologerrores.functions.LogErrores;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

@Repository
public class VersionDao{
	
	@Value("${operador.bd}")
	private String bd;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;
	

    @PersistenceContext
	private EntityManager em;

	private Query nativeQuery;

    @SuppressWarnings("unchecked")
    public boolean validarVersion(String versionName, int versionCode){
        String query = null;

        try {

            query = "SELECT * FROM " + bd + ".version_app_operador WHERE version_name = '" + versionName + "' AND version_code = " + versionCode;
            nativeQuery = em.createNativeQuery(query);
            List<Object[]> results = (List<Object[]>) nativeQuery.getResultList();
            
            if(!results.isEmpty()){
                return true;
            }else{
            	System.out.println("No valido la version");
                return false;
            }
            
        }  catch (NoResultException e) {
			e.printStackTrace();
        	return false;
		} catch (Exception e2) {
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			e2.printStackTrace();
			return false;
		}
    }

}