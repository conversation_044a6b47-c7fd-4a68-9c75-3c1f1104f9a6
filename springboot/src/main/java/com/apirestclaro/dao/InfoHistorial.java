package com.apirestclaro.dao;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class InfoHistorial {
	
	
	@Column(name = "fecha")	
    private String fecha;
	
	@Column(name = "hora")	
    private String hora;
	
	@Column(name = "usuario")	
    private String usuario;
	
	@Column(name = "extraruta")	
    private int extraruta;
	
	@Column(name = "venta")	
    private int venta;
	
	@Id
	@Column(name = "id_check")	
    private int id_check;
	

    public String getFecha() {
        return fecha;
    }

    public void setFecha(String fecha) {
        this.fecha = fecha;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public int getExtraruta() {
        return extraruta;
    }

    public void setExtraruta(int extraruta) {
        this.extraruta = extraruta;
    }

    public int getVenta() {
        return venta;
    }

    public void setVenta(int venta) {
        this.venta = venta;
    }

    public int getId_check() {
        return id_check;
    }

    public void setId_check(int id_check) {
        this.id_check = id_check;
    }
}