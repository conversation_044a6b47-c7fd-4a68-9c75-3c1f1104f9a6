package com.apirestclaro.dao;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.apirestclaro.entitys.Frecuencia;
import com.apirestclaro.entitys.InfoInventario;
import com.apirestclaro.entitys.InfoPos;
import com.apirestclaro.entitys.InfoRotacion;
import com.apirestclaro.entitys.Puntos;
import com.apirestclaro.functions.Functions;
import com.serviciologerrores.functions.LogErrores;

@Repository
public class TabsCheckInDao {
	
	@Value("${operador.bd}")
	private String bd;
	
	@Value("${operador.bd_pos}")
	private String bd_pos;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;

	@Value("${operador.coid}")
	private int coid;

	@PersistenceContext
	private EntityManager em;

	private Query nativeQuery;

	public InfoPos infoPuntos(int idPos) {
		String query = "";
		try {
			query = "SELECT puntos.idpos,puntos.razon as nombre,puntos.nombre_cli as nombre_propietario,cir.descripcion as circuito,rut.descripcion as ruta,"
					+ "puntos.detalle_direccion as direccion,puntos.tel as telefono,puntos.tel_op,IFNULL(dis.nombre,'N/A') as distribuidor, IF((SELECT fecha FROM  " 
					+ bd_pos + ".visitas_operador WHERE punto = puntos.idpos ORDER BY id DESC LIMIT 1) IS NULL, '', (SELECT fecha FROM  " 
					+ bd_pos + ".visitas_operador WHERE punto = puntos.idpos ORDER BY id DESC LIMIT 1)) AS fecha_visita, IF((SELECT efectiva FROM  " 
					+ bd_pos + ".visitas_operador WHERE punto = puntos.idpos ORDER BY id DESC LIMIT 1) IS NULL, 0, (SELECT efectiva FROM  " 
					+ bd_pos + ".visitas_operador WHERE punto = puntos.idpos ORDER BY id DESC LIMIT 1)) AS efectiva,"
					+ "puntos.latitud as latitud,puntos.longitud as longitud, IFNULL(cat.nombre,'N/A') AS categoria, IFNULL(cp.nombre,'N/A') AS tipologia "
					+ "FROM " + bd_pos + ".puntos as puntos "
					+ "left join  " + bd + ".territorios as cir on (cir.id = puntos.territorio) "
					+ "left join  " + bd + ".territorios_distribuidor as terdis on (terdis.id_territorio = puntos.territorio) "
					+ "left join  " + bd + ".distribuidores as dis on (dis.id = terdis.id_distri)"
					+ "left join  " + bd + ".zonas as rut on (rut.id = puntos.zona) "
					+ "left join  " + bd_pos + ".escalas__pdv as cat on (cat.id = puntos.id_escala) "
					+ "left join  " + bd_pos + ".categorias_puntos as cp on (cp.id = puntos.categoria) "
					+ " where puntos.idpos = " + idPos;

			nativeQuery = em.createNativeQuery(query, InfoPos.class);

			return (InfoPos) nativeQuery.getSingleResult();

		} catch (Exception ex) {
			LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
			return null;
		}
	}

	public Map<String, Object> infoFrecuencia(int idPos) {
		String query = "";
		String proxima_vis = "";
		String proximaFecha = "";
		String infoFrecuencia = "";
		String diasSemana = Functions.diaSemana();
		int contdias = 0;
		Map<String, Object> responese = new HashMap<String, Object>();
		try {
			query = "select zona from  " + bd_pos + ".puntos where idpos = " + idPos;
			nativeQuery = em.createNativeQuery(query);
			int zona = (int) nativeQuery.getSingleResult();
			if (zona > 0) {
				query = "select lunes,martes,miercoles,jueves,viernes,sabado,domingo from frecuencia_zonas where id_zona = "
						+ zona
						+ " and perfil = 1 and (lunes > 0 or martes > 0 or miercoles > 0 or jueves > 0 or viernes > 0 or sabado > 0 or domingo > 0)";

				nativeQuery = em.createNativeQuery(query);
				List<Object[]> results2 = nativeQuery.getResultList();
				if (!results2.isEmpty()) {
					if ((int) results2.get(0)[0] == 1) {
						infoFrecuencia += "Lunes,";
					}
					if ((int) results2.get(0)[1] == 1) {
						infoFrecuencia += "Martes,";
					}
					if ((int) results2.get(0)[2] == 1) {
						infoFrecuencia += "Miercoles,";
					}
					if ((int) results2.get(0)[3] == 1) {
						infoFrecuencia += "Jueves,";
					}
					if ((int) results2.get(0)[4] == 1) {
						infoFrecuencia += "Viernes,";
					}
					if ((int) results2.get(0)[5] == 1) {
						infoFrecuencia += "Sabado,";
					}
					if ((int) results2.get(0)[6] == 1) {
						infoFrecuencia += "Domingo,";
					}

					if (infoFrecuencia.length() > 0) {
						infoFrecuencia = infoFrecuencia.substring(0, infoFrecuencia.length() - 1);
					}

					if (infoFrecuencia.contains(diasSemana)) {
						proxima_vis = "Hoy, ";
					}

					String[] semana = { "Lunes", "Martes", "Miercoles", "Jueves", "Viernes", "Sabado", "Domingo" };

					String[] vector = infoFrecuencia.split(",");
					int indice = Arrays.asList(semana).indexOf(diasSemana);
					int indice2 = 0;
					if (vector.length > 0) {
						for (String dia : vector) {
							indice2 = Arrays.asList(semana).indexOf(dia);
							if (proxima_vis.equals("")) {
								if (indice2 < indice) {
									indice += 1;
									indice2 += 1;
									contdias = (7 - indice) + indice2;
								} else {
									contdias = indice2 - indice;
									break;
								}
							} else {
								if (indice2 > indice) {
									contdias = indice2 - indice;
									break;
								}
							}
						}
					}

					if (contdias > 0) {
						Calendar fecha = Calendar.getInstance();
						fecha.add(Calendar.DAY_OF_YEAR, contdias);
						proximaFecha = Functions.fechaEspanol(fecha.getTime());
					}
					proxima_vis += proximaFecha;

				} else {
					query = "select id,if(fecha = curdate(),'Hoy',DATE_FORMAT(fecha,'%d/%m/%Y')) as proxvisita,if(tipo_fre = 2,'Quincenal','Mensual') as tipofrec"
							+ " from planificacion_visitas where ruta = " + zona
							+ " and fecha >= curdate() order by fecha asc limit 2";

					nativeQuery = em.createNativeQuery(query, Frecuencia.class);
					List<Frecuencia> frecuencias = new ArrayList<Frecuencia>();
					frecuencias.addAll(nativeQuery.getResultList());

					if (frecuencias.size() > 0) {
						SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
						Date fechaDate = null;
						for (Frecuencia fec : frecuencias) {
							infoFrecuencia = fec.getTipoFrec();
							if (fec.getProxVisita().equals("Hoy")) {
								proxima_vis += fec.getProxVisita() + ", ";
							} else {
								fechaDate = formato.parse(fec.getProxVisita());
								proxima_vis += Functions.fechaEspanol(fechaDate);
								break;
							}
						}
					}
				}

				responese.put("proximaVisita", proxima_vis);
				responese.put("infoFrecuencia", infoFrecuencia);
				return responese;

			} else {
				return null;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
			return null;
		}
	}

	public List<InfoHistorial> infoHistorial(int idPos) {
		String query = "";
		try {
			query = "select limit_visitas_tab from configuracion_general where id = 1";
			nativeQuery = em.createNativeQuery(query);
			int limite = (int) nativeQuery.getSingleResult();
			if (limite <= 0) {
				limite = 5;
			}
			
			// se concatena un digito al id de la visita para que indique el tipo de la visita, esto para no realizar cambios en la estructura del servicio y tener que hacer release de la app
			// se debe cambiar este comportamiento cuando se vaya a hacer un nuevo release de la app
			query = "SELECT fecha,hora,UPPER(usuario) as usuario,extraruta,venta,id_check FROM "
					+ "( SELECT vis.fecha, vis.hora, us.nombre_usuario COLLATE latin1_spanish_ci as usuario, extraruta, venta, CONCAT('1',vis.id) AS id_check "
					+ "FROM " + bd_pos + ".visitas__check AS vis "
					+ "LEFT JOIN " + bd + ".usuarios_distri AS us ON (us.id_usuario = vis.usuario AND us.id_distri = vis.id_distri) "
					+ "WHERE vis.punto = " + idPos
					+ " UNION ALL "
					+ "SELECT vis.fecha, vis.hora, CONCAT(us.nombre,' ',us.apellido) COLLATE latin1_spanish_ci as usuario, extra_ruta as extraruta, 0 AS venta, CONCAT(2,vis.id) AS id_check "
					+ "FROM " + bd_pos + ".visitas_operador AS vis "
					+ "LEFT JOIN " + bd + ".usuarios AS us ON (us.id = vis.usuario) "
					+ "WHERE vis.punto = "+ idPos
					+ ") AS visitas "
					+ "ORDER BY fecha DESC, hora DESC LIMIT " + limite;

			//nativeQuery = em.createNativeQuery(query);
			nativeQuery = em.createNativeQuery(query, InfoHistorial.class);
			//List<Object[]> results = em.createNativeQuery(query).getResultList();
			List<InfoHistorial> historial = new ArrayList<InfoHistorial>();
			historial.addAll(nativeQuery.getResultList());
			/*for (Object[] row : results) {
				InfoHistorial info = new InfoHistorial(
						row[0].toString(), // fecha
						row[1].toString(), // hora
						row[2].toString(), // usuario
						Integer.parseInt(row[3].toString()), // extraruta
						Integer.parseInt(row[4].toString()), // venta
						Integer.parseInt(row[5].toString()) // id_check
				);
				historial.add(info);
			}*/
			/*
			 * if (historial.size() > 0) { return historial; } else { return null; }
			 */
			return historial;
		} catch (Exception ex) {
			LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
			return null;
		}
	}

	public List<InfoInventario> infoInventario(int idPos) {
		String query = "";
		try {

			query = "select ref.id as id_referencia,count(inv.id) as cant,ref.producto as referencia, 1 as tipo from inventario__punto as inv"
					+ " left join referencias as ref on (ref.id = inv.id_referencia)" + " where inv.id_pos = " + idPos
					+ " and inv.activo = 0 group by inv.id_referencia";

			nativeQuery = em.createNativeQuery(query, InfoInventario.class);
			List<InfoInventario> inventario = new ArrayList<InfoInventario>();
			inventario.addAll(nativeQuery.getResultList());

			/*
			 * if (inventario.size() > 0) { return inventario; } else { return null; }
			 */
			return inventario;
		} catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<InfoRotacion> infoRotacion(int idPos) {
		String query = "";
		String fechaIni = "";
		try {
			Calendar now = Calendar.getInstance();
			now.add(Calendar.MONTH, -2);
			now.set(Calendar.DAY_OF_MONTH, 1);  
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); 
			fechaIni = format.format(now.getTime());

			query = "select count(id) as colocadas,sum(if(activo=1,1,0)) as activas,sum(if(activo=2,1,0)) as bajas,DATE_FORMAT(fecha_venta,'%m-%Y') as mes"
					+ " from inventario__punto where id_pos = " + idPos + " and fecha_venta >= '" + fechaIni
					+ "' and fecha_venta <= curdate() group by mes order by fecha_venta desc";

			if (coid > 0) {
				query = "select count(id) as colocadas,sum(if(coid>0,1,0)) as activas,sum(if(activo=2,1,0)) as bajas,DATE_FORMAT(fecha_venta,'%m-%Y') as mes"
						+ " from inventario__punto where id_pos = " + idPos + " and fecha_venta >= '" + fechaIni
						+ "' and fecha_venta <= curdate() group by mes order by fecha_venta desc";
			}
			
			/*
			query_old = "select count(id) as colocadas,sum(if(activo=1,1,0)) as activas,sum(if(activo=2,1,0)) as bajas,DATE_FORMAT(fecha_venta,'%m-%Y') as mes"
					+ " from simcards where id_pos = " + idPos + " and fecha_venta >= '" + fechaIni
					+ "' and fecha_venta <= curdate() group by mes order by fecha_venta desc";

			query_ventas_tat = "select count(id) as colocadas,sum(if(coid>0,1,0)) as activas,sum(if(activo=2,1,0)) as bajas,DATE_FORMAT(fecha_venta,'%m-%Y') as mes"
					+ " from {$this->database}.inventario__punto where id_pos = "+ idPos + " and fecha_venta >= '"+fechaIni
					+ "' and fecha_venta <= curdate() group by mes order by fecha_venta desc";
 			*/

			System.out.println(query);
			nativeQuery = em.createNativeQuery(query, InfoRotacion.class);
			List<InfoRotacion> rotacion = new ArrayList<InfoRotacion>();
			rotacion = (List<InfoRotacion>) nativeQuery.getResultList();

			/*
			 * if (rotacion.size() > 0) { return rotacion; } else { return null; }
			 */
			return rotacion;
		} catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getLocalizedMessage(), query, ruta_log);
			return null;
		}
	}

}
