package com.apirestclaro.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.apirestclaro.entitys.NivelesDcs;
import com.serviciologerrores.functions.LogErrores;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class PermisosDao{
	
	@Value("${operador.bd}")
	private String bd;
	
	@Value("${operador.bd_pos}")
	private String bd_pos;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;

    @PersistenceContext
	private EntityManager em;

	private Query nativeQuery;

    @SuppressWarnings("unchecked")
    public Map<String, Object> buscarNiveles(Long idUs) {

        Map<String, Object> permisos = new HashMap<>();
        String permisosRegional = "";
        String permisosDistribuidor = "";
        String permisosCircuitos = "";
        String permisosRutas = "";
		String query = "";
		try {
			query = "select id,tipo,group_concat(id_tipo) as id_tipos, 1 as accion, p2 from niveles_dcs where id_usus = "
                    + idUs + " group by tipo";
            
            nativeQuery = em.createNativeQuery(query, NivelesDcs.class);
            List<NivelesDcs> nivelesDcs = (List<NivelesDcs>) nativeQuery.getResultList();

            if(!nivelesDcs.isEmpty()){     
                
                for(NivelesDcs nivel: nivelesDcs){

                    if (nivel.getTipo() == 1) {
                        if (nivel.getIdTipos().length() > 0) {
                            permisosRegional += nivel.getIdTipos() + ",";
                        }
                    }
                    if (nivel.getTipo() == 3) {
                        if (nivel.getIdTipos().length() > 0) {
                            permisosDistribuidor += nivel.getIdTipos() + ",";
                        }
                    }
                    if (nivel.getTipo() == 4) {
                        if (nivel.getIdTipos().length() > 0) {
                            permisosCircuitos += nivel.getIdTipos() + ",";
                        }
                    }
                    if (nivel.getTipo() == 5) {
                        if (nivel.getIdTipos().length() > 0) {
                            permisosRutas += nivel.getIdTipos() + ",";
                        }
                    }

                }

                if(!permisosRegional.isEmpty()) permisosRegional = permisosRegional.substring(0, permisosRegional.length()-1);
                if(!permisosDistribuidor.isEmpty()) permisosDistribuidor = permisosDistribuidor.substring(0, permisosDistribuidor.length()-1);
                if(!permisosCircuitos.isEmpty()) permisosCircuitos = permisosCircuitos.substring(0, permisosCircuitos.length()-1);
                if(!permisosRutas.isEmpty()) permisosRutas = permisosRutas.substring(0, permisosRutas.length()-1);


            }

            permisos.put("permisosRegional", permisosRegional);
            permisos.put("permisosDistribuidor", permisosDistribuidor);
            permisos.put("permisosCircuitos", permisosCircuitos);
            permisos.put("permisosRutas", permisosRutas);

            return permisos;

		} catch (NoResultException e) {
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query,ruta_log);
			return null;
		}
    }

	
	@SuppressWarnings({ "unchecked" })
	@Transactional(readOnly = true)
	public String retornarRutas(Long idUsuario) {
		String permisos = "";
		// Funcion que retorna las zonas a las que el usurario tiene permisos en los niveles dcs.!
		String queryPermisos = "SELECT distinct z.id, z.descripcion "
				+ "FROM niveles_dcs nd, territorios_distribuidor td, zonas z "
				+ "WHERE nd.id_tipo = td.id_regional AND td.id_territorio = z.territorio AND nd.tipo = 1 AND nd.id_usus = " + idUsuario
                + " AND z.estado = 1 "
                + "UNION SELECT distinct z.id, z.descripcion "
				+ "FROM niveles_dcs nd, territorios_distribuidor td, zonas z "
				+ "WHERE nd.id_tipo = td.id_distri AND td.id_territorio = z.territorio AND nd.tipo = 3 AND nd.id_usus = " + idUsuario
				+ " AND z.estado = 1 "
				+ "UNION SELECT distinct  z.id, z.descripcion "
				+ "FROM niveles_dcs nd,zonas z WHERE z.id = nd.id_tipo AND nd.id_usus = " + idUsuario + " AND nd.tipo = 5 AND z.estado = 1 "
				+ "UNION SELECT distinct z.id, z.descripcion "
				+ "FROM niveles_dcs nd, zonas z, territorios t "
				+ "WHERE t.id = z.territorio AND nd.id_tipo = t.id AND nd.id_usus = " + idUsuario + " AND nd.tipo = 4 AND z.estado = 1";
		nativeQuery = em.createNativeQuery(queryPermisos);
		List<Object[]> permisoZonas = (List<Object[]>)nativeQuery.getResultList();
		
		if(!permisoZonas.isEmpty()){
			for (Object[] permisoZona : permisoZonas) {
				permisos += permisoZona[0] + ",";
			}
			
			permisos = permisos.substring(0, permisos.length()-1);
		}

		
		return permisos;
    }

}