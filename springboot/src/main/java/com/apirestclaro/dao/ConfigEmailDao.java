package com.apirestclaro.dao;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.apirestclaro.entitys.ConfigEmail;
import com.serviciologerrores.functions.LogErrores;

@Repository
public class ConfigEmailDao {
	
	@Value("${operador.ruta_log}")
	private String ruta_log;
	
	@PersistenceContext
	private EntityManager em;
	private Query nativeQuery; 

	public ConfigEmail consultarConfig(){
		
		String query = ""; 
		try {
			query = "select id,host,port,security,user,pass from configuraciones_email";
			nativeQuery = em.createNativeQuery(query,ConfigEmail.class);

			return (ConfigEmail)nativeQuery.getSingleResult();
			
		}catch (NoResultException e) {
			return null;
		}
		catch (Exception e2) {
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			return null;
		}
	}
}
