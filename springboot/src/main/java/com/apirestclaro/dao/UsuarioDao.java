package com.apirestclaro.dao;

import java.util.ArrayList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.apirestclaro.entitys.Circuitos;
import com.apirestclaro.entitys.Distribuidor;
import com.apirestclaro.entitys.EstadosCom;
import com.apirestclaro.entitys.Motivos;
import com.apirestclaro.entitys.NivelesDcs;
import com.apirestclaro.entitys.Puntos;
import com.apirestclaro.entitys.Rutas;
import com.apirestclaro.entitys.Usuario;
import com.apirestclaro.functions.BCrypt;
import com.serviciologerrores.functions.LogErrores;

@Repository
public class UsuarioDao {
	
	@Value("${operador.bd}")
	private String bd;
	
	@Value("${operador.bd_pos}")
	private String bd_pos;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;

	@PersistenceContext
	private EntityManager em;

	private Query nativeQuery;

	public List<?> findAll() {

		String query = "";
		try {
			query = "SELECT usuarios.id as id,cedula,nombre,apellido,user,if(per.descripcion is null,'NINGUNO',per.descripcion) as perfil,id_perfil,estado_pass, (SELECT configuracion_general.radio_precision_gps FROM  " + bd + ".configuracion_general) AS tolerancia_visita FROM usuarios "
					+ "left join perfiles as per on (usuarios.id_perfil = per.id) " + "limit 5";

			nativeQuery = em.createNativeQuery(query, Usuario.class);

			return nativeQuery.getResultList();

		} catch (NoResultException e) {
			return null;
		} catch (Exception e2) {
			LogErrores.escribirLog(e2.getLocalizedMessage(), query,ruta_log);
			return null;
		}
	}

	@Transactional
	public Map<String, Object> finByUsername(String user, String pass) {

		Map<String, Object> response = new HashMap<String, Object>();

		String query = "";
		try {
			int contBloqueo = 0;
			int idus = 0;
			query = "select password,bloqueo,id from usuarios where user = '" + user + "'";
			nativeQuery = em.createNativeQuery(query);
			Object[] results = (Object[]) nativeQuery.getSingleResult();

			String passbd = results[0].toString();
			contBloqueo = (int) results[1];
			idus = (int) results[2];

			if (BCrypt.checkpw(pass, passbd)) {
				query = "select usuarios.id as id,cedula,nombre,apellido,user,if(per.descripcion is null,'NINGUNO',per.descripcion) as perfil,id_perfil,estado_pass,now() as fecha_hora, (SELECT configuracion_general.radio_precision_gps FROM  " + bd + ".configuracion_general) AS tolerancia_visita FROM usuarios "
						+ "left join perfiles as per on (usuarios.id_perfil = per.id) "
						+ "where usuarios.estado = 1 and estado_bloqueo = 1 and usuarios.user = '" + user + "'";

				nativeQuery = em.createNativeQuery(query, Usuario.class);
				Usuario us = (Usuario) nativeQuery.getSingleResult();

				response.put("estado", 1);
				response.put("usuario", us);

				if (contBloqueo > 0) {
					query = "update usuarios set bloqueo = 0 where id = " + idus;
					nativeQuery = em.createNativeQuery(query);
					nativeQuery.executeUpdate();
				}

				return response;

			} else {
				contBloqueo++;
				if (contBloqueo >= 3) {
					query = "update usuarios set bloqueo = '" + contBloqueo + "',estado_bloqueo = 0 where id = " + idus;
				} else {
					query = "update usuarios set bloqueo = '" + contBloqueo + "' where id = " + idus;
				}

				nativeQuery = em.createNativeQuery(query);
				/*
				 * nativeQuery.setParameter("bloqueo", contBloqueo);
				 * nativeQuery.setParameter("id", idus);
				 */

				int update = nativeQuery.executeUpdate();

				if (update > 0) {
					if (contBloqueo >= 3) {
						response.put("estado", 0);
						response.put("msg", "Error tu usuario ha sido bloqueado por nro de intentos fallidos");
					} else {
						response.put("estado", 0);
						response.put("msg", "Error contraseña incorrecta, intentos disponibles: " + (3 - contBloqueo));
					}
				} else {
					response.put("estado", 0);
					response.put("msg", "Error al actualizar los datos");
				}

				return response;
			}

		} catch (NoResultException e) {
			//System.out.println(e.getMessage());
			//System.out.println(query);
			response.put("estado", 2);
			response.put("msg", "Error usuario y/o contraseña incorrectos");
			return response;
		} catch (Exception e2) {
			//e2.printStackTrace();
			//System.out.println("ingresó");
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			response.put("estado", 3);
			response.put("msg", "Error Exception");
			return null;
		}

	}

	@Transactional
	public String generarToken(Usuario us, String tokenNotificacion, String versionName, int versionCode,
			Map<String, Object> datos) {

		String query = "";
		String queryAudi = "";
		long currentime = System.currentTimeMillis();
		String token = "";

		String latitud = "0";
		String longitud = "0";
		int posicionSimulada = 0;
		String tipoConexion = "0";
		String direccionIp = "0";
		String imei = "0";
		String modelo = "0";
		String versionOS = "0";
		String plataforma = "0";

		if (datos.containsKey("latitud")) {
			latitud = datos.get("latitud").toString();
		}
		if (datos.containsKey("longitud")) {
			longitud = datos.get("longitud").toString();
		}
		if (datos.containsKey("posicionSimulada")) {
			posicionSimulada = Integer.parseInt(datos.get("posicionSimulada").toString());
		}
		if (datos.containsKey("tipoConexion")) {
			tipoConexion = datos.get("tipoConexion").toString();
		}
		if (datos.containsKey("direccionIp")) {
			direccionIp = datos.get("direccionIp").toString();
		}
		if (datos.containsKey("imei")) {
			imei = datos.get("imei").toString();
		}
		if (datos.containsKey("modelo")) {
			modelo = datos.get("modelo").toString();
		}
		if (datos.containsKey("versionOS")) {
			versionOS = datos.get("versionOS").toString();
		}
		if (datos.containsKey("plataforma")) {
			plataforma = datos.get("plataforma").toString();
		}

		int generatedLong = new Random().nextInt();
		boolean error = false;

		if (generatedLong < 0) {
			generatedLong = generatedLong * -1;
		}

		token = us.getId().toString() + generatedLong + "" + currentime;
		long idUsuario = us.getId();

		try {
			query = "update usuarios set token_log = '" + token
					+ "',fecha_ge_to = curdate(),hora_ge_to = curtime(),tiempo_session = NOW(), token_noti = '"
					+ tokenNotificacion + "' where id = " + idUsuario;
			nativeQuery = em.createNativeQuery(query);
			int update = nativeQuery.executeUpdate();

			if (update <= 0) {
				error = true;
			}

			queryAudi = "INSERT INTO audi_logueo_app (fecha, hora, version_name, version_code,imei,latitud,longitud,posicion_simulada,tipoconexion,direccionip,modelo,version_os,plataforma,id_usuario) VALUES (CURDATE(), CURTIME(),'"
					+ versionName + "', " + versionCode + ",'" + imei + "','" + latitud + "','" + longitud + "','"
					+ posicionSimulada + "','" + tipoConexion + "','" + direccionIp + "','" + modelo + "','" + versionOS
					+ "','" + plataforma + "'," + idUsuario + ")";

			nativeQuery = em.createNativeQuery(queryAudi);
			int insertAudiLogueo = nativeQuery.executeUpdate();

			if (insertAudiLogueo <= 0) {
				error = true;
			}

			if (!error) {
				return token;
			} else {
				return "Error";
			}

		} catch (Exception e) {
			LogErrores.escribirLog(e.getMessage(), query + " ----  "+ queryAudi,ruta_log);
			return "Error";
		}
	}

	public Usuario validarToken(String token) {
		String query = "";
		try {
			query = "select usuarios.id as id,cedula,nombre,apellido,user,if(per.descripcion is null,'NINGUNO',per.descripcion) as perfil,id_perfil,estado_pass, now() as fecha_hora, (SELECT configuracion_general.radio_precision_gps FROM  " + bd + ".configuracion_general) AS tolerancia_visita FROM usuarios "
					+ "left join perfiles as per on (usuarios.id_perfil = per.id) "
					+ "where usuarios.estado = 1 and estado_bloqueo = 1 and usuarios.token_log = '" + token + "'";

			nativeQuery = em.createNativeQuery(query, Usuario.class);

			return (Usuario) nativeQuery.getSingleResult();

		} catch (NoResultException e) {
			return null;
		} catch (Exception e2) {
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			return null;
		}
	}

	public int validarEmail(String email) {

		String query = "";
		try {
			query = "select id from usuarios where email = '" + email + "'";
			nativeQuery = em.createNativeQuery(query);

			int idus = (int) nativeQuery.getSingleResult();

			if (idus > 0) {
				return idus;
			} else {
				return 0;
			}

		} catch (NoResultException e) {
			return 0;
		} catch (Exception e2) {
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			return 0;
		}
	}

	@Transactional
	public Boolean guardarRecoPass(int idus, String recover) {
		em.clear();
		String query = "";
		try {

			query = "update usuarios set recover='" + recover
					+ "',fecha_reco = curdate(),hora_reco = ADDTIME(curtime(),'00:30:00')  WHERE id = " + idus;

			nativeQuery = em.createNativeQuery(query);

			int update = (int) nativeQuery.executeUpdate();

			if (update > 0) {
				query = "insert into audi_usuarios " + "(id_usuario,recover,usuario,movimiento)"
						+ "SELECT id,recover,id,2 FROM usuarios WHERE id = " + idus;
				nativeQuery = em.createNativeQuery(query);

				int update2 = (int) nativeQuery.executeUpdate();

				if (update2 > 0) {
					return true;
				} else {
					return false;
				}
			} else {
				return false;
			}
		} catch (Exception e) {
			LogErrores.escribirLog(e.getMessage(), query,ruta_log);
			return false;
		}
	}

	@Transactional
	public int cambiarPass(String passold, String passnew, int idus) {
		em.clear();
		String query = "";
		try {
			query = "select password from usuarios where id = " + idus;
			nativeQuery = em.createNativeQuery(query);
			String passhash = "";
			String passbd = nativeQuery.getSingleResult().toString();

			if (BCrypt.checkpw(passold, passbd)) {
				passhash = BCrypt.hashpw(passnew, BCrypt.gensalt());
				query = "update usuarios set password = '" + passhash + "',estado_pass = 1 where id = " + idus;
				nativeQuery = em.createNativeQuery(query);

				if (nativeQuery.executeUpdate() > 0) {
					query = "insert into audi_usuarios " + "(id_usuario,password,movimiento)"
							+ "SELECT id,password,2 FROM usuarios WHERE id = " + idus;
					nativeQuery = em.createNativeQuery(query);

					if (nativeQuery.executeUpdate() > 0) {
						return 1;
					} else {
						return 0;
					}
				} else {
					return 0;
				}
			} else {
				return 2;
			}
		} catch (Exception e) {
			LogErrores.escribirLog(e.getMessage(), query,ruta_log);
			return 0;
		}
	}

	public Map<String, Object> getPermisos(int idUs, String fechahora) {
		em.clear();
		String query = "";

		List<Circuitos> circuitos = new ArrayList<Circuitos>();
		List<Circuitos> circuitosPermisosZonas = new ArrayList<Circuitos>();
		List<Rutas> rutas = new ArrayList<Rutas>();
		List<Distribuidor> distribuidores = new ArrayList<Distribuidor>();

		Map<String, Object> response = new HashMap<String, Object>();

		try {

			if (fechahora != "" && fechahora != null) {
				query = "select id,tipo,group_concat(id_tipo) as id_tipos, if(movimiento = 1,movimiento,0) as accion, p2 from audi_niveles_dcs_detallado where id_usus = "
						+ idUs + " and concat(fecha,' ',hora) > '" + fechahora
						+ "' group by tipo, p2,accion UNION SELECT id,4 AS tipo,group_concat(id) as id_tipos, estado AS accion, 0 AS p2 FROM territorios WHERE f_update > '"
						+ fechahora
						+ "' HAVING id_tipos IS NOT NULL UNION ALL SELECT id,5 AS tipo,group_concat(id) as id_tipos, estado AS accion, territorio AS p2 FROM zonas WHERE f_update > '"
						+ fechahora + "' HAVING id_tipos IS NOT NULL";
			} else {
				query = "select id,tipo,group_concat(id_tipo) as id_tipos, 1 as accion, p2 from niveles_dcs where id_usus = "
						+ idUs + " group by tipo, p2";
			}
			nativeQuery = em.createNativeQuery(query, NivelesDcs.class);
			List<NivelesDcs> niveles = nativeQuery.getResultList();

			for (NivelesDcs nivel : niveles) {
				if (nivel.getTipo() == 1) {
					query = "select ter.id, ter.nombre as descripcion, IF(ter.estado = 1," + nivel.getAccion() + ",0) as accion, tdis.id_distri "
							+ "from territorios as ter "
							+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
							+ "where tdis.id_regional in (" + nivel.getIdTipos() + ") ORDER BY ter.id";
					nativeQuery = em.createNativeQuery(query, Circuitos.class);
					circuitos.addAll(nativeQuery.getResultList());
					circuitosPermisosZonas.addAll(nativeQuery.getResultList());
				}
				if (nivel.getTipo() == 3) {
					query = "select ter.id, ter.nombre as descripcion, IF(ter.estado = 1," + nivel.getAccion() + ",0) as accion, tdis.id_distri "
							+ "from territorios as ter "
							+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
							+ "where tdis.id_distri in (" + nivel.getIdTipos() + ") ORDER BY ter.id";
					nativeQuery = em.createNativeQuery(query, Circuitos.class);
					circuitos.addAll(nativeQuery.getResultList());
					circuitosPermisosZonas.addAll(nativeQuery.getResultList());
				}
				if (nivel.getTipo() == 4) {
					query = "select ter.id, ter.nombre as descripcion, IF(ter.estado = 1," + nivel.getAccion() + ",0) as accion, tdis.id_distri "
							+ "from territorios as ter " 
							+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
							+ "where ter.id in (" + nivel.getIdTipos()
							+ ") ORDER BY ter.id";
					nativeQuery = em.createNativeQuery(query, Circuitos.class);
					circuitos.addAll(nativeQuery.getResultList());
					circuitosPermisosZonas.addAll(nativeQuery.getResultList());
				}

				if (nivel.getTipo() == 5) {
					query = "select id,tipo,'' as id_tipos, 1 as accion, p2 from niveles_dcs where id_usus = " + idUs
							+ " AND p2 = " + nivel.getP2() + " AND tipo = 5";
					nativeQuery = em.createNativeQuery(query, NivelesDcs.class);
					List<NivelesDcs> nivelesUlt = nativeQuery.getResultList();

					if (nivelesUlt.size() == 0) {
						query = "select ter.id, ter.nombre as descripcion, 0 as accion, tdis.id_distri from territorios AS ter "
								+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
								+ "inner join zonas ON (zonas.territorio = ter.id) "
								+ "where zonas.id in ("
								+ nivel.getIdTipos() + ") and zonas.estado = 1 GROUP BY ter.id ORDER BY ter.id";
						nativeQuery = em.createNativeQuery(query, Circuitos.class);
						circuitos.addAll(nativeQuery.getResultList());
					} else if (nivelesUlt.size() > 0) {
						if (nivelesUlt.size() == 1) {
							if (nivel.getAccion() == 0) {
								query = "select ter.id, ter.nombre as descripcion, " + nivel.getAccion() + " as accion, tdis.id_distri from territorios AS ter "
										+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
										+ "inner join zonas ON (zonas.territorio = ter.id) "
										+ "where zonas.id in ("
										+ nivel.getIdTipos() + ") GROUP BY ter.id ORDER BY ter.id";
								nativeQuery = em.createNativeQuery(query, Circuitos.class);
								circuitos.addAll(nativeQuery.getResultList());
							} else {
								query = "select ter.id, ter.nombre as descripcion, IF(ter.estado = 1,IF(zonas.estado = 1,1,0),0) as accion, tdis.id_distri from territorios AS ter "
										+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
										+ "inner join zonas ON (zonas.territorio = ter.id) "
										+ "where zonas.id in ("
										+ nivel.getIdTipos() + ") GROUP BY ter.id ORDER BY ter.id";
								nativeQuery = em.createNativeQuery(query, Circuitos.class);
								circuitos.addAll(nativeQuery.getResultList());
							}
						} else {
							query = "select ter.id, ter.nombre as descripcion, " + nivel.getAccion() + " as accion, tdis.id_distri from territorios AS ter "
									+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
									+ "inner join zonas ON (zonas.territorio = ter.id) "
									+ "where zonas.id in ( "
									+ nivel.getIdTipos() + ") GROUP BY ter.id ORDER BY ter.id";
							nativeQuery = em.createNativeQuery(query, Circuitos.class);
							circuitos.addAll(nativeQuery.getResultList());
						}

					}
					query = "select id,nombre as descripcion,territorio as id_circuito, IF(estado = 1," + nivel.getAccion() + ",0) as accion from zonas " 
							+ "where zonas.id in ("	+ nivel.getIdTipos() 
							+ ") ORDER BY zonas.id";
					nativeQuery = em.createNativeQuery(query, Rutas.class);
					rutas.addAll(nativeQuery.getResultList());
				}
			}

			List<Circuitos> noDuplicadosCircuitos = circuitos.stream().distinct().collect(Collectors.toList());
			response.put("circuitos", noDuplicadosCircuitos);

			List<Circuitos> noDuplicadosCircuitosPermisosZonas = circuitosPermisosZonas.stream().distinct().collect(Collectors.toList());		
			String idCircuitos = noDuplicadosCircuitosPermisosZonas.stream().map(Circuitos::getId).map(Object::toString).collect(Collectors.joining(",")).toString();
			if(!idCircuitos.isEmpty()){ //obtener rutas a partir de los circuitos
				query = "select id, nombre as descripcion, territorio as id_circuito, estado as accion from zonas "
				+ "where territorio IN (" + idCircuitos + ") ORDER BY territorio, id";
				nativeQuery = em.createNativeQuery(query, Rutas.class);
				rutas.addAll(nativeQuery.getResultList());
			} 			
			List<Rutas> noDuplicadosRutas = rutas.stream().distinct().collect(Collectors.toList());
			if(!noDuplicadosRutas.isEmpty()){ // actualizar accion de acuerdo al circuito asociado a la ruta
				noDuplicadosRutas = noDuplicadosRutas.stream().map((Rutas r)->{
					Circuitos cir = noDuplicadosCircuitos.stream().filter((Circuitos c) -> c.getId()==r.getIdCircuito()).collect(Collectors.toList()).get(0);
					r.setAccion(cir.getAccion());
					return r;
				}).collect(Collectors.toList());
			}
			response.put("rutas", noDuplicadosRutas);

			/*
			for (Circuitos cir : noDuplicadosCircuitosPermisosZonas) {
				query = "select id,nombre as descripcion,territorio as id_circuito, estado as accion from zonas where estado = 1 and territorio = " + cir.getId() + " ORDER BY id";
				//System.out.println(query);
				nativeQuery = em.createNativeQuery(query, Rutas.class);
				rutas.addAll(nativeQuery.getResultList());
			}

			query = "select tdis.id_distri, ter.nombre from territorios as ter "
					+ "inner join territorios_distribuidor as tdis on (tdis.id_territorio = ter.id) "
					+ "inner join zonas ON (zonas.territorio = ter.id) "
					+ "GROUP BY tdis.id_distri ORDER BY tdis.id_distri";			
			
			query = "select rdis.id_distri, ter.nombre from territorios as ter "
					+ "inner join regionales_distri as rdis on (rdis.id_distri = ter.id) "
					+ "inner join zonas ON (zonas.territorio = ter.id) "
					+ "GROUP BY rdis.id_distri ORDER BY rdis.id_distri";
			*/

			String idAgentes = circuitos.stream().map(Circuitos::getIdAgente).distinct().map(Object::toString).collect(Collectors.joining(", ")).toString();
			if(!idAgentes.isEmpty()){
				query = "select id, nombre from distribuidores where id IN (" + idAgentes + ") ORDER BY id";
				nativeQuery = em.createNativeQuery(query, Distribuidor.class);
				distribuidores.addAll(nativeQuery.getResultList());
			}
			response.put("agentes", distribuidores);

			return response;

		} catch (Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}

	public Map<String, Object> getPuntos(int idUs) {

		String query = "";
		List<Puntos> puntos = new ArrayList<Puntos>();
		Map<String, Object> response = new HashMap<String, Object>();
		int x = 0;
		try {
			query = "select pos.idpos,pos.cedula,pos.id_distri,pos.razon as nombre,pos.detalle_direccion as direccion,vis.fecha,vis.hora,vis.efectiva, IF((SELECT GROUP_CONCAT(DISTINCT id_encuesta) encuesta "
					+ "FROM enc__respuestas_encuesta WHERE tipo_encuestado = 2 AND id_encuestado = pos.idpos) IS NULL, '',(SELECT GROUP_CONCAT(DISTINCT id_encuesta) encuesta "
					+ "FROM enc__respuestas_encuesta WHERE tipo_encuestado = 2 AND id_encuestado = pos.idpos)) AS encuestas_respondidas, (SELECT COUNT(id) FROM  " + bd_pos + ".visitas_operador AS vc WHERE vc.punto = vis.punto AND vc.usuario = vis.usuario AND vc.fecha = CURDATE()) AS cantidad_visitas, pos.precisiongps, pos.latitud, pos.longitud "
					+ "from  " + bd_pos + ".visitas_operador as vis "
					+ "inner join  " + bd_pos + ".puntos as pos on (pos.idpos = vis.punto) "
					+ "where vis.app = 1 AND vis.usuario = " + idUs + " AND vis.fecha = CURDATE() AND pos.estado = 1 AND pos.aprobado = 1 GROUP BY idpos";

			nativeQuery = em.createNativeQuery(query, Puntos.class);
			puntos.addAll(nativeQuery.getResultList());

			if (!puntos.isEmpty()) {

				for (Puntos punto : puntos) {

					long idPunto = punto.getIdPos();

					String sqlEfectiva = "SELECT fecha, hora, efectiva FROM  " + bd_pos + ".visitas_operador WHERE app = 1 AND usuario = "
							+ idUs + " AND fecha = CURDATE() AND punto = " + idPunto
							+ " AND efectiva = 1 ORDER BY id DESC LIMIT 1";
					nativeQuery = em.createNativeQuery(sqlEfectiva);
					List<Object[]> visitaEfectiva = (List<Object[]>) nativeQuery.getResultList();

					if (!visitaEfectiva.isEmpty()) {
						String fechaVisita = visitaEfectiva.get(0)[0].toString();
						String horaVisita = visitaEfectiva.get(0)[1].toString();
						int efectiva = (int) visitaEfectiva.get(0)[2];

						puntos.get(x).setFecha(fechaVisita);
						puntos.get(x).setHora(horaVisita);
						puntos.get(x).setEfectiva(efectiva);
					} else {
						String sqlNoefectiva = "SELECT fecha, hora, efectiva FROM  " + bd_pos + ".visitas_operador WHERE app = 1 AND usuario = "
								+ idUs + " AND fecha = CURDATE() AND punto = " + idPunto
								+ " AND efectiva = 0 ORDER BY id DESC LIMIT 1";
						nativeQuery = em.createNativeQuery(sqlNoefectiva);
						List<Object[]> visitaNoEfectiva = (List<Object[]>) nativeQuery.getResultList();

						if (!visitaNoEfectiva.isEmpty()) {
							String fechaVisita = visitaNoEfectiva.get(0)[0].toString();
							String horaVisita = visitaNoEfectiva.get(0)[1].toString();
							int efectiva = (int) visitaNoEfectiva.get(0)[2];

							puntos.get(x).setFecha(fechaVisita);
							puntos.get(x).setHora(horaVisita);
							puntos.get(x).setEfectiva(efectiva);
						}
					}

					x++;
				}

			}

			response.put("puntosVisitados", puntos);
			return response;
		} catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getMessage(), query,ruta_log);
			return null;
		}
	}

	public List<EstadosCom> estadosComerciales(String fecha) {
		String query = "";
		List<EstadosCom> estadoscom = new ArrayList<EstadosCom>();
		try {
			if (fecha != "") {
				query = "select id,descripcion, 1 as accion from  " + bd_pos + ".categorias where fecha_hora_movi > '"
						+ fecha + "'";
			} else {
				query = "select id,descripcion,1 as accion from  " + bd_pos + ".categorias";
			}

			nativeQuery = em.createNativeQuery(query, EstadosCom.class);
			estadoscom = (List<EstadosCom>) nativeQuery.getResultList();
			return estadoscom;
		} catch (Exception ex) {
			LogErrores.escribirLog(ex.getMessage(), query,ruta_log);
			return null;
		}
	}

	public List<Motivos> listarMotivos() {
		String query = "";
		List<Motivos> motivos = new ArrayList<>();
		try {

			query = "SELECT id,motivo FROM  " + bd_pos + ".motivos_visita_claro";

			nativeQuery = em.createNativeQuery(query, Motivos.class);
			motivos.addAll(nativeQuery.getResultList());

			return motivos;
		} catch (Exception ex) {
			LogErrores.escribirLog(ex.getMessage(), query,ruta_log);
			return null;
		}
	}

}
