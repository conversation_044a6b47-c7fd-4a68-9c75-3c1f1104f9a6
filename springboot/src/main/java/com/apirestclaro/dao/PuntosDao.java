package com.apirestclaro.dao;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.apirestclaro.entitys.CarteraPuntos;
import com.apirestclaro.entitys.CategoriasPuntos;
import com.apirestclaro.entitys.DcsPuntos;
import com.apirestclaro.entitys.Departamentos;
import com.apirestclaro.entitys.EscalasPdv;
import com.apirestclaro.entitys.Municipios;
import com.apirestclaro.entitys.Puntos;
import com.apirestclaro.entitys.TiposDocumento;
import com.apirestclaro.functions.Functions;
import com.apirestclaro.pojos.CamposDinamicosPost;
import com.apirestclaro.pojos.CrearActualizarPuntosPost;
import com.serviciologerrores.functions.LogErrores;

@Repository
public class PuntosDao {
	
	@Value("${operador.bd}")
	private String bd;
	
	@Value("${operador.bd_pos}")
	private String bd_pos;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;
	
	@Value("${operador.aws_bucket}")
	private String awsBucket;
	
	@PersistenceContext
	private EntityManager em;

	private Query nativeQuery;
	
	@SuppressWarnings("unchecked")
	public Map<String, Object> buscarPuntos(int idUs,String nombre, int idpos, int circuito, int ruta, int estadocom,int pagina,int idAgente, String documento){
		
		String condicion = "";
		String query = "";
		String queryCount = "";
		Map<String, Object> permisosDcs = Functions.getPermisosDcs();
		Map<String, Object> datos = new HashMap<>();
		int totalPuntos = 0;
		boolean dcs = false;
		boolean otroPermiso = false;
		List<Puntos> listaPuntos = new ArrayList<>();

		String permisosRegional = permisosDcs.get("permisosRegional").toString();
		String permisosDistribuidor = permisosDcs.get("permisosDistribuidor").toString();
		String permisosCircuitos = permisosDcs.get("permisosCircuitos").toString();
		String permisosRutas = permisosDcs.get("permisosRutas").toString();

		if (nombre.trim().length() > 0) {
			condicion = condicion + " and pos.razon like '%" + nombre + "%'";
		}
		if(idpos > 0) {
			condicion = condicion + " and pos.idpos = " + idpos;
		}else if(!documento.trim().isEmpty()) {
			condicion = condicion + " and pos.cedula = '" + documento+"'";
		}
		if(circuito > 0) {
			condicion = condicion + " and pos.territorio = " + circuito;
		}
		if(ruta > 0) {
			condicion = condicion + " and pos.zona = " + ruta;
		}
		if(estadocom > 0) {
			condicion = condicion + " and pos.estado_com = " + estadocom;
		}
		if(idAgente > 0) {
			condicion = condicion + " and pos.id_distri = " + idAgente;
		}

		condicion += " AND (";

		if(!permisosRegional.isEmpty()){
			condicion += " (td.id_regional IN (" + permisosRegional + "))";
			dcs = true;
			otroPermiso = true;
		} 
		if(!permisosDistribuidor.isEmpty()){
			if(otroPermiso){
				condicion += " OR";
			}	

			condicion += " (td.id_distri IN (" + permisosDistribuidor + "))";
			dcs = true;
			otroPermiso = true;
		}
		if(!permisosCircuitos.isEmpty()){
			if(otroPermiso){
				condicion += " OR";
			}

			condicion += " (pos.territorio IN (" + permisosCircuitos + "))";
			dcs = true;
			otroPermiso = true;
		}
		if(!permisosRutas.isEmpty()){
			if(otroPermiso){
				condicion += " OR";
			}
			condicion += " (pos.zona IN (" + permisosRutas + "))";
			dcs = true;
		}

		condicion += " )";

		if(dcs){

			try{
				int inicio = 0; 
				if(pagina >= 1) {
					inicio = inicio +(pagina * 20);
				}
				
				query = "select pos.idpos,pos.id_distri,pos.razon as nombre,pos.detalle_direccion as direccion,if(visf.fecha is null, '', visf.fecha) as fecha ,if(visf.hora is null, '', visf.hora) as hora, " 
						+ " if(visf.efectiva is null, 0,visf.efectiva) as efectiva, IF((SELECT GROUP_CONCAT(DISTINCT id_encuesta) encuesta FROM enc__respuestas_encuesta "
						+ "WHERE tipo_encuestado = 2 AND id_encuestado = pos.idpos) IS NULL,'',(SELECT GROUP_CONCAT(DISTINCT id_encuesta) encuesta FROM enc__respuestas_encuesta " 
						+ "WHERE tipo_encuestado = 2 AND id_encuestado = pos.idpos)) AS encuestas_respondidas, 0 AS cantidad_visitas, pos.precisiongps, pos.latitud, pos.longitud, pos.cedula from  " + bd_pos + ".puntos as pos " 
						+"left join (select vis.id,fecha,hora,vis.punto,efectiva,usuario from  " + bd_pos + ".visitas_operador vis inner join " 
						+ "(SELECT MAX(id) AS id FROM  " + bd_pos + ".visitas_operador WHERE fecha = CURDATE() AND usuario = " + idUs +" GROUP BY punto) as visop on (vis.id = visop.id AND vis.fecha = CURDATE()) where vis.usuario = " + idUs +") as visf " 
						+" on (visf.punto = pos.idpos) INNER JOIN  " + bd + ".territorios_distribuidor AS td ON (td.id_territorio = pos.territorio)" + 
						"where pos.estado = 1 AND pos.aprobado = 1 " + condicion + " limit " + inicio + "," + 20;
				nativeQuery = em.createNativeQuery(query, Puntos.class);
				listaPuntos = (List<Puntos>) nativeQuery.getResultList();
				if(!listaPuntos.isEmpty()){
					queryCount = "SELECT COUNT(pos.idpos) AS cantidadTotal FROM  " + bd_pos + ".puntos as pos INNER JOIN  " + bd + ".territorios_distribuidor AS td ON (td.id_territorio = pos.territorio) WHERE pos.estado = 1 AND pos.aprobado = 1 " + condicion;
					nativeQuery = em.createNativeQuery(queryCount);
					totalPuntos = ((Number) nativeQuery.getSingleResult()).intValue();
				}
				
			}
			catch(Exception ex) {
				//ex.printStackTrace();
				LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
				return null;
			}

		}

		datos.put("listaPuntos", listaPuntos);
		datos.put("totalPuntos", totalPuntos);
		
		return datos; 
						
	}
	
	
	public DcsPuntos buscarDcsPuntos(Long idPos) {
		
		DcsPuntos dcsPuntos = null;
		String query = "";
		
		try{
			
			query = "SELECT td.id, pos.territorio, pos.zona, td.id_regional, td.id_distri, pos.latitud, pos.longitud FROM  " + bd_pos + ".puntos as pos "
					+ "INNER JOIN  " + bd + ".territorios_distribuidor AS td ON (td.id_territorio = pos.territorio) "
					+ "WHERE pos.idpos = " + idPos + " AND pos.estado = 1 AND pos.aprobado = 1 ";
			nativeQuery = em.createNativeQuery(query, DcsPuntos.class);
			List<DcsPuntos> dcsPuntosList = (List<DcsPuntos>) nativeQuery.getResultList();
			
			if(!dcsPuntosList.isEmpty()) {
				dcsPuntos = dcsPuntosList.get(0);
			}
			
			return dcsPuntos; 
			
		}catch(Exception ex) {
			//ex.printStackTrace();
			LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
			return null;
		}
		
	}
	
	
	public List<EscalasPdv> clasificacionCategorias(){
		
		String query = "SELECT id,nombre as descripcion,CONCAT('"+awsBucket +"',icono) as url,principal from " + bd_pos + ".escalas__pdv where estado = 1";
		try {
			
			nativeQuery = em.createNativeQuery(query,EscalasPdv.class);
			
			return (List<EscalasPdv>) nativeQuery.getResultList();
			
			
		}
		catch(Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}
	
	public List<Departamentos> getDepartamentos(){
		String query = "SELECT id,nombre as descripcion from departamentos";
		try {
			
			nativeQuery = em.createNativeQuery(query,Departamentos.class);
			
			return (List<Departamentos>) nativeQuery.getResultList();
			
		}
		catch(Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}
	
	public List<Municipios> getMunicipios(){
		String query = "SELECT id,nombre as descripcion,departamento as id_depto from municipios";
		try {
			
			nativeQuery = em.createNativeQuery(query,Municipios.class);
			
			return (List<Municipios>) nativeQuery.getResultList();
			
		}
		catch(Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}
	
	public List<TiposDocumento> getTiposDocumento(){
		String query = "SELECT id,nombre_doc as descripcion from tipo_documento";
		try {
			
			nativeQuery = em.createNativeQuery(query,TiposDocumento.class);
			return (List<TiposDocumento>) nativeQuery.getResultList();
			
		}
		catch(Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}
	
	
	public List<CategoriasPuntos> getCategoriasPuntos(){
		String query = "SELECT id,nombre as descripcion from " + bd_pos + ".categorias_puntos";
		try {
			
			nativeQuery = em.createNativeQuery(query,CategoriasPuntos.class);
			return (List<CategoriasPuntos>) nativeQuery.getResultList();
			
		}
		catch(Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}


	@SuppressWarnings("unchecked")
	public List<CarteraPuntos> getCarteraPuntos(String nombre,int idpos,String documento,String codigoUnicoTienda,int idDepartamento,int idMunicipio,int idRuta,int idCircuito,int idEstadoComercial,int idAgente) {
		
		String condicion = "";
		String query = "";
		String queryCount = "";
		
		Map<String, Object> permisosDcs = Functions.getPermisosDcs();
		
		int totalPuntos = 0;
		boolean dcs = false;
		List<CarteraPuntos> listaPuntos = new ArrayList<>();

		String permisosRegional = permisosDcs.get("permisosRegional").toString();
		String permisosDistribuidor = permisosDcs.get("permisosDistribuidor").toString();
		String permisosCircuitos = permisosDcs.get("permisosCircuitos").toString();
		String permisosRutas = permisosDcs.get("permisosRutas").toString();
		
		if (nombre.length() > 0) {
			condicion = condicion + " and razon like '%" + nombre + "%'";
		}
		if(idpos > 0) {
			condicion = condicion + " and idpos = " + idpos;
		}
		if(documento.length() > 0) {
			condicion = condicion + " and cedula = '" + documento + "'";
		}
		if(codigoUnicoTienda.length() > 0) {
			condicion = condicion + " and cod_cum = '" + codigoUnicoTienda + "'";
		}
		if(idDepartamento > 0) {
			condicion = condicion + " and depto = " + idDepartamento;
		}
		if(idMunicipio > 0) {
			condicion = condicion + " and ciudad = " + idMunicipio;
		}
		if(idRuta > 0) {
			//condicion = condicion + " and territorio = " + idRuta; 
			condicion = condicion + " and zona = " + idRuta; //CarteraPuntos::idRuta=zona
		}
		if(idCircuito > 0) {
			//condicion = condicion + " and zona = " + idCircuito;
			condicion = condicion + " and territorio = " + idCircuito; //CarteraPuntos::idCircuito=territorio
		}
		if(idEstadoComercial > 0) {
			condicion = condicion + " and estado_com = " + idEstadoComercial;
		}
		if(idAgente > 0) {
			condicion = condicion + " and id_distri = " + idAgente;
		}

		condicion += " AND (";

		if(!permisosRegional.isEmpty()){
			condicion += " (id_regional IN (" + permisosRegional + "))";
			dcs = true;
		} 
		if(!permisosDistribuidor.isEmpty()){
			if(dcs){
				condicion += " OR";
			}	
			condicion += " (id_distri IN (" + permisosDistribuidor + "))";
			dcs = true;
			
		}
		if(!permisosCircuitos.isEmpty()){
			if(dcs){
				condicion += " OR";
			}
			condicion += " (territorio IN (" + permisosCircuitos + "))";
			dcs = true;
		}
		if(!permisosRutas.isEmpty()){
			if(dcs){
				condicion += " OR";
			}
			condicion += " (zona IN (" + permisosRutas + "))";
			dcs = true;
		}

		condicion += " )";

		if(dcs){

			try{
				
				query = "SELECT "
						+ "idpos,"
						+ "razon,"
						+ "nombre_cli,"
						+ "tipo_doc,"
						+ "cedula,"
						+ "email,"
						+ "tel,"
						+ "celular,"
						+ "tel_op,"
						+ "categoria,"
						+ "id_escala,"
						+ "depto,"
						+ "ciudad,"
						+ "zona,"
						+ "territorio,"
						+ "estado_com,"
						+ "barrio,"
						+ "detalle_direccion,"
						+ "latitud,"
						+ "longitud, "
						+ "id_distri "
						+ "FROM " + bd_pos + ".puntos WHERE estado = 1 AND aprobado = 1 " + condicion;
				
				nativeQuery = em.createNativeQuery(query, CarteraPuntos.class);
				listaPuntos = (List<CarteraPuntos>) nativeQuery.getResultList();
				
				if(listaPuntos.isEmpty()){
					return null;
				}
				else {
					return listaPuntos; 
				}
				
			}
			catch(Exception ex) {
				//ex.printStackTrace();
				LogErrores.escribirLog(ex.getLocalizedMessage(), query,ruta_log);
				return null;
			}

		}	
		else {
			return null;
		}
	}

	@Transactional
	public Map<String, Object> crearActualizarPuntos(CrearActualizarPuntosPost datos, int idUs) {
		String query = "";
		long idPos = 0;
		Map<String, Object> result = new HashMap<>();
		try{	
			int movimiento = 0;
			if(datos.getIdPos() > 0) {
				query = "UPDATE "+ bd_pos + ".puntos set razon = '" + datos.getNombrePdv() + "', nombre_cli = '" + datos.getNombreCliente() + "', tipo_doc = '" + datos.getTipoDocumento() + "', cedula = '" + datos.getNumeroDocumento() + "',"
						+ "email = '" + datos.getCorreo() + "', tel = '" + datos.getTelefonoFijo() + "', celular = '" + datos.getCelular() + "', tel_op = '" + datos.getTelefonoOp() + "', categoria = '" + datos.getIdCategoria() + "', "
						+ "id_escala = '" + datos.getIdCategoriaClasificacion() + "', depto = '" + datos.getIdDepartamento() + "', ciudad = '" + datos.getIdMunicipio()+ "', territorio = '" + datos.getIdCircuito()+ "', "
						+ "zona = '" + datos.getIdRuta()+ "', estado_com = '" + datos.getIdEstadoComercial()+ "', barrio = '" + datos.getBarrio() + "', detalle_direccion = '" + datos.getDireccion()+ "', "
						+ "latitud = '" + datos.getLatitud() + "', longitud = '" + datos.getLongitud() + "', estado = 1, aprobado = 1, user_update = " + idUs + ", origin_update = 0, updated_at = current_timestamp() "
						+ "where idpos = " + datos.getIdPos();
				movimiento = 2;
				idPos = datos.getIdPos();
			} else {
				query = "INSERT into "+ bd_pos + ".puntos (razon,nombre_cli,tipo_doc,cedula,email,tel,celular,tel_op,categoria,id_escala,depto,ciudad,territorio,zona,estado_com,barrio,detalle_direccion,latitud,longitud,estado,fecha_crea,hora_crea,usu_crea,user_create,created_at,aprobado) "
						+" values ('" + datos.getNombrePdv() + "','" + datos.getNombreCliente() + "','" + datos.getTipoDocumento() + "','" + datos.getNumeroDocumento() + "',"
						+ "'" + datos.getCorreo() + "','" + datos.getTelefonoFijo() + "','" + datos.getCelular() + "','" + datos.getTelefonoOp() + "','" + datos.getIdCategoria() + "', "
						+ "'" + datos.getIdCategoriaClasificacion() + "','" + datos.getIdDepartamento() + "','" + datos.getIdMunicipio()+ "','" + datos.getIdCircuito()+ "', "
						+ "'" + datos.getIdRuta()+ "','" + datos.getIdEstadoComercial()+ "','" + datos.getBarrio() + "','" + datos.getDireccion() + "', "
						+ "'" + datos.getLatitud() + "','" + datos.getLongitud() + "',1,curdate(),curtime()," + idUs + "," + idUs + ",now(),1)";
				movimiento = 1;
			}
			
			nativeQuery = em.createNativeQuery(query);
			nativeQuery.executeUpdate();
			
			if(movimiento == 1) {
				nativeQuery = em.createNativeQuery("SELECT LAST_INSERT_ID();");
				BigInteger biid = (BigInteger) nativeQuery.getSingleResult();
				idPos = biid.longValue();
			}

			Map<String, Object> resultadoCampos = asociarCamposDinamicos(datos, movimiento, (int) idPos, idUs);
			if (!(Boolean) resultadoCampos.get("estado")) {
				result.put("estado", false);
				result.put("msg", resultadoCampos.get("msg"));
				return result;
			}			

			query = "INSERT into "+ bd_pos + ".audi_puntos (idpos,razon,nombre_cli,tipo_doc,cedula,email,tel,celular,tel_op,categoria,depto,ciudad,territorio,zona,estado_com,barrio,detalle_direccion,latitud,longitud,movimiento,fecha_crea,hora_crea,usuario_crea) "
						+" values ("+ idPos +",'" + datos.getNombrePdv() + "','" + datos.getNombreCliente() + "','" + datos.getTipoDocumento() + "','" + datos.getNumeroDocumento() + "',"
						+ "'" + datos.getCorreo() + "','" + datos.getTelefonoFijo() + "','" + datos.getCelular() + "','" + datos.getTelefonoOp() + "','" + datos.getIdCategoria() + "', "
						+ "'" + datos.getIdDepartamento() + "','" + datos.getIdMunicipio()+ "','" + datos.getIdCircuito()+ "', "
						+ "'" + datos.getIdRuta()+ "','" + datos.getIdEstadoComercial()+ "','" + datos.getBarrio() + "','" + datos.getDireccion() + "', "
						+ "'" + datos.getLatitud() + "','" + datos.getLongitud() + "'," + movimiento + ", curdate(), curtime(), " + idUs + ")";
			nativeQuery = em.createNativeQuery(query);
			nativeQuery.executeUpdate();
			
			result.put("estado", true);
			result.put("msg", "OK");
			return result;
		}
		catch (Exception e2) {
			//e2.printStackTrace();			
			//LogErrores.escribirLog(e2.getMessage(), query,ruta_log);				
			StringBuilder sb = new StringBuilder();
			sb.append(e2.getMessage()).append("\n");
			for (StackTraceElement element : e2.getStackTrace()) {
				sb.append(element.toString()).append("\n");
			}
			LogErrores.escribirLog(sb.toString(), query, ruta_log);
			result.put("estado", false);
			result.put("msg", e2.getMessage());
			return result;
		}
	}
			
	private Map<String, Object> asociarCamposDinamicos(CrearActualizarPuntosPost datos, int movimiento, int idPos,
			int idUs) {
		String query = "";
		Map<String, Object> result = new HashMap<>();

		// borramos los campos dinamicos cuando se actualiza un punto
		if (movimiento == 2) {
			query = "DELETE FROM " + bd_pos + ".puntos__campos_datos WHERE id_punto = " + idPos;
			nativeQuery = em.createNativeQuery(query);
			nativeQuery.executeUpdate();
		}

		int cantCampos = datos.getCamposDinamicos().size();		
		
		// Consultar campos dinamicos para concatenar en la tabla puntos
		query = "SELECT id, nombre, max_caracteres FROM " + bd_pos + ".puntos__campos WHERE estado = 1 ORDER BY orden";
		nativeQuery = em.createNativeQuery(query);
		List<Object[]> camposDinamicos = nativeQuery.getResultList();
		Map<String, String> camposDinamicosIds = new HashMap<>();
		for (Object[] campo : camposDinamicos) {
			camposDinamicosIds.put(campo[0].toString(), "0");
		}		

		// crear campos dinamicos
		if (cantCampos > 0) {
			String[] values = new String[cantCampos];
			List<String> errores = new ArrayList<String>();

			query = "INSERT into " + bd_pos	+ ".puntos__campos_datos (id_punto, id_campo, dato_numerico, dato_texto, dato_fecha, created_at, user_create, updated_at, user_update, origin_update) VALUES %s";

			for (int i = 0; i < cantCampos; i++) {
				int idTipoCampo = datos.getCamposDinamicos().get(i).getId_tipo();
				int idCampo = datos.getCamposDinamicos().get(i).getId_campo();
				String datoCampo = datos.getCamposDinamicos().get(i).getDato();				

				List<CamposDinamicosPost> campos = datos.getCamposDinamicos();
				Map<String, Object> resultado = validarCampoDinamico(camposDinamicos, campos, i);
				if (!(Boolean) resultado.get("estado")) {
					errores.add((String) resultado.get("msg"));
					continue;
				}

				//actualizamos el campo dinamico en la lista de campos dinamicos
				camposDinamicosIds.put(String.valueOf(idCampo), datoCampo);

				switch ((int) idTipoCampo) {
					case 1: // numerico
						// $sql_campo .= " ($puntoid, {$value["id_campo"]}, {$value["dato"]}, NULL,
						// NULL, NOW(),$id_user,NOW(), $id_user, 0),";
						//long datoCampoInt = Long.parseLong(datoCampo);
						//int datoCampoInt = Integer.parseInt(datoCampo);
						values[i] = String.format("(%d,%d,'%s',NULL,NULL,NOW(),%d,NOW(),%d,0)", idPos, idCampo, datoCampo,
								idUs, idUs);
						break;
					case 2: // select
						// $sql_campo .= "($puntoid, {$value["id_campo"]}, {$value["dato"]}, NULL, NULL,
						// NOW(),$id_user,NOW(), $id_user, 0),";
						values[i] = String.format("(%d,%d,'%s',NULL,NULL,NOW(),%d,NOW(),%d,0)", idPos, idCampo, datoCampo,
								idUs, idUs);
						break;
					case 3: // text
						// $sql_campo .= "($puntoid, {$value["id_campo"]}, 0, '{$value["dato"]}', NULL,
						// NOW(),$id_user,NOW(), $id_user, 0),";
						values[i] = String.format("(%d,%d,0,'%s',NULL,NOW(),%d,NOW(),%d,0)", idPos, idCampo, datoCampo,
								idUs, idUs);
						break;
					case 4: // fecha
						// $sql_campo .= "($puntoid, {$value["id_campo"]}, 0, NULL, '{$value["dato"]}',
						// NOW(),$id_user,NOW(), $id_user, 0),";
						values[i] = String.format("(%d,%d,0,NULL,'%s',NOW(),%d,NOW(),%d,0)", idPos, idCampo, datoCampo,
								idUs, idUs);
						break;
					default:
						break;
				}
			}

			if (errores.size() > 0) {
				result.put("estado", false);
				String erroresStr = String.join(",\n", errores);
				result.put("msg", erroresStr);
				return result;
			}

			if (values.length > 0) {
				String valuesStr = String.join(",", values);
				query = String.format(query, valuesStr);
				nativeQuery = em.createNativeQuery(query);
				nativeQuery.executeUpdate();
			}
		}
		
		setCamposDinamicosPunto(camposDinamicosIds, idPos);

		result.put("estado", true);
		result.put("msg", "OK");
		return result;
	}

	private Map<String, Object> setCamposDinamicosPunto(Map<String, String> campos, int idPos) {
		Map<String, Object> result = new HashMap<>();
		String query = "";
		try {		
			String strCampos = campos.entrySet().stream()
					.map(entry -> entry.getKey() + ":" + entry.getValue())
					.collect(Collectors.joining(";"));
			if (strCampos.length() == 0) {
				query = String.format("UPDATE %s.puntos SET campos_dinamicos = NULL WHERE idpos = %d LIMIT 1", bd_pos, idPos);
			} else {
				// Es importante escapar las comillas simples dentro de strCampos si contiene alguna
				String escapedStrCampos = strCampos.replace("'", "''");
				query = String.format("UPDATE %s.puntos SET campos_dinamicos = '%s' WHERE idpos = %d LIMIT 1", bd_pos, escapedStrCampos, idPos);
			}
			nativeQuery = em.createNativeQuery(query);
			nativeQuery.executeUpdate();
			result.put("estado", true);
			result.put("msg", "OK");
			return result;
		} catch (Exception ex) {
			ex.printStackTrace();	
			StringBuilder sb = new StringBuilder();
			sb.append(ex.getMessage()).append("\n");
			for (StackTraceElement element : ex.getStackTrace()) {
				sb.append(element.toString()).append("\n");
			}
			LogErrores.escribirLog(sb.toString(), query, ruta_log);
			result.put("estado", false);
			result.put("msg", "Error al guardar campos dinamicos serializados.\n" + ex.getMessage());
			return result;
		}
	}

	private Map<String, Object> validarCampoDinamico(List<Object[]> camposDinamicos, List<CamposDinamicosPost> datos, int index) {
		Map<String, Object> result = new HashMap<>();
		try {
			int idTipoCampo = datos.get(index).getId_tipo();
			int idCampo = datos.get(index).getId_campo();
			String datoCampo = datos.get(index).getDato();
			
			Optional<Object[]> resultado = camposDinamicos.stream()
				.filter(campo -> campo[0] != null && campo[0].equals(idCampo))
				.findFirst();
		
			if (resultado.isPresent()) {
				Object[] filaEncontrada = resultado.get();
				Integer maxCaracteres = filaEncontrada[2] != null ? (Integer) filaEncontrada[2] : null; // max_caracteres
				String nombre = (String) filaEncontrada[1]; // nombre
				
				if (idTipoCampo == 3 && maxCaracteres != null && maxCaracteres > 0
						&& datoCampo.length() > maxCaracteres) {
					result.put("estado", false);
					result.put("msg", "El campo " + nombre + " excede el limite de caracteres permitidos");
					return result;
				}

				if (idTipoCampo == 1 && Long.parseLong(datoCampo.toString()) > Long.MAX_VALUE) { // validar que el valor no exceda el maximo permitido por la db
					result.put("estado", false);
					result.put("msg", "El campo " + nombre + " excede valor limite para un campo numerico");
					return result;
				}

				result.put("estado", true);
				result.put("msg", "OK");
			} else {
				result.put("estado", false);
				result.put("msg", "El campo " + idCampo + " no existe o esta desactivado en la base de datos");
			}
			return result;
		} catch (Exception ex) {		
			ex.printStackTrace();	
			StringBuilder sb = new StringBuilder();
			sb.append(ex.getMessage()).append("\n");
			for (StackTraceElement element : ex.getStackTrace()) {
				sb.append(element.toString()).append("\n");
			}
			LogErrores.escribirLog(sb.toString(), datos.toString(), ruta_log);
			result.put("estado", false);
			result.put("msg", "Error al validar el campo.\n" + ex.getMessage());
			return result;
		}
	}
	
	public Map<String, Object> camposDinamicosPunto(int idPos) {
		String query = "SELECT pcd.id, pcd.id_punto, pc.tipo_dato, pcd.id_campo, "
				+ "CASE "
				+ "WHEN pc.tipo_dato = 1 THEN pcd.dato_numerico "
				+ "WHEN pc.tipo_dato = 2 THEN pcl.nombre "
				+ "WHEN pc.tipo_dato = 3 THEN pcd.dato_texto "
				+ "WHEN pc.tipo_dato = 4 THEN pcd.dato_fecha "
				+ "ELSE NULL "
				+ "END AS dato, "
				+ "pcl.id AS id_lista "
				+ "FROM " + bd_pos + ".puntos__campos AS pc "
				+ "INNER JOIN " + bd_pos + ".puntos__campos_datos AS pcd ON (pc.id = pcd.id_campo AND pc.estado = 1) "
				+ "LEFT JOIN " + bd_pos + ".puntos__campos_listas AS pcl ON (pcl.id_campo = pcd.id_campo AND pc.tipo_dato = 2 AND pcl.id = pcd.dato_numerico) "
				+ "WHERE pcd.id_punto = " + idPos + " ORDER BY pc.orden";

		Map<String, Object> result = new HashMap<>();
		try {
			nativeQuery = em.createNativeQuery(query);
			List<Object> campos = nativeQuery.getResultList();

			if (campos.size() > 0) {
				List<Object> camposDinamicosPuntoList = new ArrayList<>();
				for (Object campo : campos) {
					Object[] campoArray = (Object[]) campo;
					Map<String, Object> camposDinamicosPunto = new HashMap<>();
					camposDinamicosPunto.put("id", campoArray[0]);
					camposDinamicosPunto.put("id_punto", campoArray[1]);
					camposDinamicosPunto.put("tipo_dato", campoArray[2]);
					camposDinamicosPunto.put("id_campo", campoArray[3]);
					camposDinamicosPunto.put("dato", campoArray[4]);
					camposDinamicosPunto.put("id_lista", campoArray[5]);					

					camposDinamicosPuntoList.add(camposDinamicosPunto);
				}
				result.put("estado", true);
				result.put("msg", "OK");
				result.put("datos", camposDinamicosPuntoList);
			} else {
				result.put("estado", false);
				result.put("msg", "No se encontraron campos dinamicos para el punto.");
				result.put("datos",null);
			}
		} catch (Exception ex) {
			ex.printStackTrace();	
			StringBuilder sb = new StringBuilder();
			sb.append(ex.getMessage()).append("\n");
			for (StackTraceElement element : ex.getStackTrace()) {
				sb.append(element.toString()).append("\n");
			}
			LogErrores.escribirLog(sb.toString(), query, ruta_log);
			result.put("estado", false);
			result.put("msg", "Error al consultar los campos dinamicos.\n" + ex.getMessage());
			result.put("datos", sb.toString());
		}
		return result;
	}
	
	public Map<String, Object> camposDinamicos() {
		String query = "";
		Map<String, Object> result = new HashMap<>();
		try {			
			Map<String, Object> datos = new HashMap<>();

			// Consultar campos
			List<Map<String, Object>> campos = new ArrayList<>();
			query = "SELECT id, nombre, tipo_dato, max_caracteres, orden FROM " +
					bd_pos + ".puntos__campos WHERE estado = 1 ORDER BY orden";
			nativeQuery = em.createNativeQuery(query);
			List<Object[]> resultCampos = nativeQuery.getResultList();

			if (resultCampos.size() > 0) {
				for (Object campo : resultCampos) {
					Object[] campoArray = (Object[]) campo;
					Map<String, Object> campoDinamico = new HashMap<>();
					campoDinamico.put("id", campoArray[0]);
					campoDinamico.put("nombre", campoArray[1]);
					campoDinamico.put("tipo_dato", campoArray[2]);
					campoDinamico.put("max_caracteres", campoArray[3]);
					campoDinamico.put("orden", campoArray[4]);

					campos.add(campoDinamico);
				}

			} 
			datos.put("campos_dinamicos", campos);

			// Consultar listas
			List<Map<String, Object>> listas = new ArrayList<>();
			query = "SELECT id, nombre, id_campo FROM " + bd_pos + ".puntos__campos_listas WHERE estado = 1 ";
			nativeQuery = em.createNativeQuery(query);
			List<Object[]> resultListas = nativeQuery.getResultList();
			
			if (resultListas.size() > 0) {
				for (Object lista : resultListas) {
					Object[] listaArray = (Object[]) lista;
					Map<String, Object> listaDinamica = new HashMap<>();
					listaDinamica.put("id", listaArray[0]);
					listaDinamica.put("nombre", listaArray[1]);
					listaDinamica.put("id_campo", listaArray[2]);

					listas.add(listaDinamica);
				}
			} 			
			datos.put("listas", listas);

			if (datos.isEmpty()) {
				result.put("estado", false);
				result.put("msg", "No se encontraron campos dinamicos.");
				result.put("datos", null);
			} else {
				result.put("estado", true);
				result.put("msg", "OK");
				result.put("datos", datos);
			}
		} catch (Exception ex) {
			ex.printStackTrace();	
			StringBuilder sb = new StringBuilder();
			sb.append(ex.getMessage()).append("\n");
			for (StackTraceElement element : ex.getStackTrace()) {
				sb.append(element.toString()).append("\n");
			}
			LogErrores.escribirLog(sb.toString(), query, ruta_log);
			result.put("estado", false);
			result.put("msg", "Error al consultar los campos dinamicos.\n" + ex.getMessage());
			result.put("datos", sb.toString());
		}
		return result;
	}
}
