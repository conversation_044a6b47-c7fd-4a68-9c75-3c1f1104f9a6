package com.apirestclaro.dao;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.apirestclaro.entitys.Encuesta;
import com.apirestclaro.entitys.EncuestaOperador;
import com.apirestclaro.entitys.NivelesDcs;
import com.apirestclaro.entitys.Preguntas;
import com.apirestclaro.entitys.PreguntasOperador;
import com.apirestclaro.entitys.Respuestas;
import com.apirestclaro.entitys.RespuestasOperador;
import com.apirestclaro.entitys.Usuario;
import com.apirestclaro.functions.Functions;
import com.apirestclaro.pojos.EncuestaRespondida;
import com.apirestclaro.pojos.FormularioDinamico;
import com.serviciologerrores.functions.LogErrores;

@Repository
public class EncuestaDao {

	@Value("${operador.bd}")
	private String bd;

	@Value("${operador.bd_pos}")
	private String bd_pos;

	@Value("${operador.ruta_log}")
	private String ruta_log;

	@PersistenceContext
	private EntityManager em;

	private Query nativeQuery;

	public Map<String, Object> verEncuestas(Usuario us, String idEncuestas) {

		List<Encuesta> encuestas = new ArrayList<>();
		List<Preguntas> preguntas = new ArrayList<>();
		List<Respuestas> respuestas = new ArrayList<>();

		Map<String, Object> response = new HashMap<String, Object>();

		String query = "";

		try {

			String permisosRuta = Functions.getPermisosRuta();
			Map<String, Object> encuestasObject = this.compararPermisosDcsEncuestas(permisosRuta, idEncuestas, us);
			String idEncuestasInsertar = encuestasObject.get("idEncuestasInsertar").toString();
			String idEncuestasEliminar = encuestasObject.get("idEncuestasEliminar").toString();

			if (!idEncuestasInsertar.isEmpty()) {

				///// consulta las encuestas
				query = "select enc.id,enc.titulo,enc.descripcion,enc.obligatorio,enc.navegar_atras, enc.aplicar, 1 AS estado_accion from enc__encuesta as enc "
						+ "inner join enc__asignacion_operador as aop on (aop.id_encuesta = enc.id and aop.id_perfil = " + us.getIdPerfil() + ") " 
						+ "where enc.vigente = 1 AND enc.estado = 1 AND enc.id IN ("
						+ idEncuestasInsertar + ") group by enc.id";
				// System.out.println(query);

				nativeQuery = em.createNativeQuery(query, Encuesta.class);
				encuestas = nativeQuery.getResultList();

				if (encuestas.size() > 0) {
					//// consulta las preguntas
					String idencuestas = "";
					for (Encuesta enc : encuestas) {
						idencuestas = idencuestas + enc.getId() + ",";
					}
					idencuestas = idencuestas.substring(0, idencuestas.length() - 1);

					query = "select id,pregunta,id_encuesta,tipo,id_respuesta,obligatorio,orden from enc__preguntas where estado = 1 and id_encuesta in ("
							+ idencuestas + ") order by id_encuesta,orden";
					nativeQuery = em.createNativeQuery(query, Preguntas.class);
					preguntas = nativeQuery.getResultList();

					String idpreguntas = "";
					for (Preguntas pre : preguntas) {
						idpreguntas = idpreguntas + pre.getId() + ",";
					}
					idpreguntas = idpreguntas.substring(0, idpreguntas.length() - 1);

					query = "select id,respuesta,id_pregunta,id_encuesta,orden from enc__opciones_resp where estado = 1 and id_encuesta in ("
							+ idencuestas + ") and id_pregunta in (" + idpreguntas + ") order by id_pregunta,orden";
					nativeQuery = em.createNativeQuery(query, Respuestas.class);
					respuestas = nativeQuery.getResultList();

				}

			}

			if (!idEncuestas.isEmpty()) {
				query = "select enc.id,enc.titulo,enc.descripcion,enc.obligatorio,enc.navegar_atras, enc.aplicar, 0 AS estado_accion from enc__encuesta as enc WHERE (enc.estado = 0 OR enc.vigente = 0) AND enc.id IN ("
						+ idEncuestas + ")";

				nativeQuery = em.createNativeQuery(query, Encuesta.class);
				List<Encuesta> encuestas2 = nativeQuery.getResultList();

				if (encuestas2.size() > 0) {
					encuestas.addAll(encuestas2); // Merge both lists
				}

			}

			if (!idEncuestasEliminar.isEmpty()) {
				query = "select enc.id,enc.titulo,enc.descripcion,enc.obligatorio,enc.navegar_atras, enc.aplicar, 0 AS estado_accion from enc__encuesta as enc WHERE enc.id IN ("
						+ idEncuestasEliminar + ")";

				nativeQuery = em.createNativeQuery(query, Encuesta.class);
				List<Encuesta> encuestas2 = nativeQuery.getResultList();

				if (encuestas2.size() > 0) {
					encuestas.addAll(encuestas2); // Merge both lists
				}
			}

			List<Encuesta> noDuplicadosEncuestas = encuestas.stream().distinct().collect(Collectors.toList());

			response.put("respuestas", respuestas);
			response.put("preguntas", preguntas);
			response.put("encuestas", noDuplicadosEncuestas);

			return response;

		} catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getMessage(), query, ruta_log);
			return null;
		}

	}

	public Map<String, Object> verEncuestasOperador(Usuario us, String idEncuestas) {

		List<EncuestaOperador> encuestas = new ArrayList<>();
		List<PreguntasOperador> preguntas = new ArrayList<>();
		List<RespuestasOperador> respuestas = new ArrayList<>();

		Map<String, Object> response = new HashMap<String, Object>();

		String query = "";

		try {

			String permisosRuta = Functions.getPermisosRuta();
			Map<String, Object> encuestasObject = this.compararPermisosDcsEncuestasOp(permisosRuta, idEncuestas, us);
			String idEncuestasInsertar = encuestasObject.get("idEncuestasInsertar").toString();
			String idEncuestasEliminar = encuestasObject.get("idEncuestasEliminar").toString();

			if (!idEncuestasInsertar.isEmpty()) {

				///// consulta las encuestas
				query = "SELECT enc.id as id_encuesta,enc.titulo,enc.estado as estado_encuesta,enc.descripcion as descripcion_encuesta,"
						+ "'0000-00-00' as fecha_crea,'00:00:00' as hora_crea,enc.fecha_inicio, enc.fecha_fin,enc.vigente,ap.obligatorio,enc.navegar_atras,"
						+ "1 AS estado_accion,ap.aplica as aplicar " 
						+ "FROM frm__encuesta as enc "
						+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = enc.id AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
						+ "WHERE enc.vigente = 1 AND enc.estado = 1 AND enc.id IN ("
						+ idEncuestasInsertar + ") group by enc.id";
				// System.out.println(query);

				nativeQuery = em.createNativeQuery(query, EncuestaOperador.class);
				encuestas = nativeQuery.getResultList();

				if (encuestas.size() > 0) {
					//// consulta las preguntas
					String idencuestas = "";
					for (EncuestaOperador enc : encuestas) {
						idencuestas = idencuestas + enc.getId() + ",";
					}
					idencuestas = idencuestas.substring(0, idencuestas.length() - 1);

					query = "SELECT id as id_pregunta,pregunta,id_encuesta,tipo as tipo_pregunta,obligatorio, "
							+ "concat(id_seccion,orden) as orden,id_respuesta,0 as evidencia,0 as evidencia_obligatoria,0 as evidencia_observacion "
							+ "from frm__preguntas WHERE id_encuesta in (" + idencuestas
							+ ") order by id_encuesta,orden";
					nativeQuery = em.createNativeQuery(query, PreguntasOperador.class);
					preguntas = nativeQuery.getResultList();

					String idpreguntas = "";
					for (PreguntasOperador pre : preguntas) {
						idpreguntas = idpreguntas + pre.getId() + ",";
					}
					idpreguntas = idpreguntas.substring(0, idpreguntas.length() - 1);

					query = "SELECT id as id_respuesta,respuesta,id_pregunta,id_encuesta,orden, foto as evidencia,foto_obligatoria as evidencia_obligatoria,observacion_foto as evidencia_observacion "
							+ "FROM frm__respuestas WHERE id_encuesta in (" + idencuestas + ") and id_pregunta in ("
							+ idpreguntas + ") order by id_pregunta,orden";
					nativeQuery = em.createNativeQuery(query, RespuestasOperador.class);
					respuestas = nativeQuery.getResultList();

				}

			}

			if (!idEncuestas.isEmpty()) {
				query = "SELECT enc.id as id_encuesta,enc.titulo,enc.estado as estado_encuesta,enc.descripcion as descripcion_encuesta,"
						+ "'0000-00-00' as fecha_crea,'00:00:00' as hora_crea,enc.fecha_inicio, enc.fecha_fin,enc.vigente,enc.navegar_atras,"
						+ "0 AS estado_accion,0 as aplicar, 0 as obligatorio " 
						+ "FROM frm__encuesta as enc "
						+ "WHERE (enc.estado = 0 OR enc.vigente = 0) AND enc.id IN (" + idEncuestas + ")";

				nativeQuery = em.createNativeQuery(query, EncuestaOperador.class);
				List<EncuestaOperador> encuestas2 = nativeQuery.getResultList();

				if (encuestas2.size() > 0) {
					encuestas.addAll(encuestas2); // Merge both lists
				}

			}

			if (!idEncuestasEliminar.isEmpty()) {
				query = "SELECT enc.id as id_encuesta,enc.titulo,enc.estado as estado_encuesta,enc.descripcion as descripcion_encuesta, "
					+ "'0000-00-00' as fecha_crea,'00:00:00' as hora_crea,enc.fecha_inicio, enc.fecha_fin,enc.vigente,0 AS obligatorio,enc.navegar_atras, 1 as aplicar, 0 AS estado_accion "
					+ "from frm__encuesta as enc "
					+ "WHERE enc.id IN (" + idEncuestasEliminar + ")";

				nativeQuery = em.createNativeQuery(query, EncuestaOperador.class);
				List<EncuestaOperador> encuestas2 = nativeQuery.getResultList();

				if (encuestas2.size() > 0) {
					encuestas.addAll(encuestas2); // Merge both lists
				}
			}

			List<EncuestaOperador> noDuplicadosEncuestas = encuestas.stream().distinct().collect(Collectors.toList());

			response.put("respuestas", respuestas);
			response.put("preguntas", preguntas);
			response.put("encuestas", noDuplicadosEncuestas);

			return response;

		} catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getMessage(), query, ruta_log);
			return null;
		}

	}

	private Map<String, Object> compararPermisosDcsEncuestas(String permisosRuta, String idEncuestas, Usuario us) {
		String query = "";
		String filtro = "";
		String idsEncuestas = "";
		String idsEncuestasEliminar = "";
		Map<String, Object> response = new HashMap<String, Object>();
		try {
			if (!idEncuestas.isEmpty()) {
				filtro += "AND nd.id_encuesta NOT IN (" + idEncuestas + ")";
			}

			query = "SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "
					+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") " 
					+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_regional) "
					+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN ("+ permisosRuta + ")) "
					+ "WHERE nd.nivel = 1 AND nd.tipo = 1 " + filtro
					+ " GROUP BY nd.id_encuesta "
					+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "					
					+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") " 
					+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_distri) "
					+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) "
					+ "WHERE nd.nivel = 1 AND nd.tipo = 2 " + filtro
					+ " GROUP BY nd.id_encuesta "
					+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "					
					+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") "
					+ "INNER JOIN zonas z on (z.id = nd.id_tipo AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) "
					+ "WHERE nd.nivel = 1 AND nd.tipo = 4  " + filtro
					+ " GROUP BY nd.id_encuesta "
					+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "					
					+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") "
					+ "INNER JOIN territorios t on (nd.id_tipo = t.id) "
					+ "INNER JOIN zonas z on (t.id = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) "
					+ "WHERE nd.nivel = 1 AND nd.tipo = 3 " + filtro;

			nativeQuery = em.createNativeQuery(query);

			List<Object[]> encuestasIds = (List<Object[]>) nativeQuery.getResultList();

			if (!encuestasIds.isEmpty()) {
				for (Object[] idEncuestasO : encuestasIds) {
					idsEncuestas += idEncuestasO[0] + ",";
				}

				idsEncuestas = idsEncuestas.substring(0, idsEncuestas.length() - 1);
			}

			if (!idEncuestas.isEmpty()) {

				query = "SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "
						+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") "
						+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_regional) "
						+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) "
						+ "WHERE nd.nivel = 1 AND nd.tipo = 1 AND nd.id_encuesta IN (" + idEncuestas
						+ ") GROUP BY nd.id_encuesta "
						+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "
						+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") "
						+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_distri) "
						+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.nivel = 1 AND nd.tipo = 2 AND nd.id_encuesta IN (" + idEncuestas
						+ ") GROUP BY nd.id_encuesta "
						+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "
						+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") "
						+ "INNER JOIN zonas z on (z.id = nd.id_tipo AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.nivel = 1 AND nd.tipo = 4 AND nd.id_encuesta IN (" + idEncuestas
						+ ") GROUP BY nd.id_encuesta "
						+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "
						+ "INNER JOIN enc__asignacion_operador as aop on (aop.id_encuesta = nd.id_encuesta and aop.id_perfil = " + us.getIdPerfil() + ") "
						+ "INNER JOIN territorios t on (nd.id_tipo = t.id) "
						+ "INNER JOIN zonas z on (t.id = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.nivel = 1 AND nd.tipo = 3 AND nd.id_encuesta IN (" + idEncuestas + ")";
				nativeQuery = em.createNativeQuery(query);

				// nativeQuery = em.createNativeQuery(query);
				List<Object[]> encuestasIdsE = (List<Object[]>) nativeQuery.getResultList();
				String idEncuestasValidar = "";

				if (!encuestasIdsE.isEmpty()) {
					for (Object[] idEncuestaE : encuestasIdsE) {
						idEncuestasValidar += idEncuestaE[0] + ",";
					}

					idEncuestasValidar = idEncuestasValidar.substring(0, idEncuestasValidar.length() - 1);

					String[] idEncuestaArray1 = idEncuestas.split(",");
					String[] idEncuestaArray2 = idEncuestasValidar.split(",");
					if (idEncuestaArray1.length > 0) {
						for (int i = 0; i < idEncuestaArray1.length; i++) {
							boolean contains = Arrays.stream(idEncuestaArray2).anyMatch(idEncuestaArray1[i]::equals);
							if (!contains) {
								idsEncuestasEliminar += idEncuestaArray1[i] + ",";
							}
						}
					}

					if (!idsEncuestasEliminar.isEmpty()) {
						idsEncuestasEliminar = idsEncuestasEliminar.substring(0, idsEncuestasEliminar.length() - 1);
					}
				} else {
					idsEncuestasEliminar = idEncuestas;
				}

			}

			response.put("idEncuestasInsertar", idsEncuestas);
			response.put("idEncuestasEliminar", idsEncuestasEliminar);

			return response;
		} catch (NoResultException e) {
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query, ruta_log);
			return null;
		}
	}

	private Map<String, Object> compararPermisosDcsEncuestasOp(String permisosRuta, String idEncuestas, Usuario us) {
		String query = "";
		String filtro = "";
		String idsEncuestas = "";
		String idsEncuestasEliminar = "";
		Map<String, Object> response = new HashMap<String, Object>();
		try {

			if (!idEncuestas.isEmpty()) {
				filtro += "AND nd.id_encuesta NOT IN (" + idEncuestas + ")";
			}

			query = "SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
					+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
					+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_regional) "
					+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN ("
					+ permisosRuta + ")) " + "WHERE nd.tipo = 1 " + filtro + " GROUP BY nd.id_encuesta "

					+ "UNION SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
					+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
					+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_distri) "
					+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN ("
					+ permisosRuta + ")) " + "WHERE nd.tipo = 2 " + filtro + " GROUP BY nd.id_encuesta "

					+ "UNION SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
					+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
					+ "INNER JOIN zonas z on (z.id = nd.id_tipo AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) "
					+ "WHERE nd.tipo = 4 " + filtro + " GROUP BY nd.id_encuesta "

					+ "UNION SELECT nd.id_encuesta, z.descripcion FROM enc__niveles_dcs nd "
					+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
					+ "INNER JOIN territorios t on (nd.id_tipo = t.id) "
					+ "INNER JOIN zonas z on ( t.id = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta
					+ ")) " + "WHERE nd.tipo = 3 " + filtro;

			nativeQuery = em.createNativeQuery(query);

			nativeQuery = em.createNativeQuery(query);
			List<Object[]> encuestasIds = (List<Object[]>) nativeQuery.getResultList();

			if (!encuestasIds.isEmpty()) {
				for (Object[] idEncuestasO : encuestasIds) {
					idsEncuestas += idEncuestasO[0] + ",";
				}

				idsEncuestas = idsEncuestas.substring(0, idsEncuestas.length() - 1);
			}

			if (!idEncuestas.isEmpty()) {

				query = "SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
						+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
						+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_regional) "
						+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.tipo = 1 AND nd.id_encuesta IN (" + idEncuestas
						+ ") GROUP BY nd.id_encuesta "

						+ "UNION SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
						+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
						+ "INNER JOIN territorios_distribuidor td on (nd.id_tipo = td.id_distri) "
						+ "INNER JOIN zonas z on (td.id_territorio = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.tipo = 2  AND nd.id_encuesta IN (" + idEncuestas
						+ ") GROUP BY nd.id_encuesta "

						+ "UNION SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
						+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
						+ "INNER JOIN zonas z on (z.id = nd.id_tipo AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.tipo = 4 AND nd.id_encuesta IN (" + idEncuestas
						+ ") GROUP BY nd.id_encuesta "

						+ "UNION SELECT nd.id_encuesta, z.descripcion FROM frm__niveles_dcs nd "
						+ "INNER JOIN frm__asignacion_perfiles as ap on (ap.id_encuesta = nd.id_encuesta AND ap.nivel = 1 AND ap.id_perfil = " + us.getIdPerfil() + ") " 
						+ "INNER JOIN territorios t on (nd.id_tipo = t.id) "
						+ "INNER JOIN zonas z on (t.id = z.territorio AND z.estado = 1 AND z.id IN (" + permisosRuta + ")) " 
						+ "WHERE nd.tipo = 3 AND nd.id_encuesta IN (" + idEncuestas + ")";

				nativeQuery = em.createNativeQuery(query);

				// nativeQuery = em.createNativeQuery(query);
				List<Object[]> encuestasIdsE = (List<Object[]>) nativeQuery.getResultList();
				String idEncuestasValidar = "";

				if (!encuestasIdsE.isEmpty()) {
					for (Object[] idEncuestaE : encuestasIdsE) {
						idEncuestasValidar += idEncuestaE[0] + ",";
					}

					idEncuestasValidar = idEncuestasValidar.substring(0, idEncuestasValidar.length() - 1);

					String[] idEncuestaArray1 = idEncuestas.split(",");
					String[] idEncuestaArray2 = idEncuestasValidar.split(",");
					if (idEncuestaArray1.length > 0) {
						for (int i = 0; i < idEncuestaArray1.length; i++) {
							boolean contains = Arrays.stream(idEncuestaArray2).anyMatch(idEncuestaArray1[i]::equals);
							if (!contains) {
								idsEncuestasEliminar += idEncuestaArray1[i] + ",";
							}
						}
					}

					if (!idsEncuestasEliminar.isEmpty()) {
						idsEncuestasEliminar = idsEncuestasEliminar.substring(0, idsEncuestasEliminar.length() - 1);
					}
				} else {
					idsEncuestasEliminar = idEncuestas;
				}

			}

			response.put("idEncuestasInsertar", idsEncuestas);
			response.put("idEncuestasEliminar", idsEncuestasEliminar);

			return response;
		} catch (NoResultException e) {
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query, ruta_log);
			return null;
		}
	}

	public List<NivelesDcs> buscarNiveles(Long idUs) {

		String query = "";
		try {
			query = "select id,tipo,group_concat(id_tipo) as id_tipos, 1 as accion, p2 from niveles_dcs where id_usus = "
					+ idUs + " group by tipo";

			nativeQuery = em.createNativeQuery(query, NivelesDcs.class);

			return nativeQuery.getResultList();
		} catch (NoResultException e) {
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query, ruta_log);
			return null;
		}
	}

	@Transactional
	public Map<String, Object> guardarEncuesta(List<EncuestaRespondida> datos, Map<String, Object> datosVisita,
			long idUsuario, int idPerfil, long idVisita) {
		Map<String, Object> infoEncuesta = new HashMap<String, Object>();

		String query = "";
		String queryEncuesta = "";
		String queryVisita = "";
		String queryConcat = "";
		long idPos = 0;
		long idCircuito = 0;
		long idRuta = 0;
		long idRegional = 0;
		long idDistribuidor = 0;
		long tipoEncuestado = 1;
		Boolean error = false;
		String fecha = null;
		String hora = null;

		try {

			if (idVisita > 0) {
				idPos = Long.parseLong(datosVisita.get("idPdv").toString());
				idCircuito = Long.parseLong(datosVisita.get("idCircuito").toString());
				idRuta = Long.parseLong(datosVisita.get("idRuta").toString());
				idRegional = Long.parseLong(datosVisita.get("idRegional").toString());
				idDistribuidor = Long.parseLong(datosVisita.get("idDistribuidor").toString());
				tipoEncuestado = 2;
			}

			for (EncuestaRespondida encuesta : datos) {
				long idEncuesta = encuesta.getIdEncuesta();
				long idPregunta = encuesta.getIdPregunta();
				long idRespuesta = encuesta.getIdRespuesta();
				String respuestaTexto = encuesta.getRespuestaTexto();
				fecha = encuesta.getFechaDetalle();
				hora = encuesta.getHoraDetalle();

				queryConcat += "(" + tipoEncuestado + ", " + idPos + ", " + idUsuario + ", " + idPerfil + ", "
						+ idPregunta + ", " + idRespuesta + ", '" + respuestaTexto + "', " + idEncuesta + ", '" + fecha
						+ "', '" + hora + "', " + idRegional + ", " + idCircuito + ", " + idRuta + ", " + idDistribuidor
						+ ", 6, " + idVisita + "),";
			}

			if (!queryConcat.isEmpty()) {

				queryConcat = queryConcat.substring(0, queryConcat.length() - 1);

				queryEncuesta = "INSERT INTO enc__respuestas_encuesta (tipo_encuestado, id_encuestado, id_usuario, tipo_usuario, id_pregunta, id_opcion, txt_opcion, id_encuesta, fecha, hora, regional, territorio, zona, distri, origen, id_visita) "
						+ "VALUES " + queryConcat;

				query = queryEncuesta;
				nativeQuery = em.createNativeQuery(queryEncuesta);
				int insertEncuesta = nativeQuery.executeUpdate();

				if (insertEncuesta == 0) {
					query = queryEncuesta;
					error = true;
				}

				queryVisita = "INSERT INTO " + bd_pos
						+ ".visitas_detalle_operador (`fecha`, `hora`, `proceso`, `id_check`) " + "VALUES ('" + fecha
						+ "', '" + hora + "', 1, " + idVisita + ")";

				query = queryVisita;
				nativeQuery = em.createNativeQuery(queryVisita);
				int insertVisitaDeralle = nativeQuery.executeUpdate();

				if (insertVisitaDeralle == 0) {
					error = true;
				}

				if (!error) {
					infoEncuesta.put("estado", 1);
					infoEncuesta.put("mensaje", "Se guardó con éxito la encuesta");
				} else {
					infoEncuesta.put("estado", 0);
					infoEncuesta.put("mensaje", "Hubo un error al tratar de guardar los datos de la encuesta");
					LogErrores.escribirLog("", query, ruta_log);
				}

			} else {
				infoEncuesta.put("estado", 0);
				infoEncuesta.put("mensaje", "Hubo un error en los datos de la encuesta");
			}

			return infoEncuesta;
		} catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query, ruta_log);
			return null;
		}

	}

	@Transactional
	public Map<String, Object> guardarFormularioDinamico(List<FormularioDinamico> datos,
			Map<String, Object> datosVisita, long idUsuario, int idPerfil, long idVisita) {
		Map<String, Object> infoEncuesta = new HashMap<String, Object>();

		String query = "";
		String queryFrm = "";
		String queryVisita = "";
		String queryConcat = "";
		long idPos = 0;
		long idCircuito = 0;
		long idRuta = 0;
		long idRegional = 0;
		long idDistribuidor = 0;
		long tipoEncuestado = 1;
		Boolean error = false;
		String fecha = null;
		String hora = null;

		try {

			if (idVisita > 0) {
				idPos = Long.parseLong(datosVisita.get("idPdv").toString());
				idCircuito = Long.parseLong(datosVisita.get("idCircuito").toString());
				idRuta = Long.parseLong(datosVisita.get("idRuta").toString());
				idRegional = Long.parseLong(datosVisita.get("idRegional").toString());
				idDistribuidor = Long.parseLong(datosVisita.get("idDistribuidor").toString());
				tipoEncuestado = 2;
				fecha = datosVisita.get("fechaInicial").toString();
				hora = datosVisita.get("horaCierre").toString();
			}

			int idFrm = 0;
			long idEncuesta = 0;
			long idPregunta = 0;
			long idRespuesta = 0;
			String respuestaTexto = "";
			long idApp = 0;

			for (FormularioDinamico frm : datos) {
				idFrm = 0;
				idEncuesta = frm.getIdEncuesta();
				idPregunta = frm.getIdPregunta();
				idRespuesta = frm.getIdRespuesta();
				respuestaTexto = frm.getRespuestaTexto();
				idApp = frm.getId();

				try {
					queryFrm = "SELECT id from frm__encuestas_respondidas where origen = 1 and tipo_encuestado = 2 and id_encuestado = "
							+ idPos + " and id_encuesta = " + idEncuesta + " and fecha = '" + fecha + "' and id_visita = "
							+ idVisita;

					nativeQuery = em.createNativeQuery(queryFrm);
					idFrm = (int) nativeQuery.getSingleResult();
				}
				catch(NoResultException e) {
					idFrm = 0;
				}
				if (idFrm <= 0) {
					queryFrm = "INSERT into frm__encuestas_respondidas (origen, tipo_encuestado, id_encuestado, tipo_usuario, id_usuario, id_encuesta, fecha, hora, regional, distri,territorio, zona, plataforma, id_visita,tipo_usuario_text) "
							+ "values (1, 2, " + idPos + "," + idPerfil + "," + idUsuario + "," + idEncuesta + ",'"
							+ fecha + "','" + hora + "'," + idRegional + "," + idDistribuidor + "," + idCircuito + ","
							+ idRuta + ",1," + idVisita + ",(SELECT descripcion from perfiles where id = " + idPerfil + "))";

					nativeQuery = em.createNativeQuery(queryFrm);
					if (nativeQuery.executeUpdate() > 0) {
						nativeQuery = em.createNativeQuery("SELECT LAST_INSERT_ID();");
						BigInteger  biid = (BigInteger) nativeQuery.getSingleResult();
						idFrm = biid.intValue();
					}
				}
				// idVisita = biid.longValue();
				queryFrm = "INSERT INTO frm__ids_respuestas (id_enc_respondida, id_pregunta, id_respuesta, txt_respuesta,id_encuestaresp_app) VALUES "
						+ "(" + idFrm + "," + idPregunta + ", " + idRespuesta + ", '" + respuestaTexto + "', " + idApp
						+ ")";
				query = queryFrm;
				nativeQuery = em.createNativeQuery(queryFrm);
				nativeQuery.executeUpdate();
			}

			if (idFrm > 0) {

				queryVisita = "INSERT INTO " + bd_pos
						+ ".visitas_detalle_operador (`fecha`, `hora`, `proceso`, `id_check`) VALUES ('" + fecha
						+ "', '" + hora + "', 1, " + idVisita + ")";

				query = queryVisita;
				nativeQuery = em.createNativeQuery(queryVisita);
				int insertVisitaDeralle = nativeQuery.executeUpdate();

				if (insertVisitaDeralle == 0) {
					error = true;
				}

				if (!error) {
					infoEncuesta.put("estado", 1);
					infoEncuesta.put("mensaje", "Se guardó con éxito la encuesta");
				} else {
					infoEncuesta.put("estado", 0);
					infoEncuesta.put("mensaje", "Hubo un error al tratar de guardar los datos de la encuesta");
					LogErrores.escribirLog("", query, ruta_log);
				}

			} else {
				infoEncuesta.put("estado", 0);
				infoEncuesta.put("mensaje", "Hubo un error en los datos de la encuesta");
			}

			return infoEncuesta;
		} catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query, ruta_log);
			return null;
		}

	}
	
	@Transactional
	public boolean guardarNombreImg(long idApp, String nombreImagen) {
		String queryFrm = "";
		try {
			queryFrm = "UPDATE " + bd + ".frm__ids_respuestas set imagen = '" + nombreImagen + "' where id_encuestaresp_app = '" + idApp + "'";
			System.out.println(queryFrm);
			nativeQuery = em.createNativeQuery(queryFrm);
			nativeQuery.executeUpdate();
			return true;
			
		}
		catch(Exception e) {
			LogErrores.escribirLog(e.getMessage(), queryFrm, ruta_log);
			return false;
		}
	}

}
