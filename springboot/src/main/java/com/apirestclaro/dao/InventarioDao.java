package com.apirestclaro.dao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.apirestclaro.entitys.DetalleInventario;
import com.apirestclaro.entitys.InfoProducto;
import com.serviciologerrores.functions.LogErrores;

@Repository
public class InventarioDao {
	
	@Value("${operador.ruta_log}")
	private String ruta_log;
	
	@Value("${operador.bd_pos}")
	private String bd_pos;
	
	@Value("${operador.bd_dis}")
	private String bd_dis;
	
	@Value("${operador.sim_id_start:0}")
	private String sim_id_start;
	
	@Value("${operador.coid}")
	private int coid;
	
	@Value("${operador.item_asignacion}")
	private int item_asignacion;
	
	@PersistenceContext
	private EntityManager em;
	private Query nativeQuery;
	
	public List<DetalleInventario> getDetalleInventario(int idPos,int idReferencia, int pagina){
		
		
		String query = "";
		try {
			int inicio = 0; 
			
			if(pagina >= 1) {
				inicio = inicio +(pagina * 20);
			}
			query = "SELECT id,serial from inventario__punto where id_pos = " + idPos + " and activo = 0 and id_referencia = " 
			+ idReferencia + " limit " + inicio + "," + 20;
			nativeQuery = em.createNativeQuery(query, DetalleInventario.class);
			List<DetalleInventario> detalle = new ArrayList<DetalleInventario>();
			detalle.addAll(nativeQuery.getResultList());
		    
			return detalle;
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getMessage(), query, ruta_log);
			return null;
		}
	}
	
	public Map<String,Object> getDetalleInfoProducto(String serial){
		
		String query = "";
		String coidQuery = "";
			
		Map<String, Object> response = new HashMap<String, Object>();
		try {

			// validar si hay un sim_id_start configurado y si el serial contiene el valor configurado para removerlo
			if (Integer.parseInt(sim_id_start) > 0 && sim_id_start.length() > 0 && serial.startsWith(sim_id_start)) {
				serial = serial.substring(sim_id_start.length());
			}
			
			if (coid > 0) {
				coidQuery = "IFNULL(sim.coid,'0') as coid, ";
			}
			else {
				coidQuery = "'0' as coid, ";
			}

			query = "SELECT sim.id,sim.iccid AS serial, ref.pn, ref.producto as referencia,IFNULL(sim.fecha_ac,'N/A') as fecha_ac," + 
					"IF(sim.paquete > 0, sim.paquete, 'Sin Paquete') AS paquete, sim.movil," +
					"sim.id_pos, sim.id_vendedor,sim.nro_pedido, IFNULL(sim.hora_ac,'N/A') as hora_ac, b.nombre_bodega, sim.id_combo, " + 
					"sim.archivo_actualiza, sim.archivo,"+ 
					"IF(sim.aceptado = 1, 'APROBADO', 'NO APROBADO') AS estado," + 
					"IF(sim.fecha_aceptacion IS NULL, 'N/A', sim.fecha_aceptacion) AS fecha_aceptacion," + 
					"IF(sim.hora_aceptacion IS NULL, 'N/A', sim.hora_aceptacion) AS hora_aceptacion," + 
					"IF(sim.id_sol_traslado > 0, sim.id_sol_traslado, IFNULL(sl_i.id_solicitud,0)) AS id_sol_traslado, " + 
					"sim.id_bodega, sim.id_bodega_distri, sim.fecha_asignacion, sim.hora_asignacion, sim.id_vendedor," + 
					"sim.distri, dis.nombre as distribuidor, dis.nombre_corto,sim.estado AS venta, IF(sim.fecha_venta IS NULL, 'N/A', sim.fecha_venta) AS fecha_venta," + 
					"IF(sim.hora_venta IS NULL, 'N/A', sim.hora_venta) AS hora_venta, sim.fecha_ingreso," + 
					"IFNULL(usd.nombre_usuario,'N/A') as vendedor," +
					"IFNULL(pos.razon,'N/A') as nombre_punto, " +
					coidQuery +
					"sim.activo " +
					"FROM simcards AS sim " + 
					"LEFT JOIN usuarios_distri as usd ON (usd.id_usuario = sim.id_vendedor and usd.id_distri = sim.distri) " +
					"LEFT JOIN " + bd_pos + ".puntos as pos ON (pos.idpos = sim.id_pos) " +
					"LEFT JOIN distribuidores as dis ON (sim.distri = dis.id) " +
					"LEFT JOIN bodega AS b ON (b.id = sim.id_bodega) " + 
					"LEFT JOIN referencias AS ref ON ((sim.id_referencia_origen > 0 AND ref.id = sim.id_referencia_origen) OR (sim.id_referencia_origen = 0 AND sim.id_referencia = ref.id)) " + 
					"LEFT JOIN solicitud_traslado_items AS sl_i ON (sl_i.serial = sim.iccid AND sl_i.id = (SELECT id FROM solicitud_traslado_items WHERE serial = sim.iccid ORDER BY id DESC LIMIT 1)) " + 
					"WHERE sim.iccid = '"+ serial + "'";
			
	
			nativeQuery = em.createNativeQuery(query, InfoProducto.class);
			InfoProducto info = (InfoProducto) nativeQuery.getSingleResult();
			if(info != null){
				
				Map<String, Object> infoProducto = new HashMap<String, Object>();
				if(Integer.parseInt(sim_id_start) > 0){
					infoProducto.put("serial", sim_id_start + info.getSerial());
				} else {
					infoProducto.put("serial", info.getSerial());
				}
				infoProducto.put("pn", info.getPn());
				infoProducto.put("referencia", info.getReferencia());
				infoProducto.put("paquete", info.getPaquete());
				infoProducto.put("movil", info.getMovil());
				
				/* DESPACHO */
				Map<String, Object> infoDistribucion = new HashMap<String, Object>();
				Map<String, Object> despacho = new HashMap<String, Object>();
				if(info.getArchivo() > 0 || info.getArchivo_actualiza() > 0) {
					int archivo = info.getArchivo_actualiza() > 0 ? info.getArchivo_actualiza() : info.getArchivo();
					Object[] resultDes = null;
					try{
						query = "SELECT nombre, fecha_subida, hora_subida FROM archivos WHERE id = " + archivo;
						nativeQuery = em.createNativeQuery(query);
						resultDes = (Object[])nativeQuery.getSingleResult();
						despacho.put("nombreArchivo",resultDes[0].toString());
						despacho.put("bodegaDestino", info.getNombreBodega());
						despacho.put("fechaDespacho",resultDes[1].toString());
						despacho.put("hora",resultDes[2].toString());					
					}
					catch(NoResultException e) {
						e.printStackTrace();
						LogErrores.escribirLog(e.getMessage(), query, ruta_log);
					}
				}else{
					despacho.put("nombreArchivo","N/A");
					despacho.put("fechaDespacho","N/A");
					despacho.put("hora","N/A");
					despacho.put("bodegaDestino","N/A");
				}
				infoDistribucion.put("despacho", despacho);	
				
				/* ACEPTACION */
				Map<String, Object> aceptacion = new HashMap<String, Object>();
				aceptacion.put("estado",info.getEstado());
				aceptacion.put("distribuidor",info.getDistribuidor());
				aceptacion.put("fechaAceptacion",info.getFecha_aceptacion());
				aceptacion.put("hora",info.getHora_aceptacion());
				infoDistribucion.put("aceptacion", aceptacion);
				
				String archivoActivacion = "N/A";
				if(coid > 0) {
					try{
						query = "SELECT IF(a.nombre IS NULL, 'N/A', a.nombre) AS archivo " + 
								"FROM csv_activaciones AS c_a " + 
								"INNER JOIN archivos AS a ON (a.id = c_a.archivo) " + 
								"WHERE c_a.coid = '" + info.getCoid() + "'";
						
						nativeQuery = em.createNativeQuery(query);
						Object result = nativeQuery.getSingleResult();
						archivoActivacion = result.toString();
						
					}
					catch(NoResultException e) {
						e.printStackTrace();
						LogErrores.escribirLog(e.getMessage(), query, ruta_log);
					}
				}
									
				Map<String, Object> activaciones = new HashMap<String, Object>();
				activaciones.put("nombreArchivo",archivoActivacion);
				activaciones.put("fecha",info.getFechaAc());
				activaciones.put("hora",info.getHoraAc());
				
				infoDistribucion.put("activaciones", activaciones);
				
				Map<String, Object> venta = new HashMap<String, Object>();
				venta.put("usuario",info.getVendedor());
				venta.put("idPdv", info.getIdPos());
				venta.put("puntoVenta",info.getNombrePunto());
				venta.put("usuarioAccion",info.getVendedor());
				venta.put("fechaVenta",info.getFecha_venta());
				venta.put("hora",info.getHora_venta());
				venta.put("detalle","N/A");
				
				
				Map<String, Object> movimientos = new HashMap<String, Object>();
				
				Map<String, Object> translados = new HashMap<String, Object>();
				
				Object[] resultTras = null;
				
				if(info.getId_sol_traslado() > 0) {
					try {
						query = "SELECT IFNULL(us.nombre_usuario,'N/A') as usuarioAccion, "
								+ "IFNULL(IF(sl.tipo_origen = 1,(SELECT nombre_bodega FROM " + bd_dis + info.getNombreCorto() + "_" + info.getDistri() + ".bodega WHERE id = sl.bodega_origen)"
								+ ",(SELECT nombre_bodega FROM bodega WHERE id = sl.bodega_origen)),'N/A') as bodega_origen, "
								+ "IFNULL(IF(sl.estado = 1,(SELECT nombre_bodega FROM bodega where id = sl.bodega_destino),'N/A'),'N/A') as bodega_destino, sl.fecha,"
								+ "IFNULL(sl.hora,'N/A'), IFNULL(sl.fecha_acepta,'N/A') AS traslado_fecha, IFNULL(sl.hora_acepta,'N/A') AS horatraslado,"
								+ "sl.usuario_acepta, sl.distri, sl.tipo_pro, sl.estado, sl.id "
								+ "FROM solicitud_traslado AS sl "
								+ "LEFT JOIN usuarios_distri as us ON (us.id_usuario = sl.usuario and us.id_distri = sl.distri) "
								+ "LEFT JOIN distribuidores AS dis ON (sl.distri = dis.id) "
								+ "WHERE sl.id = '" + info.getId_sol_traslado() + "'";
						
						//System.out.println(query);
						nativeQuery = em.createNativeQuery(query);
						resultTras = (Object[])nativeQuery.getSingleResult();
					}
					catch(NoResultException e) {
						
					}
				}
				
				if(resultTras != null) {
					translados.put("usuarioAccion",resultTras[0].toString());
					translados.put("bodegaOrigen",resultTras[1].toString());
					translados.put("bodegaDestino",resultTras[2].toString());					
					translados.put("fechaTranslado",resultTras[3].toString());
					translados.put("hora",resultTras[4].toString());				
				}
				else {
					translados.put("usuarioAccion","N/A");
					translados.put("bodegaOrigen","N/A");
					translados.put("bodegaDestino","N/A");					
					translados.put("fechaTranslado","N/A");
					translados.put("hora","N/A");
				}
				translados.put("detalle","N/A");	
				
				movimientos.put("translado", translados);
				movimientos.put("venta", venta);
				
				Map<String, Object> asignacion = new HashMap<String, Object>();;
				
				Object[] resultAsig = null;
				if (item_asignacion > 0) {
					try {
						query = "SELECT if(item.tipo_bodega = 1,(SELECT nombre_bodega FROM bodega WHERE id = item.id_bodega)"  
								+ ",(SELECT nombre_bodega FROM " + bd_dis  + info.getNombreCorto() + "_" + info.getDistri() + ".bodega WHERE id = item.id_bodega)) as bodegaOrigen,"  
								+ "us.nombre_usuario as usuario, usa.nombre_usuario as usuarioAccion,item.fecha as fechaAsignacion, item.hora,IFNULL(ped.observacion,'N/A') as detalle "  
								+ "FROM " + bd_dis  + info.getNombreCorto() + "_" + info.getDistri() + ".pedido__audi_item as item "  
								+ "LEFT JOIN " + bd_dis  + info.getNombreCorto() + "_" + info.getDistri() + ".pedido__audi as ped on (ped.id_pedido = item.id_pedido and accion = 1) "  
								+ "LEFT JOIN usuarios_distri as usa on (usa.id_usuario = ped.usuario and usa.id_distri = " + info.getDistri() + ") "  
								+ "LEFT JOIN usuarios_distri as us on (us.id_usuario = item.id_usuario and us.id_distri = " + info.getDistri() + ") " 
								+ "WHERE item.id_producto = " + info.getId();
								
								nativeQuery = em.createNativeQuery(query);
								resultAsig = (Object[])nativeQuery.getSingleResult();
					}
					catch(NoResultException e) {
						
					}
				}
				
			    if (resultAsig != null) {
			    	asignacion.put("bodegaOrigen", resultAsig[0].toString());
			    	asignacion.put("usuario", resultAsig[1].toString());
			    	asignacion.put("usuarioAccion", resultAsig[2].toString());
			    	asignacion.put("fechaAsignacion", resultAsig[3].toString());
			    	asignacion.put("hora", resultAsig[4].toString());
			    	asignacion.put("detalle", resultAsig[5].toString());
			    }
			    else {
			    	asignacion.put("bodegaOrigen", "N/A");
			    	asignacion.put("usuario", "N/A");
			    	asignacion.put("usuarioAccion", "N/A");
			    	asignacion.put("fechaAsignacion", "N/A");
			    	asignacion.put("hora", "N/A");
			    	asignacion.put("detalle", "N/A");
			    }
			    
			    movimientos.put("asignacion", asignacion);
			    
			    Map<String, Object> devoluciones = new HashMap<String, Object>();
			    
			    List<Object[]> devVentas = this.devoluciones(1, serial,  bd_dis  + info.getNombreCorto() + "_" + info.getDistri(),info.getId());
			    List<Object[]> devBodegasOrigen = this.devoluciones(2, serial,  bd_dis  + info.getNombreCorto() + "_" + info.getDistri(),info.getId());
			    List<Object[]> devBodegaDis = this.devoluciones(3, serial,  bd_dis  + info.getNombreCorto() + "_" + info.getDistri(),info.getId());
			    
			    List<Map<String,Object>> listVentas = new ArrayList<>();
			    List<Map<String,Object>> listDevBodegasOrigen = new ArrayList<>();
			    List<Map<String,Object>> listDevBodegasDis = new ArrayList<>();
			    
			    if(devVentas != null) {
			    	
			    	 Map<String, Object> ventas = new HashMap<String, Object>();
			    	 for (Object[] item : devVentas) {
			    		 
			    		 ventas.put("vendedor", item[0].toString());
			    		 ventas.put("usuarioAccion", item[1].toString());
			    		 ventas.put("precio", item[2].toString());
			    		 ventas.put("observacion", item[3].toString());
			    		 ventas.put("detalle", "N/A");
			    		 
			    		 listVentas.add(ventas);
			
					}
			    }
			    devoluciones.put("venta", listVentas);
			    
			    if(devBodegasOrigen != null) {
			    	
			    	 Map<String, Object> devolucionOrigen = new HashMap<String, Object>();
			    	 
			    	 for (Object[] item : devBodegasOrigen) {
			    		 
			    		 devolucionOrigen.put("bodegaOrigen", item[0].toString());
			    		 devolucionOrigen.put("usuarioAccion", item[1].toString());
			    		 devolucionOrigen.put("fechaDevolucion", item[2].toString());
			    		 devolucionOrigen.put("hora", item[3].toString());
			    		 devolucionOrigen.put("detalle", "N/A");
			    		 
			    		 listDevBodegasOrigen.add(devolucionOrigen);
			
					}
			    }
			    devoluciones.put("origen", listDevBodegasOrigen);
			    
			    
			    if(devBodegaDis != null) {
			    	
			    	 Map<String, Object> devolucionBodega = new HashMap<String, Object>();
			    	
			    	 for (Object[] item : devBodegaDis) {
			    		 
			    		 devolucionBodega.put("usuario", item[8].toString());
			    		 devolucionBodega.put("bodegaDestino", item[5].toString());
			    		 devolucionBodega.put("usuarioAccion", item[7].toString());
			    		 devolucionBodega.put("fechaDevolucion", item[1].toString());
			    		 devolucionBodega.put("hora", item[2].toString());
			    		 devolucionBodega.put("detalle", "N/A");
			    		 
			    		 listDevBodegasDis.add(devolucionBodega);
			
					}
			    }
			    devoluciones.put("bodega", listDevBodegasDis);

				response.put("infoProducto", infoProducto);
				response.put("infoDistribucion", infoDistribucion);
				response.put("devoluciones", devoluciones);
				response.put("movimientos", movimientos);
	
				return response;
			}
			else {
				/*response.put("estado", 0);
				response.put("msg", "Nro de serie no encontrado");*/
				return null;
			}
		}	
		catch(NoResultException e) {
			/*response.put("estado", 0);
			response.put("msg", "Nro de serie no encontrado");*/
			return null;
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LogErrores.escribirLog(ex.getMessage(), query, ruta_log);
			return null;
		}
	}
	
	public List<Object[]> devoluciones (int tipo, String serial, String bdDis, int idSim){
		
		try {
		
			String query = "";
			switch(tipo) {
			case 1: /// devoluciones de venta
				query = "SELECT CONCAT(u_v.nombre, ' ', u_v.apellido) AS vendedor," + 
				 		"CONCAT(u_u_a.nombre, ' ', u_u_a.apellido) AS usuario_accion," + 
				 		"i_d.valor AS precio, " + 
				 		"d_v.observacion " + 
				 		"FROM " + bdDis + ".item_devolucion AS i_d " + 
				 		"INNER JOIN " + bdDis + ".devolucion_venta AS d_v ON (d_v.id = i_d.id_devolucion) " + 
				 		"INNER JOIN " + bdDis + ".usuarios AS u_v ON (u_v.id = d_v.vendedor) " + 
				 		"INNER JOIN " + bdDis + ".usuarios AS u_u_a ON (u_u_a.id = d_v.usuario) " + 
				 		"WHERE i_d.serial = '" + serial + "' " + 
				 		"ORDER BY i_d.id DESC";
				
				 break;
			case 2: // devoluciones a bodega origen
			   query = "SELECT IF(b.nombre_bodega IS NULL, 'N/A', b.nombre_bodega) AS bodega,"
			   		+ "CONCAT(u.nombre, ' ', u.apellido) AS usuario_accion,"
			   		+ "a_t_p.fecha, a_t_p.hora "
			   		+ "FROM " + bdDis + ".audi_traslado_productos AS a_t_p "
			   		+ "INNER JOIN " + bdDis + ".usuarios AS u ON (u.id = a_t_p.usuario) "
			   		+ "LEFT JOIN " + bdDis + ".bodega AS b ON (b.id = a_t_p.id_bodega_origen) "
			   		+ "WHERE a_t_p.serie = '" + serial + "' AND a_t_p.id_bodega_destino = 0 "
			   		+ "ORDER BY a_t_p.id DESC";
			   break;
			case 3: // devoluciones a bodega distribuidor
				   query = "SELECT a_dp.tipo, "
				   		+ "a_dp.fecha, "
				   		+ "a_dp.hora, "
				   		+ "a_dp.usuario,"
				   		+ "a_dp.id_vendedor, "
				   		+ "a_dp.id_bodega, "
				   		+ "a_dp.tipo_bodega,"
				   		+ "CONCAT(u.nombre, ' ', u.apellido) AS usunombre,"
				   		+ "CONCAT(u_v.nombre, ' ', u_v.apellido) AS vendnombre "
				   		+ "FROM " + bdDis + ".audi_devolucion_producto AS a_dp "
				   		+ "LEFT JOIN " + bdDis + ".usuarios AS u ON (u.id = a_dp.usuario) "
				   		+ "LEFT JOIN " + bdDis + ".usuarios AS u_v ON (u_v.id = a_dp.id_vendedor) "
				   		+ "WHERE a_dp.id_producto = '" + idSim + "' AND a_dp.tipo = 1 "
				   		+ "ORDER BY a_dp.id DESC";
				   break;
			default:
			}
			
			 nativeQuery = em.createNativeQuery(query); 
			 List<Object[]> result = (List<Object[]>) nativeQuery.getResultList();
			 return result; 
			
		}
		catch(Exception e) {
			return null;
		}
	}
}
