package com.apirestclaro.dao;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.apirestclaro.entitys.DetalleInventario;
import com.apirestclaro.entitys.DetalleVisita;
import com.apirestclaro.pojos.Menu;
import com.apirestclaro.pojos.NoEfectiva;
import com.serviciologerrores.functions.LogErrores;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class VisitaDao {
	
	@Value("${operador.bd}")
	private String bd;
	
	@Value("${operador.bd_pos}")
	private String bd_pos;
	
	@Value("${operador.ruta_log}")
	private String ruta_log;
	
	@PersistenceContext
	private EntityManager em;
	
	private Query nativeQuery;
	
	
	@SuppressWarnings("unchecked")
	@Transactional
	public Map<String, Object> guardarVisita(Map<String, Object> datosVisita) {
		Map<String, Object> infoVisita =  new HashMap<String, Object>();
		String query = null;
		long idVisita = 0;
		Boolean visitaEnPunto = false;
		Boolean posicionSimulada = false;
		try {
		
			long idPos = Long.parseLong(datosVisita.get("idPdv").toString());
			String fechainicial = datosVisita.get("fechaInicial").toString();
			String horainicial = datosVisita.get("horaInicial").toString();
			String horaCierre = datosVisita.get("horaCierre").toString();
			double latitud = Double.parseDouble(datosVisita.get("latitud").toString()); 
			double longitud = Double.parseDouble(datosVisita.get("longitud").toString()); 
			long idCircuito = Long.parseLong(datosVisita.get("idCircuito").toString());
			long idRuta = Long.parseLong(datosVisita.get("idRuta").toString());
			long idRegional = Long.parseLong(datosVisita.get("idRegional").toString());
			long idDistribuidor = Long.parseLong(datosVisita.get("idDistribuidor").toString());
			long idUsuario = Long.parseLong(datosVisita.get("idUsuario").toString());
			Boolean efectiva = Boolean.parseBoolean(datosVisita.get("efectiva").toString());
			String idCheckMovil = datosVisita.get("idCheckMovil").toString();
			double precisionGps = Double.parseDouble(datosVisita.get("precisionGps").toString());
			double latitudPdv = Double.parseDouble(datosVisita.get("latitudPdv").toString()); 
			double longitudPdv = Double.parseDouble(datosVisita.get("longitudPdv").toString());
			long radioPrecisionGps = Long.parseLong(datosVisita.get("toleranciaVisita").toString());
			double precisionGpsPdv = Double.parseDouble(datosVisita.get("precisionGpsPdv").toString());
			double distancia = Double.parseDouble(datosVisita.get("distancia").toString());
			
			if(datosVisita.containsKey("visitaEnPunto")) {
				visitaEnPunto = Boolean.parseBoolean(datosVisita.get("visitaEnPunto").toString());
			}
			if(datosVisita.containsKey("posicionSimulada")) {
				posicionSimulada = Boolean.parseBoolean(datosVisita.get("posicionSimulada").toString());
			}
			nativeQuery = em.createNativeQuery("SELECT id FROM " + bd_pos + ".visitas_operador WHERE id_check_movil = '" + idCheckMovil + "'");
			List<Object[]> validarVisita = (List<Object[]>) nativeQuery.getResultList();

			if(!validarVisita.isEmpty()){
				infoVisita.put("estado", 0);
				infoVisita.put("mensaje", "Esta visita ya se encuentra registrada");

				return infoVisita;
			}

			query = String.format("INSERT INTO %s.visitas_operador (fecha, hora, hora_cierre, punto, usuario, territorio, zona, lat, lng, "
				+ "id_reg, distri, efectiva, id_check_movil, precision_gps, latitud_pdv, longitud_pdv, "
				+ "app, visita_en_punto, posicion_simulada, radio_precision_gps, precision_gps_pdv, distancia) "
				+ "VALUES ('%s', '%s', '%s', %d, %d, %d, %d, %f, %f, %d, %d, %b, '%s', %f, %f, %f, %d, %b, %b, %d, %f, %f)",
				bd_pos, fechainicial, horainicial, horaCierre, idPos, idUsuario, idCircuito, idRuta, latitud, longitud, 
				idRegional, idDistribuidor, efectiva, idCheckMovil, precisionGps, latitudPdv, longitudPdv,
				1, visitaEnPunto, posicionSimulada, radioPrecisionGps, precisionGpsPdv, distancia);
			
			nativeQuery = em.createNativeQuery(query);
			int insertVisita = nativeQuery.executeUpdate();

			nativeQuery = em.createNativeQuery("SELECT LAST_INSERT_ID();");
			BigInteger biid = (BigInteger) nativeQuery.getSingleResult();
			idVisita = biid.longValue();
			
			if(insertVisita > 0){
				infoVisita.put("estado", 1);
				infoVisita.put("mensaje", "Se guardó con éxito la visita");
				infoVisita.put("idVisita", idVisita);
			}else{
				infoVisita.put("estado", 0);
				infoVisita.put("mensaje", "Hubo un error al tratar de guardar los datos de la visita");
			}
			
			return infoVisita;
		
		} catch (Exception e2) {
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			return null;
		}
		
	}

	@Transactional
	public Map<String, Object> guardarVisitaNoEfectiva(Map<String, Object> datos, long idUsuario, long idPos, long idVisita){
		
		String query = null;
		String queryConcat = "";
		Map<String, Object> infoVisita =  new HashMap<String, Object>();

		try{

			String fecha = (String) datos.get("fechaDetalle");
			String hora = (String) datos.get("horaDetalle");
			int idMotivo = Integer.parseInt(datos.get("idMotivo").toString());
			//String observacion = (String) noEfectiva.getObservacion();

			/*for(NoEfectiva noEfectiva: datos){
				String fecha = (String) noEfectiva.getFechaDetalle();
				String hora = (String) noEfectiva.getHoraDetalle();
				String observacion = (String) noEfectiva.getObservacion();

				queryConcat += "('" + fecha + "', '" + hora + "', '" + observacion + "', 0, " + idVisita + "),";
			}*/

			/*if(!queryConcat.isEmpty()){

				queryConcat = queryConcat.substring(0, queryConcat.length()-1);*/

				query = "INSERT INTO " + bd_pos + ".visitas_detalle_operador (`fecha`, `hora`, id_motivo, `proceso`, `id_check`) VALUES ('" + fecha + "', '" + hora + "', " + idMotivo + ", 0, " + idVisita + ")";

				nativeQuery = em.createNativeQuery(query);
				int insertVisita = nativeQuery.executeUpdate();

				if(insertVisita > 0){
					infoVisita.put("estado", 1);
					infoVisita.put("mensaje", "Se guardó con éxito la visita detalle");
				}else{
					infoVisita.put("estado", 0);
					infoVisita.put("mensaje", "Hubo un error al tratar de guardar los datos de la visita detalle");
				}
			//}

			return infoVisita;

		}catch (Exception e2) {
			LogErrores.escribirLog(e2.getMessage(), query,ruta_log);
			return null;
		}
	}

	@Transactional
	public void reversarVisita(long idVisita){
		String query = null;
		try{
			query = "DELETE FROM " + bd_pos + ".visitas_operador WHERE id = " + idVisita;
			nativeQuery = em.createNativeQuery(query);
			nativeQuery.executeUpdate();
		}catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query,ruta_log);
		}
	}
	
	public Map<String, Object> detalleVisita(int idVisita){
		String query = null;
		try{			
			// el primer digito indica el tipo de la visita, esto para no realizar cambios en la estructura del servicio y tener que hacer release de la app
			// se debe cambiar este comportamiento cuando se vaya a hacer un nuevo release de la app
			Integer tipoVisita = Character.getNumericValue(Integer.toString(idVisita).charAt(0));
			idVisita = Integer.parseInt(Integer.toString(idVisita).substring(1));
			String tabla = (tipoVisita == 2) ? "visitas_operador" : "visitas__check";

			if(tipoVisita == 2){
				query = "SELECT vis.id,vis.fecha,UPPER(CONCAT(us.nombre,' ',us.apellido) COLLATE latin1_spanish_ci) as usuario, vis.hora,vis.hora_cierre,'N/A' as tipo_visita,"
						+ "vis.visita_en_punto,'No' as qr,'N/A' as observacion_qr,'N/A' as motivo_no_venta,'N/A' as observacion_no_venta, "
						+ "vis.lat, vis.lng, (vis.precision_gps + vis.radio_precision_gps) as precisiongps,"
						+ "vis.latitud_pdv as lat_pdv, vis.longitud_pdv as long_pdv,"
						+ "SEC_TO_TIME(TIMESTAMPDIFF(SECOND, vis.hora, vis.hora_cierre)) AS tiempo_visita, "
						+ "vis.precision_gps_pdv as precisiongps_punto "
						+ "FROM " + bd_pos + "." + tabla + " as vis "
						+ "LEFT JOIN usuarios as us ON (us.id = vis.usuario) "
						+ "WHERE vis.id = " + idVisita;

			} else {
				query = "SELECT vis.id,vis.fecha,UPPER(us.nombre_usuario) as usuario, vis.hora,vis.hora_cierre,'N/A' as tipo_visita,"
						+ "vis.visitaEnPunto as visita_en_punto,IF(vis.qr = 1,'Si','No') as qr,IFNULL(vis.observacion_qr,'N/A') as observacion_qr,'N/A' as motivo_no_venta,'N/A' as observacion_no_venta, "
						+ "vis.lat, vis.lng, (vis.precisiongps + vis.radio_precision_gps) as precisiongps,"
						+ "vis.lat_pdv, vis.long_pdv,"
						+ "SEC_TO_TIME(TIMESTAMPDIFF(SECOND, vis.hora, vis.hora_cierre)) AS tiempo_visita, "
						+ "vis.precision_gps_pdv as precisiongps_punto "
						+ "FROM " + bd_pos + "." + tabla + " as vis "
						+ "LEFT JOIN usuarios_distri as us on (us.id_usuario = vis.usuario and us.id_distri = vis.id_distri) "
						+ "WHERE vis.id = " + idVisita;
			}
			
			nativeQuery = em.createNativeQuery(query,DetalleVisita.class);
			
			DetalleVisita detalle = (DetalleVisita)nativeQuery.getSingleResult();
			
			Map<String,Object> datos = new HashMap<String, Object>();
		    Map<String,Object> infoDetalle = new HashMap<String, Object>();
		    
		    infoDetalle.put("fecha", detalle.getFecha());  
		    infoDetalle.put("usuario", detalle.getUsuario());    
		    infoDetalle.put("horaCheckIn", detalle.getHoraCheckIn());    
		    infoDetalle.put("horaCheckOut", detalle.getHoraCheckOut());    
		    infoDetalle.put("tipoVisita", detalle.getTipoVisita());    
		    infoDetalle.put("tiempoVisita", detalle.getTiempoVisita());    
		    infoDetalle.put("visitaEnPunto", detalle.getVisitaEnPunto());    
		    infoDetalle.put("marcacionQr", detalle.getMarcacionQr()); 
		    infoDetalle.put("observacionQr", detalle.getObservacionQr()); 
		    infoDetalle.put("motivoNoVenta", detalle.getMotivoNoVenta()); 
		    infoDetalle.put("observacionNoVenta", detalle.getObservacionNoVenta()); 
		    infoDetalle.put("latitudVendedor", detalle.getLatitudVendedor()); 
		    infoDetalle.put("longitudVendedor", detalle.getLongitudVendedor()); 
		    infoDetalle.put("presicionGpsVendedor", detalle.getPresicionGpsVendedor()); 
		    infoDetalle.put("latitudPunto", detalle.getLatitudPunto()); 
		    infoDetalle.put("longitudPunto", detalle.getLongitudPunto()); 
		    infoDetalle.put("presicionGpsPunto", detalle.getPresicionGpsPunto());
		    
		    datos.put("detalleVisita", infoDetalle);
		    
		    //// procesos de la visita
		    List<Map<String,Object>> procesos = new ArrayList<>();
		    List<Object[]> result;
		    
		    boolean venta = false;
		    try {
				String tablaDetalle = (tipoVisita == 2) ? "visitas_detalle_operador" : "visitas_detalle";
		    	String queryProcesos = "SELECT hora,"
			    		+ "CASE "
			    		+ "WHEN proceso = 0 THEN 'Visita' "
			    		+ "WHEN proceso = 1 THEN 'Toma de pedido'"
			    		+ "WHEN proceso = 2 THEN 'Entrega de pedido'"
			    		+ "WHEN proceso = 3 THEN 'Autoventa'"
			    		+ "WHEN proceso = 4 THEN 'Baja Manual'"
			    		+ "WHEN proceso = 6 THEN 'Venta de saldo'"
			    		+ "WHEN proceso = 7 THEN 'Encuesta' END as tipo_proceso "
						+ "from " + bd_pos + "." + tablaDetalle + " WHERE id_check = " + idVisita;
			    
			    nativeQuery = em.createNativeQuery(queryProcesos);
			    result = (List<Object[]>) nativeQuery.getResultList();
		    }
		    catch(NoResultException e) {
		    	result = new ArrayList<Object[]>();
		    }
		    if(!result.isEmpty()) {
		    	for (Object[] item : result) {
		    		
		    		if(item[1].toString().equals("Autoventa")) {
		    			venta = true;
		    		}
		    		Map<String,Object> proceso = new HashMap<String, Object>();
		    		proceso.put("hora", item[0].toString());
		    		proceso.put("proceso", item[1].toString());
		    		procesos.add(proceso);
	
				}
		    	datos.put("procesos", procesos);
		    }
		    
		    /// resumen de la compra
		    List<Map<String,Object>> ventas = new ArrayList<>();
		    if(venta){
		    	
		    	List<Object[]> resultVentas;
		    	try {
		    		String queryVentas = "SELECT ref.pn,ref.producto,count(inv.id) as cantidad,inv.id_pedido as numero_pedido "
			    			+ "from inventario__punto as inv "
			    			+ "inner join referencias as ref on (ref.id = inv.id_referencia) "
			    			+ "where visita = " + idVisita + " group by inv.id_referencia";
			    	 nativeQuery = em.createNativeQuery(queryVentas);
			    	 resultVentas = (List<Object[]>) nativeQuery.getResultList();
		    	}
		    	 catch(NoResultException e) {
		    		 resultVentas = new ArrayList<Object[]>();
				 }
		    	if(!resultVentas.isEmpty()) {
		    		for (Object[] item : resultVentas) {
			    		
			    		if(item[1].toString().equals("Autoventa")) {
			    			venta = true;
			    		}
			    		Map<String,Object> referencia = new HashMap<String, Object>();
			    		referencia.put("sku", item[0].toString());
			    		referencia.put("nombre", item[1].toString());
			    		referencia.put("tipoCompra", "SIMCARD");
			    		referencia.put("cantidad", Integer.parseInt(item[2].toString()));
			    		referencia.put("numeroPedido", item[3]);
			    		ventas.add(referencia);
					}
		    	}		
		    }
		    
		    datos.put("resumenCompra", ventas);
		    
		    return datos;
		    
			
		}catch(NoResultException e) {
			return null;
		}catch (Exception e) {
			e.printStackTrace();
			LogErrores.escribirLog(e.getMessage(), query,ruta_log);
			return null;
		}		
	}

}
