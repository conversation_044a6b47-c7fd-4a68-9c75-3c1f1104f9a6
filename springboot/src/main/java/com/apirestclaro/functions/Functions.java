package com.apirestclaro.functions;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

import com.apirestclaro.entitys.Usuario;


public class Functions {

	private static Usuario usuario;

	private static Map<String, Object> permisosDcs;

	private static String permisosRuta;
	
	public static Usuario getUsuario() {
		return usuario;
	}

	public static void setUsuario(final Usuario usuario) {
		Functions.usuario = usuario;
	}

	private static String secretKey = "MBOX2019-OPERATOR";

	public static String getSecretKey() {
		return secretKey;
	}
	
	public static boolean esNumero (final String numero) {
		try {
		   Integer.parseInt(numero);
		   return true;
		}
		catch(final NumberFormatException ex) {
			return false;
		}		
	}
	
	public static String fechaEspanol(final Date fecha) {
	  
		final SimpleDateFormat formateador = new SimpleDateFormat("dd 'de' MMMM 'de' yyyy", new Locale("es_ES"));	
		final String fechaEs = formateador.format(fecha);
		
		return fechaEs;
	
	}
	
	public static String diaSemana () {
		String diaLetras = ""; 
		 final Calendar now = Calendar.getInstance();
		 final int dia = now.get(Calendar.DAY_OF_WEEK);
		 
		 if(dia==Calendar.SUNDAY){
			 diaLetras = "Domingo";
		 }if(dia==Calendar.MONDAY){
			 diaLetras = "Lunes";
		 }
		 if(dia==Calendar.TUESDAY){
			 diaLetras = "Martes";
		 }
		 if(dia==Calendar.WEDNESDAY){
			 diaLetras = "Miercoles";
		 }
		 if(dia==Calendar.WEDNESDAY){
			 diaLetras = "Miercoles";
		 }
		 if(dia==Calendar.THURSDAY){
			 diaLetras = "Jueves";
		 }
		 if(dia==Calendar.FRIDAY){
			 diaLetras = "Viernes";
		 }
		 if(dia==Calendar.SATURDAY){
			 diaLetras = "Sabado";
		 }
		 return diaLetras;
	}

	/*public static String getToken(Usuario us) {

		try {
			List grantedAuthorities = AuthorityUtils.commaSeparatedStringToAuthorityList(us.getPerfil());

			Claims claims = Jwts.claims();
			claims.put("authorities", new ObjectMapper().writeValueAsString(grantedAuthorities));

			String token = Jwts.builder().setClaims(claims).setId(us.getId().toString()).setSubject(us.getUsername())
					.setIssuedAt(new Date(System.currentTimeMillis()))
					.setExpiration(new Date(System.currentTimeMillis() + 600000))
					.signWith(SignatureAlgorithm.HS256, secretKey.getBytes()).compact();

			return token;

		} catch (Exception e) {
			return null;
		}
	}*/

	public static String getMD5(final String data) throws NoSuchAlgorithmException {
		final MessageDigest messageDigest = MessageDigest.getInstance("MD5");

		messageDigest.update(data.getBytes());
		final byte[] digest = messageDigest.digest();
		final StringBuffer sb = new StringBuffer();
		for (final byte b : digest) {
			sb.append(Integer.toHexString((int) (b & 0xff)));
		}
		return sb.toString();
	}

	public static void setPermisosDcs(Map<String, Object> permisosDcs){
		Functions.permisosDcs = permisosDcs;
	}

	public static Map<String, Object> getPermisosDcs() {
		return permisosDcs;
	}

	public static void setPermisosRuta(String permisosDcs){
		Functions.permisosRuta = permisosDcs;
	}

	public static String getPermisosRuta() {
		return permisosRuta;
	}

}
