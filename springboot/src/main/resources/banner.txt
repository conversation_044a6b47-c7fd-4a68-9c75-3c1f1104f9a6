
${Ansi.GREEN}        __     __  __            _ _ _               
${Ansi.GREEN}        \ \   |  \/  |          (_) | |              
${Ansi.GREEN}    _____\ \  | \  / | _____   ___| | |__   _____  __
${Ansi.GREEN}   |______> > | |\/| |/ _ \ \ / / | | '_ \ / _ \ \/ /
${Ansi.GREEN}         / /  | |  | | (_) \ V /| | | |_) | (_) >  < 
${Ansi.GREEN}        /_/   |_|  |_|\___/ \_/ |_|_|_.__/ \___/_/\_\
${Ansi.BLUE} == Spring Boot${spring-boot.formatted-version} == ${Ansi.DEFAULT}                                                     
                                                    