#debug=true
#server.address=*************
#server.port=2020

spring.application.name=servicio-operador-movistar-uruguay-v3_3_4
spring.jmx.default-domain="app_operador_movistar_uruguay_v3_3_4"

#Produccion
spring.datasource.url=***********************************************************************************************************
spring.datasource.username=uruguayseguro
spring.datasource.password=hhjW26Jilv(

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL55Dialect

operador.ur=https://uruguay.movilbox.net/operador/
operador.bd=movistar_uruguay
operador.bd_pos=pos_distrimovistar_uruguay
operador.bd_dis=distriu_
operador.ruta_log=/var/www/html/movistar_uruguay/errores_app_operador/
operador.sim_id_start=89
operador.coid=0
operador.item_asignacion=0

aws.accessKeyId=********************
aws.secretKey=PAm/lbgX763wLKyGIP962k35QvN7uIuISWCfNWka

# Region de AWS
aws.region=us-east-1

# Nombre del bucket de AWS
aws.bucket=movilbox-s3
aws.folder=movistar_uruguay/operador/
#aws.bucket=https://movilbox-test.s3.amazonaws.com/tat/movistar_uruguay/operador/encuestas/

operador.aws_bucket=https://movilbox-s3.s3.amazonaws.com/
